import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum RecipeStatus {
  draft = "draft",
  publish = "publish",
  archived = "archived",
  deleted = "deleted",
}

export enum RecipeServingMethod {
  individual_plates = "individual_plates",
  family_style = "family_style",
  buffet_style = "buffet_style",
  shared_platters = "shared_platters",
  tasting_portions = "tasting_portions",
  cocktail_style = "cocktail_style",
  picnic_style = "picnic_style",
}

export enum RecipeYieldUnit {
  servings = "servings",
  portions = "portions",
  cups = "cups",
  liters = "liters",
  pieces = "pieces",
  slices = "slices",
  bowls = "bowls",
  plates = "plates",
}

export enum RecipeServeIn {
  dinner_plates = "dinner_plates",
  salad_plates = "salad_plates",
  bowls = "bowls",
  soup_bowls = "soup_bowls",
  ramekins = "ramekins",
  glasses = "glasses",
  mugs = "mugs",
  platters = "platters",
  serving_dishes = "serving_dishes",
}

export enum RecipeComplexityLevel {
  low = "low",
  medium = "medium",
  hard = "hard",
}

interface RecipeAttributes {
  id?: number;
  recipe_title: string;
  recipe_public_title?: string;
  recipe_description?: string;
  recipe_preparation_time?: number;
  recipe_cook_time?: number;
  has_recipe_public_visibility: boolean;
  has_recipe_private_visibility: boolean;
  recipe_status: RecipeStatus;
  recipe_serve_in?: RecipeServeIn;
  recipe_complexity_level?: RecipeComplexityLevel;
  recipe_garnish?: string;
  recipe_head_chef_tips?: string;
  recipe_foh_tips?: string;
  recipe_impression?: number;
  recipe_yield?: number;
  recipe_yield_unit?: RecipeYieldUnit;
  recipe_total_portions?: number;
  recipe_single_portion_size?: number;
  recipe_serving_method?: RecipeServingMethod;
  organization_id?: string;
  recipe_placeholder?: number;
  ingredient_costs_updated_at?: Date;
  nutrition_values_updated_at?: Date;
  vitamin_a?: number;
  vitamin_c?: number;
  calcium?: number;
  iron?: number;
  is_ingredient_cooking_method?: boolean;
  is_preparation_method?: boolean;
  created_by: number;
  updated_by: number;
  created_at?: Date;
  updated_at?: Date;
  recipe_slug: string;
}

export class Recipe
  extends Model<RecipeAttributes, never>
  implements RecipeAttributes {
  id!: number;
  recipe_title!: string;
  recipe_public_title?: string;
  recipe_description?: string;
  recipe_preparation_time?: number;
  recipe_cook_time?: number;
  has_recipe_public_visibility!: boolean;
  has_recipe_private_visibility!: boolean;
  recipe_status!: RecipeStatus;
  recipe_serve_in?: RecipeServeIn;
  recipe_complexity_level?: RecipeComplexityLevel;
  recipe_garnish?: string;
  recipe_head_chef_tips?: string;
  recipe_foh_tips?: string;
  recipe_impression?: number;
  recipe_yield?: number;
  recipe_yield_unit?: RecipeYieldUnit;
  recipe_total_portions?: number;
  recipe_single_portion_size?: number;
  recipe_serving_method?: RecipeServingMethod;
  organization_id?: string;
  ingredient_costs_updated_at?: Date;
  nutrition_values_updated_at?: Date;
  vitamin_a?: number;
  vitamin_c?: number;
  calcium?: number;
  iron?: number;
  is_ingredient_cooking_method?: boolean;
  is_preparation_method?: boolean;
  recipe_placeholder?: number;
  created_by!: number;
  updated_by!: number;
  created_at!: Date;
  updated_at!: Date;
  recipe_slug!: string;
}

Recipe.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    recipe_title: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    recipe_public_title: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    recipe_description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    recipe_preparation_time: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    recipe_cook_time: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    has_recipe_public_visibility: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    has_recipe_private_visibility: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    recipe_status: {
      type: DataTypes.ENUM(Object.values(RecipeStatus)),
      allowNull: false,
      defaultValue: RecipeStatus.draft,
    },
    recipe_serve_in: {
      type: DataTypes.ENUM(Object.values(RecipeServeIn)),
      allowNull: true,
    },
    recipe_complexity_level: {
      type: DataTypes.ENUM(Object.values(RecipeComplexityLevel)),
      allowNull: true,
    },
    recipe_garnish: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    recipe_head_chef_tips: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    recipe_foh_tips: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    recipe_impression: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    recipe_yield: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    recipe_yield_unit: {
      type: DataTypes.ENUM(Object.values(RecipeYieldUnit)),
      allowNull: true,
    },
    recipe_total_portions: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    recipe_single_portion_size: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    recipe_serving_method: {
      type: DataTypes.ENUM(Object.values(RecipeServingMethod)),
      allowNull: true,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    recipe_placeholder: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "nv_items",
        key: "id",
      },
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    recipe_slug: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    ingredient_costs_updated_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "Timestamp when ingredient costs were last calculated for this recipe",
    },
    nutrition_values_updated_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "Timestamp when nutrition values were last calculated for this recipe",
    },
    vitamin_a: {
      type: DataTypes.FLOAT,
      allowNull: true,
      comment: "Vitamin A content in the recipe",
    },
    vitamin_c: {
      type: DataTypes.FLOAT,
      allowNull: true,
      comment: "Vitamin C content in the recipe",
    },
    calcium: {
      type: DataTypes.FLOAT,
      allowNull: true,
      comment: "Calcium content in the recipe",
    },
    iron: {
      type: DataTypes.FLOAT,
      allowNull: true,
      comment: "Iron content in the recipe",
    },
    is_ingredient_cooking_method: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
      comment: "Whether cooking method is applied to ingredients",
    },
    is_preparation_method: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
      comment: "Whether preparation method is applied to ingredients",
    },
  },
  {
    sequelize,
    tableName: "mo_recipe",
    modelName: "Recipe",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        unique: true,
        fields: ["recipe_slug", "organization_id"],
        name: "unique_recipe_slug_per_org",
      },
      {
        fields: ["organization_id"],
        name: "idx_recipe_organization",
      },
      {
        fields: ["recipe_status"],
        name: "idx_recipe_status",
      },
      {
        fields: ["created_by"],
        name: "idx_recipe_created_by",
      },
      {
        fields: ["updated_by"],
        name: "idx_recipe_updated_by",
      },
    ],
  },
);

// Define associations
Recipe.associate = (models: any) => {
  // Recipe belongs to User (created_by)
  Recipe.belongsTo(models.User, {
    foreignKey: "created_by",
    as: "creator",
  });

  // Recipe belongs to User (updated_by)
  Recipe.belongsTo(models.User, {
    foreignKey: "updated_by",
    as: "updater",
  });

  // Recipe belongs to Item (recipe_placeholder)
  Recipe.belongsTo(models.Item, {
    foreignKey: "recipe_placeholder",
    as: "placeholderItem",
    constraints: true,
    onDelete: "SET NULL",
    onUpdate: "CASCADE",
  });

  // Many-to-many association with Category through RecipeCategory
  Recipe.belongsToMany(models.Category, {
    through: models.RecipeCategory,
    foreignKey: "recipe_id",
    otherKey: "category_id",
    as: "categories",
  });

  // Many-to-many association with FoodAttributes through RecipeAttributes
  Recipe.belongsToMany(models.FoodAttributes, {
    through: models.RecipeAttributes,
    foreignKey: "recipe_id",
    otherKey: "attributes_id",
    as: "attributes",
  });

  // Many-to-many association with Ingredient through RecipeIngredients
  Recipe.belongsToMany(models.Ingredient, {
    through: models.RecipeIngredients,
    foreignKey: "recipe_id",
    otherKey: "ingredient_id",
    as: "ingredients",
  });

  // Many-to-many association with User through RecipeUser (bookmarks)
  Recipe.belongsToMany(models.User, {
    through: models.RecipeUser,
    foreignKey: "recipe_id",
    otherKey: "user_id",
    as: "bookmarkedByUsers",
  });

  // One-to-many association with RecipeSteps
  Recipe.hasMany(models.RecipeSteps, {
    foreignKey: "recipe_id",
    as: "steps",
  });

  // One-to-many association with RecipeResources
  Recipe.hasMany(models.RecipeResources, {
    foreignKey: "recipe_id",
    as: "resources",
  });

  // One-to-many association with RecipeHistory
  Recipe.hasMany(models.RecipeHistory, {
    foreignKey: "recipe_id",
    as: "history",
  });
};

export default Recipe;
