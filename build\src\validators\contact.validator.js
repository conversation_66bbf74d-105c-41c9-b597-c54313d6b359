"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const celebrate_1 = require("celebrate");
const createContactUsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object().keys({
        name: celebrate_1.Joi.string().max(100).required(),
        email: celebrate_1.Joi.string().email().max(255).required(),
        mobile: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.string().max(20), celebrate_1.Joi.string().allow(null, ""))
            .optional(),
        message: celebrate_1.Joi.string().max(1000).required(),
        subject: celebrate_1.Joi.string().max(200).optional(),
        recipe_id: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().integer().min(1), celebrate_1.Joi.string()
            .pattern(/^\d+$/)
            .custom((value) => parseInt(value)))
            .allow(null)
            .optional(),
    }),
});
const updateContactUsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object().keys({
        name: celebrate_1.Joi.string().max(100).optional(),
        email: celebrate_1.Joi.string().email().max(255).optional(),
        mobile: celebrate_1.Joi.string().max(20).allow(null, "").optional(),
        message: celebrate_1.Joi.string().optional(),
        recipe_id: celebrate_1.Joi.number().integer().allow(null).optional(),
    }),
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
});
const deleteContactUsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
});
const getContactUsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
});
const getContactUsListValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: celebrate_1.Joi.object().keys({
        page: celebrate_1.Joi.number().integer().min(1).default(1),
        limit: celebrate_1.Joi.number().integer().min(1).max(100).default(10),
        search: celebrate_1.Joi.string().max(100).optional(),
        start_date: celebrate_1.Joi.date().iso().optional(),
        end_date: celebrate_1.Joi.date().iso().optional(),
        sort_by: celebrate_1.Joi.string()
            .valid("name", "email", "created_at", "updated_at")
            .default("created_at"),
        sort_order: celebrate_1.Joi.string().valid("asc", "desc").default("desc"),
    }),
});
exports.default = {
    createContactUsValidator,
    updateContactUsValidator,
    deleteContactUsValidator,
    getContactUsValidator,
    getContactUsListValidator,
};
