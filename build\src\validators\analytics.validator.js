"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const celebrate_1 = require("celebrate");
// ============================================================================
// SIMPLIFIED ANALYTICS VALIDATORS - Only what's needed per feature document
// ============================================================================
// Valid CTA types from your requirements
const ctaTypes = [
    "contact_info", // Contact Info block
    "contact_form", // Contact Us form
    "custom_cta", // Custom CTA link
];
// Valid date range options for dashboard
const dateRangeOptions = [
    "today",
    "this_week",
    "this_month",
    "last_7_days",
    "last_month",
    "last_30_days",
    "last_90_days",
    "last_year",
    "custom", // For custom date range
];
// ============================================================================
// PUBLIC ANALYTICS VALIDATORS - Matching your feature requirements
// ============================================================================
/**
 * Validator for tracking recipe interactions on public recipes
 * Supports: recipe_view, recipe_bookmark, recipe_share
 * Essential for dashboard analytics requirements
 */
const trackRecipeViewValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: {
        recipe_id: celebrate_1.Joi.number().required(),
        organization_id: celebrate_1.Joi.string().optional(),
        recipe_name: celebrate_1.Joi.string().optional(),
        event_type: celebrate_1.Joi.string()
            .valid("recipe_view", "recipe_bookmark", "recipe_share")
            .default("recipe_view")
            .optional(),
        // For recipe_view events
        referrer: celebrate_1.Joi.string().optional(),
        // For recipe_share events
        share_platform: celebrate_1.Joi.string()
            .valid("facebook", "twitter", "whatsapp", "email", "copy_link")
            .optional(),
    },
});
/**
 * Validator for tracking CTA clicks on public recipes
 * Tracks clicks on Contact Info, Contact Form, Custom CTA buttons
 */
const trackCtaClickValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: {
        recipe_id: celebrate_1.Joi.number().required(),
        organization_id: celebrate_1.Joi.string().optional(),
        recipe_name: celebrate_1.Joi.string().optional(),
        cta_type: celebrate_1.Joi.string()
            .valid(...ctaTypes)
            .required(),
        cta_text: celebrate_1.Joi.string().optional(),
    },
});
/**
 * Validator for contact form submissions from public recipes
 * Stores contact form data when users submit from public recipe pages
 */
const submitContactFormValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: {
        recipe_id: celebrate_1.Joi.number().required(),
        organization_id: celebrate_1.Joi.string().optional(),
        recipe_name: celebrate_1.Joi.string().optional(),
        name: celebrate_1.Joi.string().required(),
        email: celebrate_1.Joi.string().email().required(),
        mobile: celebrate_1.Joi.string().optional(),
        message: celebrate_1.Joi.string().required(),
    },
});
// ============================================================================
// DASHBOARD ANALYTICS VALIDATORS - For your 50/50 dashboard layout
// ============================================================================
/**
 * Validator for getting CTA click analytics with pagination (no default values)
 * Shows: Recipe Name, CTA Type, Clicks, Last Clicked At
 */
const getCtaClickAnalyticsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: {
        date_range: celebrate_1.Joi.string()
            .valid(...dateRangeOptions)
            .optional(),
        start_date: celebrate_1.Joi.when("date_range", {
            is: "custom",
            then: celebrate_1.Joi.date().iso().required(),
            otherwise: celebrate_1.Joi.date().iso().optional(),
        }),
        end_date: celebrate_1.Joi.when("date_range", {
            is: "custom",
            then: celebrate_1.Joi.date().iso().min(celebrate_1.Joi.ref("start_date")).required(),
            otherwise: celebrate_1.Joi.date().iso().optional(),
        }),
        sort: celebrate_1.Joi.string().valid("asc", "desc").optional(),
        search: celebrate_1.Joi.string()
            .optional()
            .description("General search term for recipe name or CTA type"),
        recipe_name: celebrate_1.Joi.string().optional(), // Search filter
        cta_type: celebrate_1.Joi.string()
            .valid(...ctaTypes)
            .optional()
            .description("Filter by CTA type"),
        page: celebrate_1.Joi.number().min(1).optional(),
        limit: celebrate_1.Joi.number().min(1).max(100).optional(),
    },
});
/**
 * Validator for getting contact form submissions with pagination (no default values)
 * Shows: Recipe Name, Name, Email, Mobile, Message, Submitted On
 */
const getContactSubmissionsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: {
        date_range: celebrate_1.Joi.string()
            .valid(...dateRangeOptions)
            .optional(),
        start_date: celebrate_1.Joi.when("date_range", {
            is: "custom",
            then: celebrate_1.Joi.date().iso().required(),
            otherwise: celebrate_1.Joi.date().iso().optional(),
        }),
        end_date: celebrate_1.Joi.when("date_range", {
            is: "custom",
            then: celebrate_1.Joi.date().iso().min(celebrate_1.Joi.ref("start_date")).required(),
            otherwise: celebrate_1.Joi.date().iso().optional(),
        }),
        search: celebrate_1.Joi.string()
            .optional()
            .description("General search term for recipe name, user name, or email"),
        recipe_id: celebrate_1.Joi.number().optional(), // Filter by recipe
        recipe_name: celebrate_1.Joi.string()
            .optional()
            .description("Filter by recipe name (partial match)"),
        user_email: celebrate_1.Joi.string()
            .email()
            .optional()
            .description("Filter by user email (partial match)"),
        page: celebrate_1.Joi.number().min(1).optional(),
        limit: celebrate_1.Joi.number().min(1).max(100).optional(),
    },
});
/**
 * Validator for exporting contact form submissions
 * Supports CSV and JSON export formats
 */
const exportContactSubmissionsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: {
        format: celebrate_1.Joi.string().valid("csv", "json").default("csv").optional(),
        date_range: celebrate_1.Joi.string()
            .valid(...dateRangeOptions)
            .optional(),
        start_date: celebrate_1.Joi.when("date_range", {
            is: "custom",
            then: celebrate_1.Joi.date().iso().required(),
            otherwise: celebrate_1.Joi.date().iso().optional(),
        }),
        end_date: celebrate_1.Joi.when("date_range", {
            is: "custom",
            then: celebrate_1.Joi.date().iso().min(celebrate_1.Joi.ref("start_date")).required(),
            otherwise: celebrate_1.Joi.date().iso().optional(),
        }),
        search: celebrate_1.Joi.string()
            .optional()
            .description("General search term for recipe name, user name, or email"),
        recipe_name: celebrate_1.Joi.string()
            .optional()
            .description("Filter by recipe name (partial match)"),
        user_email: celebrate_1.Joi.string()
            .email()
            .optional()
            .description("Filter by user email (partial match)"),
    },
});
/**
 * Validator for deleting contact form submissions
 * Admin can delete individual submissions
 */
const deleteContactSubmissionValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: {
        id: celebrate_1.Joi.number().required(),
    },
});
/**
 * Validator for analytics summary endpoint
 * Shows paginated analytics events with filtering options
 */
const getAnalyticsSummaryValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: {
        page: celebrate_1.Joi.number().min(1).default(1),
        limit: celebrate_1.Joi.number().min(1).max(50).default(20),
        event_type: celebrate_1.Joi.string()
            .valid("recipe_view", "cta_click", "contact_form_submit")
            .optional(),
        entity_type: celebrate_1.Joi.string()
            .valid("recipe", "category", "ingredient")
            .optional(),
        entity_id: celebrate_1.Joi.number().optional(),
        date_range: celebrate_1.Joi.string()
            .valid(...dateRangeOptions)
            .default("last_30_days")
            .optional(),
        start_date: celebrate_1.Joi.when("date_range", {
            is: "custom",
            then: celebrate_1.Joi.date().iso().required(),
            otherwise: celebrate_1.Joi.date().iso().optional(),
        }),
        end_date: celebrate_1.Joi.when("date_range", {
            is: "custom",
            then: celebrate_1.Joi.date().iso().min(celebrate_1.Joi.ref("start_date")).required(),
            otherwise: celebrate_1.Joi.date().iso().optional(),
        }),
    },
});
/**
 * Validator for recipe view analytics endpoint
 * Shows recipe view statistics with date filtering
 */
const getRecipeViewAnalyticsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: {
        date_range: celebrate_1.Joi.string()
            .valid(...dateRangeOptions)
            .default("last_30_days"),
        start_date: celebrate_1.Joi.when("date_range", {
            is: "custom",
            then: celebrate_1.Joi.date().iso().required(),
            otherwise: celebrate_1.Joi.date().iso().optional(),
        }),
        end_date: celebrate_1.Joi.when("date_range", {
            is: "custom",
            then: celebrate_1.Joi.date().iso().min(celebrate_1.Joi.ref("start_date")).required(),
            otherwise: celebrate_1.Joi.date().iso().optional(),
        }),
        sort: celebrate_1.Joi.string().valid("asc", "desc").default("desc"),
    },
});
/**
 * Validator for getting recipe view statistics for private recipes
 * Validates recipe ID parameter
 */
const getRecipeViewStatisticsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: {
        recipeId: celebrate_1.Joi.number().required().positive(),
    },
    [celebrate_1.Segments.QUERY]: {
        organization_id: celebrate_1.Joi.string().optional(),
    },
});
/**
 * Validator for resetting recipe view statistics for private recipes
 * Validates recipe ID parameter and optional user_ids array in body
 */
const resetRecipeViewStatisticsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: {
        recipeId: celebrate_1.Joi.number().required().positive(),
    },
    [celebrate_1.Segments.BODY]: {
        user_ids: celebrate_1.Joi.array()
            .items(celebrate_1.Joi.number().positive())
            .min(1)
            .optional()
            .messages({
            "array.min": "user_ids array must contain at least one user ID when provided",
            "number.positive": "All user IDs must be positive numbers",
        }),
    },
    [celebrate_1.Segments.QUERY]: {
        organization_id: celebrate_1.Joi.string().optional(),
    },
});
exports.default = {
    // Public tracking validators
    trackRecipeViewValidator,
    trackCtaClickValidator,
    submitContactFormValidator,
    // Dashboard analytics validators
    getCtaClickAnalyticsValidator,
    getContactSubmissionsValidator,
    exportContactSubmissionsValidator,
    deleteContactSubmissionValidator,
    getAnalyticsSummaryValidator,
    getRecipeViewAnalyticsValidator,
    // Recipe view statistics validators
    getRecipeViewStatisticsValidator,
    resetRecipeViewStatisticsValidator,
};
