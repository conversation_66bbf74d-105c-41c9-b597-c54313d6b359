import Analytics, {
  AnalyticsEventType,
  AnalyticsEntityType,
} from "../models/Analytics";
import { Op, QueryTypes } from "sequelize";
import { sequelize } from "../models/index";

interface AnalyticsQuery {
  eventType?: AnalyticsEventType;
  entityType?: AnalyticsEntityType;
  entityId?: number;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

interface DashboardStats {
  // Core Business Metrics
  totalRecipes: number;
  activeUsers: number;
  topCategory: {
    name: string;
    count: number;
  };
  highestImpressionRecipe: {
    name: string;
    impressions: number;
  };

  // Recipe Analytics
  totalViews: number;
  totalContactSubmissions: number;

  // Dashboard Charts Data
  recipeViewsTrend: any[]; // Top 10 recipes with views for last 30 days
  categoryPerformance: any[]; // Bar chart data
  userEngagementHeatmap: any[]; // Heatmap data for user engagement
  conversionFunnel: any[]; // Conversion analytics data
  recentActivity: any[];
}

class AnalyticsService {
  /**
   * Track any event with flexible metadata - Supports organization filtering
   */
  async trackEvent(data: {
    eventType: AnalyticsEventType;
    entityType: AnalyticsEntityType;
    entityId?: number;
    organizationId?: string;
    userId?: number;
    ipAddress?: string;
    userAgent?: string;
    metadata?: any;
  }): Promise<Analytics> {
    // Remove referrer from metadata if present (for public APIs)
    const cleanMetadata = { ...data.metadata };
    delete cleanMetadata.referrer;

    return await Analytics.trackEvent({
      event_type: data.eventType,
      entity_type: data.entityType,
      entity_id: data.entityId,
      organization_id: data.organizationId,
      user_id: data.userId,
      ip_address: data.ipAddress,
      user_agent: data.userAgent,
      metadata: cleanMetadata,
    });
  }

  /**
   * Get analytics data with flexible filtering - Public API
   */
  async getAnalytics(query: AnalyticsQuery): Promise<Analytics[]> {
    const whereClause: any = {};

    if (query.eventType) whereClause.event_type = query.eventType;
    if (query.entityType) whereClause.entity_type = query.entityType;
    if (query.entityId) whereClause.entity_id = query.entityId;

    if (query.startDate && query.endDate) {
      whereClause.created_at = {
        [Op.between]: [query.startDate, query.endDate],
      };
    }

    return await Analytics.findAll({
      where: whereClause,
      limit: query.limit || 100,
      offset: query.offset || 0,
      order: [["created_at", "DESC"]],
    });
  }

  /**
   * Get dashboard statistics - Organization-based and optimized
   */
  async getDashboardStats(
    organizationId: string | null | undefined,
    dateRange: string = "last_30_days"
  ): Promise<DashboardStats> {
    try {
      const { startDate, endDate } = this.getDateRange(dateRange);

      // Get all counts in parallel with organization-filtered queries
      const [
        recipeCount,
        activeUsersCount,
        topCategoryData,
        totalViews,
        contactCount,
        recipeViewsTrend,
        categoryPerformance,
        userEngagementHeatmap,
        conversionFunnel,
        recentActivity,
        highestImpressionRecipe,
      ] = await Promise.all([
        this.getRecipeCount(organizationId),
        this.getActiveUsersCount(organizationId, startDate, endDate),
        this.getTopCategory(organizationId, startDate, endDate),
        this.getTotalRecipeViews(organizationId, startDate, endDate),
        this.getContactSubmissions(organizationId, startDate, endDate),
        this.getRecipeViewsTrend(organizationId, startDate, endDate),
        this.getCategoryPerformance(organizationId, startDate, endDate),
        this.getUserEngagementHeatmap(organizationId, startDate, endDate),
        this.getConversionFunnel(organizationId, startDate, endDate),
        this.getRecentActivity(organizationId, 10),
        this.getHighestImpressionRecipe(organizationId),
      ]);

      return {
        // Core Business Metrics
        totalRecipes: recipeCount,
        activeUsers: activeUsersCount,
        topCategory: topCategoryData,
        highestImpressionRecipe: highestImpressionRecipe,

        // Recipe Analytics
        totalViews: totalViews,
        totalContactSubmissions: contactCount,

        // Dashboard Charts Data
        recipeViewsTrend: recipeViewsTrend,
        categoryPerformance: categoryPerformance,
        userEngagementHeatmap: userEngagementHeatmap,
        conversionFunnel: conversionFunnel,
        recentActivity: recentActivity,
      };
    } catch (error) {
      console.error("Dashboard stats error:", error);
      return {
        totalRecipes: 0,
        activeUsers: 0,
        topCategory: { name: "No Data", count: 0 },
        highestImpressionRecipe: { name: "No Data", impressions: 0 },
        totalViews: 0,
        totalContactSubmissions: 0,
        recipeViewsTrend: [],
        categoryPerformance: [],
        userEngagementHeatmap: [],
        conversionFunnel: [],
        recentActivity: [],
      };
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS - Organization-filtered and optimized
  // ============================================================================

  /**
   * Get total recipe count - Organization filtered
   */
  private async getRecipeCount(
    organizationId?: string | null
  ): Promise<number> {
    try {
      const whereClause = organizationId
        ? `WHERE recipe_status != 'deleted' AND organization_id = :organizationId`
        : `WHERE recipe_status != 'deleted'`;

      const result = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_recipe ${whereClause}`,
        {
          replacements: { organizationId },
          type: QueryTypes.SELECT,
        }
      );
      return (result[0] as any)?.total || 0;
    } catch {
      return 0;
    }
  }

  /**
   * Get active users count - Organization filtered
   */
  private async getActiveUsersCount(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    try {
      const whereCondition: any = {
        created_at: {
          [Op.between]: [startDate, endDate],
        },
      };

      if (organizationId) {
        whereCondition.organization_id = organizationId;
      }

      const result = await Analytics.count({
        distinct: true,
        col: "user_id",
        where: whereCondition,
      });
      return result || 0;
    } catch {
      return 0;
    }
  }

  /**
   * Get top performing category based on recipe views - Organization filtered
   */
  private async getTopCategory(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<{ name: string; count: number }> {
    try {
      const orgFilter = organizationId
        ? `AND r.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT c.category_name, COUNT(a.id) as count
         FROM mo_recipe_analytics a
         JOIN mo_recipe r ON a.entity_id = r.id
         JOIN mo_category c ON r.category_id = c.id
         WHERE a.event_type = 'recipe_view'
         AND a.created_at BETWEEN :startDate AND :endDate
         ${orgFilter}
         GROUP BY c.category_name
         ORDER BY count DESC
         LIMIT 1`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      if (result.length > 0) {
        const topCategory = result[0] as any;
        return {
          name: topCategory.category_name || "Unknown",
          count: parseInt(topCategory.count) || 0,
        };
      }

      return { name: "No Data", count: 0 };
    } catch {
      return { name: "No Data", count: 0 };
    }
  }

  /**
   * Get total recipe views from analytics - Organization filtered
   */
  private async getTotalRecipeViews(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    try {
      const whereCondition: any = {
        event_type: AnalyticsEventType.RECIPE_VIEW,
        created_at: {
          [Op.between]: [startDate, endDate],
        },
      };

      if (organizationId) {
        whereCondition.organization_id = organizationId;
      }

      const result = await Analytics.count({
        where: whereCondition,
      });
      return result || 0;
    } catch {
      return 0;
    }
  }

  /**
   * Get contact form submissions count - Organization filtered
   */
  private async getContactSubmissions(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    try {
      const whereCondition: any = {
        event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
        created_at: {
          [Op.between]: [startDate, endDate],
        },
      };

      if (organizationId) {
        whereCondition.organization_id = organizationId;
      }

      const result = await Analytics.count({
        where: whereCondition,
      });
      return result || 0;
    } catch {
      return 0;
    }
  }

  /**
   * Get recipe with highest impressions - Organization filtered
   */
  private async getHighestImpressionRecipe(
    organizationId?: string | null
  ): Promise<{
    name: string;
    impressions: number;
  }> {
    try {
      const orgFilter = organizationId
        ? `WHERE organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT recipe_name, recipe_impression as impressions
         FROM mo_recipe
         ${orgFilter}
         ORDER BY recipe_impression DESC
         LIMIT 1`,
        {
          replacements: { organizationId },
          type: QueryTypes.SELECT,
        }
      );

      if (result.length > 0) {
        const recipe = result[0] as any;
        return {
          name: recipe.recipe_name || "Unknown Recipe",
          impressions: parseInt(recipe.impressions) || 0,
        };
      }

      return { name: "No Data", impressions: 0 };
    } catch {
      return { name: "No Data", impressions: 0 };
    }
  }

  /**
   * Get user engagement heatmap data - Organization filtered dynamic query
   */
  private async getUserEngagementHeatmap(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    try {
      const orgFilter = organizationId
        ? `AND organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           EXTRACT(HOUR FROM created_at) as hour,
           EXTRACT(DOW FROM created_at) as day_of_week,
           COUNT(*) as engagement_count,
           COUNT(DISTINCT user_id) as unique_users,
           ROUND(COUNT(*) * 100.0 / NULLIF(COUNT(DISTINCT user_id), 0), 2) as view_percentage,
           ROUND(COUNT(CASE WHEN event_type = 'contact_form_submit' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as contact_percentage
         FROM mo_recipe_analytics
         WHERE created_at BETWEEN :startDate AND :endDate
         ${orgFilter}
         GROUP BY EXTRACT(HOUR FROM created_at), EXTRACT(DOW FROM created_at)
         ORDER BY day_of_week, hour`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((row: any) => ({
        hour: parseInt(row.hour) || 0,
        day_of_week: parseInt(row.day_of_week) || 0,
        engagement_count: parseInt(row.engagement_count) || 0,
        unique_users: parseInt(row.unique_users) || 0,
        view_percentage: parseFloat(row.view_percentage) || 0,
        contact_percentage: parseFloat(row.contact_percentage) || 0,
        intensity: Math.min(parseInt(row.engagement_count) / 10, 1), // Normalized intensity for heatmap
      }));
    } catch {
      return [];
    }
  }

  /**
   * Get conversion funnel data - Organization filtered dynamic query
   */
  private async getConversionFunnel(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    try {
      const orgFilter = organizationId
        ? `AND organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           'Recipe Views' as stage,
           1 as stage_order,
           COUNT(CASE WHEN event_type = 'recipe_view' THEN 1 END) as stage_count,
           COUNT(DISTINCT user_id) as unique_users
         FROM mo_recipe_analytics
         WHERE created_at BETWEEN :startDate AND :endDate ${orgFilter}

         UNION ALL

         SELECT
           'CTA Clicks' as stage,
           2 as stage_order,
           COUNT(CASE WHEN event_type = 'cta_click' THEN 1 END) as stage_count,
           COUNT(DISTINCT user_id) as unique_users
         FROM mo_recipe_analytics
         WHERE created_at BETWEEN :startDate AND :endDate ${orgFilter}

         UNION ALL

         SELECT
           'Contact Forms' as stage,
           3 as stage_order,
           COUNT(CASE WHEN event_type = 'contact_form_submit' THEN 1 END) as stage_count,
           COUNT(DISTINCT user_id) as unique_users
         FROM mo_recipe_analytics
         WHERE created_at BETWEEN :startDate AND :endDate ${orgFilter}

         ORDER BY stage_order`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      // Calculate conversion rates
      const totalViews =
        result.find((r: any) => r.stage === "Recipe Views")?.stage_count || 1;

      return result.map((row: any) => {
        const stageCount = parseInt(row.stage_count) || 0;
        const conversionRate =
          totalViews > 0 ? (stageCount / totalViews) * 100 : 0;

        return {
          stage: row.stage,
          stage_order: parseInt(row.stage_order),
          count: stageCount,
          unique_users: parseInt(row.unique_users) || 0,
          conversion_rate: Math.round(conversionRate * 100) / 100,
          drop_off_rate: Math.round((100 - conversionRate) * 100) / 100,
        };
      });
    } catch {
      return [
        {
          stage: "Recipe Views",
          stage_order: 1,
          count: 0,
          unique_users: 0,
          conversion_rate: 0,
          drop_off_rate: 100,
        },
        {
          stage: "CTA Clicks",
          stage_order: 2,
          count: 0,
          unique_users: 0,
          conversion_rate: 0,
          drop_off_rate: 100,
        },
        {
          stage: "Contact Forms",
          stage_order: 3,
          count: 0,
          unique_users: 0,
          conversion_rate: 0,
          drop_off_rate: 100,
        },
      ];
    }
  }

  /**
   * Get recipe views trend - Organization filtered, Top 10 recipes with single recipe name
   */
  private async getRecipeViewsTrend(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    try {
      const orgFilter = organizationId
        ? `AND r.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           r.id as recipe_id,
           r.recipe_name,
           COUNT(a.id) as total_views,
           COUNT(CASE WHEN a.created_at >= :startDate THEN 1 END) as recent_views,
           (COUNT(a.id) + COALESCE(r.recipe_impression, 0)) as combined_views
         FROM mo_recipe r
         LEFT JOIN mo_recipe_analytics a ON r.id = a.entity_id
           AND a.event_type = 'recipe_view'
           AND a.created_at BETWEEN :startDate AND :endDate
         WHERE r.recipe_status != 'deleted' ${orgFilter}
         GROUP BY r.id, r.recipe_name, r.recipe_impression
         ORDER BY combined_views DESC
         LIMIT 10`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((row: any) => ({
        recipe_id: parseInt(row.recipe_id),
        recipe_name: row.recipe_name || `Recipe ${row.recipe_id}`, // Single name, not array
        total_views: parseInt(row.total_views) || 0,
        recent_views: parseInt(row.recent_views) || 0,
        combined_views: parseInt(row.combined_views) || 0,
      }));
    } catch {
      return [];
    }
  }

  /**
   * Get category performance data - Organization filtered
   */
  private async getCategoryPerformance(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    try {
      const orgFilter = organizationId
        ? `AND r.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           c.category_name,
           COUNT(DISTINCT r.id) as unique_recipes,
           COUNT(a.id) as total_views,
           COUNT(DISTINCT a.user_id) as unique_users,
           ROUND(COUNT(a.id) * 100.0 / NULLIF(COUNT(DISTINCT r.id), 0), 2) as avg_views_per_recipe
         FROM mo_category c
         LEFT JOIN mo_recipe r ON c.id = r.category_id
         LEFT JOIN mo_recipe_analytics a ON r.id = a.entity_id
           AND a.event_type = 'recipe_view'
           AND a.created_at BETWEEN :startDate AND :endDate
         WHERE c.category_status = 'active' ${orgFilter}
         GROUP BY c.id, c.category_name
         HAVING COUNT(DISTINCT r.id) > 0
         ORDER BY total_views DESC
         LIMIT 10`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((row: any) => ({
        category_name: row.category_name || "Unknown",
        unique_recipes: parseInt(row.unique_recipes) || 0,
        total_views: parseInt(row.total_views) || 0,
        unique_users: parseInt(row.unique_users) || 0,
        avg_views_per_recipe:
          Math.round(
            (parseInt(row.total_views) /
              Math.max(parseInt(row.unique_recipes), 1)) *
              100
          ) / 100,
      }));
    } catch {
      return [];
    }
  }

  /**
   * Get recent activity data - Organization filtered
   */
  private async getRecentActivity(
    organizationId: string | null | undefined,
    limit: number = 10
  ): Promise<any[]> {
    try {
      const orgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           a.event_type,
           a.entity_id as recipe_id,
           a.created_at,
           a.user_id,
           r.recipe_name,
           COALESCE(r.recipe_name, CONCAT('Recipe ', a.entity_id)) as actual_recipe_name
         FROM mo_recipe_analytics a
         LEFT JOIN mo_recipe r ON a.entity_id = r.id
         WHERE a.entity_type = 'recipe' ${orgFilter}
         ORDER BY a.created_at DESC
         LIMIT :limit`,
        {
          replacements: { organizationId, limit },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((row: any) => ({
        event_type: row.event_type,
        recipe_id: parseInt(row.recipe_id),
        recipe_name: row.actual_recipe_name || `Recipe ${row.recipe_id}`,
        created_at: row.created_at,
        user_id: row.user_id,
        activity_description: this.getActivityDescription(
          row.event_type,
          row.actual_recipe_name || row.recipe_name
        ),
      }));
    } catch {
      return [];
    }
  }

  /**
   * Helper method to generate activity descriptions
   */
  private getActivityDescription(
    eventType: string,
    recipeName: string
  ): string {
    switch (eventType) {
      case "recipe_view":
        return `Viewed "${recipeName}"`;
      case "cta_click":
        return `Clicked CTA on "${recipeName}"`;
      case "contact_form_submit":
        return `Submitted contact form for "${recipeName}"`;
      default:
        return `Activity on "${recipeName}"`;
    }
  }

  /**
   * Helper method to parse date ranges
   */
  private getDateRange(dateRange: string): { startDate: Date; endDate: Date } {
    const endDate = new Date();
    const startDate = new Date();

    switch (dateRange) {
      case "last_7_days":
        startDate.setDate(endDate.getDate() - 7);
        break;
      case "last_30_days":
        startDate.setDate(endDate.getDate() - 30);
        break;
      case "last_90_days":
        startDate.setDate(endDate.getDate() - 90);
        break;
      case "last_year":
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    return { startDate, endDate };
  }

  // ============================================================================
  // ANALYTICS CONTROLLER METHODS - Organization-filtered and dynamic
  // ============================================================================

  /**
   * Get CTA click analytics - Enhanced with search, cta_type filter, and date ranges
   */
  async getCtaClickAnalytics(
    organizationId: string | null | undefined,
    dateRange: string = "last_30_days",
    startDate?: string,
    endDate?: string,
    page: number = 1,
    limit: number = 50,
    ctaType?: string,
    search?: string, // General search term for recipe name, user info, etc.
    sort: string = "created_at:desc",
    sortBy?: string
  ): Promise<any> {
    try {
      const { startDate: defaultStart, endDate: defaultEnd } =
        this.getDateRange(dateRange);
      const finalStartDate = startDate ? new Date(startDate) : defaultStart;
      const finalEndDate = endDate ? new Date(endDate) : defaultEnd;

      const offset = (page - 1) * limit;

      // Organization filter
      const orgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";

      // CTA Type filter
      const ctaFilter = ctaType ? `AND a.metadata->>'cta_type' = :ctaType` : "";

      // Enhanced search filter - searches in recipe name, user info, and metadata
      let searchFilter = "";
      if (search) {
        searchFilter = `AND (
          r.recipe_name ILIKE :search
          OR a.metadata->>'user_name' ILIKE :search
          OR a.metadata->>'user_email' ILIKE :search
          OR a.metadata->>'cta_type' ILIKE :search
        )`;
      }

      // Parse sort parameter
      const [sortField, sortOrder] = sort.split(":");
      const validSortFields = [
        "created_at",
        "recipe_name",
        "cta_type",
        "user_name",
      ];
      const finalSortField = validSortFields.includes(sortField)
        ? sortField
        : "created_at";
      const finalSortOrder = sortOrder === "asc" ? "ASC" : "DESC";

      // Count query
      const countResult = await sequelize.query(
        `SELECT COUNT(*) as total
         FROM mo_recipe_analytics a
         LEFT JOIN mo_recipe r ON a.entity_id = r.id
         WHERE a.event_type = 'cta_click'
         AND a.created_at BETWEEN :startDate AND :endDate
         ${orgFilter}
         ${ctaFilter}
         ${searchFilter}`,
        {
          replacements: {
            startDate: finalStartDate,
            endDate: finalEndDate,
            organizationId,
            ctaType,
            search: search ? `%${search}%` : undefined,
          },
          type: QueryTypes.SELECT,
        }
      );

      // Data query
      const dataResult = await sequelize.query(
        `SELECT
           a.id,
           a.created_at,
           a.metadata->>'cta_type' as cta_type,
           a.metadata->>'user_name' as user_name,
           a.metadata->>'user_email' as user_email,
           r.recipe_name,
           r.id as recipe_id,
           a.user_id,
           a.ip_address
         FROM mo_recipe_analytics a
         LEFT JOIN mo_recipe r ON a.entity_id = r.id
         WHERE a.event_type = 'cta_click'
         AND a.created_at BETWEEN :startDate AND :endDate
         ${orgFilter}
         ${ctaFilter}
         ${searchFilter}
         ORDER BY ${finalSortField === "cta_type" ? "a.metadata->>'cta_type'" : finalSortField === "recipe_name" ? "r.recipe_name" : finalSortField === "user_name" ? "a.metadata->>'user_name'" : "a." + finalSortField} ${finalSortOrder}
         LIMIT :limit OFFSET :offset`,
        {
          replacements: {
            startDate: finalStartDate,
            endDate: finalEndDate,
            organizationId,
            ctaType,
            search: search ? `%${search}%` : undefined,
            limit,
            offset,
          },
          type: QueryTypes.SELECT,
        }
      );

      const total = (countResult[0] as any)?.total || 0;
      const totalPages = Math.ceil(total / limit);

      return {
        status: true,
        data: dataResult,
        pagination: {
          current_page: page,
          page_size: limit,
          total_records: total,
          total_pages: totalPages,
          has_next: page < totalPages,
          has_prev: page > 1,
        },
      };
    } catch {
      return {
        status: false,
        data: [],
        pagination: {
          current_page: page,
          page_size: limit,
          total_records: 0,
          total_pages: 0,
          has_next: false,
          has_prev: false,
        },
      };
    }
  }

  /**
   * Get contact submission analytics - Enhanced with recipe name search, user email search, and date ranges
   */
  async getContactSubmissionAnalytics(
    organizationId: string | null | undefined,
    dateRange: string = "last_30_days",
    startDate?: string,
    endDate?: string,
    page?: number,
    limit?: number,
    search?: string, // General search for recipe name, user email, user name
    sort: string = "created_at:desc",
    sortBy?: string
  ): Promise<any> {
    try {
      const { startDate: defaultStart, endDate: defaultEnd } =
        this.getDateRange(dateRange);
      const finalStartDate = startDate ? new Date(startDate) : defaultStart;
      const finalEndDate = endDate ? new Date(endDate) : defaultEnd;

      // Organization filter
      const orgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";

      // Enhanced search filter - searches in recipe name, user email, user name, and contact details
      let searchFilter = "";
      if (search) {
        searchFilter = `AND (
          r.recipe_name ILIKE :search
          OR a.metadata->>'user_email' ILIKE :search
          OR a.metadata->>'user_name' ILIKE :search
          OR a.metadata->>'contact_email' ILIKE :search
          OR a.metadata->>'contact_name' ILIKE :search
          OR a.metadata->>'message' ILIKE :search
        )`;
      }

      // Parse sort parameter
      const [sortField, sortOrder] = sort.split(":");
      const validSortFields = [
        "created_at",
        "recipe_name",
        "user_email",
        "contact_email",
      ];
      const finalSortField = validSortFields.includes(sortField)
        ? sortField
        : "created_at";
      const finalSortOrder = sortOrder === "asc" ? "ASC" : "DESC";

      let query = `
        SELECT
          a.id,
          a.created_at,
          a.metadata,
          a.metadata->>'user_email' as user_email,
          a.metadata->>'user_name' as user_name,
          a.metadata->>'contact_email' as contact_email,
          a.metadata->>'contact_name' as contact_name,
          a.metadata->>'message' as message,
          r.recipe_name,
          r.id as recipe_id,
          a.user_id,
          a.ip_address
        FROM mo_recipe_analytics a
        LEFT JOIN mo_recipe r ON a.entity_id = r.id
        WHERE a.event_type = 'contact_form_submit'
        AND a.created_at BETWEEN :startDate AND :endDate
        ${orgFilter}
        ${searchFilter}
        ORDER BY ${finalSortField === "recipe_name" ? "r.recipe_name" : finalSortField === "user_email" ? "a.metadata->>'user_email'" : finalSortField === "contact_email" ? "a.metadata->>'contact_email'" : "a." + finalSortField} ${finalSortOrder}
      `;

      const replacements: any = {
        startDate: finalStartDate,
        endDate: finalEndDate,
        organizationId,
        search: search ? `%${search}%` : undefined,
      };

      // Add pagination if provided
      if (page && limit) {
        const offset = (page - 1) * limit;
        query += ` LIMIT :limit OFFSET :offset`;
        replacements.limit = limit;
        replacements.offset = offset;

        // Get total count for pagination
        const countResult = await sequelize.query(
          `SELECT COUNT(*) as total
           FROM mo_recipe_analytics a
           LEFT JOIN mo_recipe r ON a.entity_id = r.id
           WHERE a.event_type = 'contact_form_submit'
           AND a.created_at BETWEEN :startDate AND :endDate
           ${orgFilter}
           ${searchFilter}`,
          {
            replacements: {
              startDate: finalStartDate,
              endDate: finalEndDate,
              organizationId,
              search: search ? `%${search}%` : undefined,
            },
            type: QueryTypes.SELECT,
          }
        );

        const dataResult = await sequelize.query(query, {
          replacements,
          type: QueryTypes.SELECT,
        });

        const total = (countResult[0] as any)?.total || 0;
        const totalPages = Math.ceil(total / limit);

        return {
          status: true,
          data: dataResult,
          pagination: {
            current_page: page,
            page_size: limit,
            total_records: total,
            total_pages: totalPages,
            has_next: page < totalPages,
            has_prev: page > 1,
          },
        };
      } else {
        // No pagination - return all results
        const dataResult = await sequelize.query(query, {
          replacements,
          type: QueryTypes.SELECT,
        });

        return {
          status: true,
          data: dataResult,
          total: dataResult.length,
        };
      }
    } catch {
      return {
        status: false,
        data: [],
        total: 0,
      };
    }
  }

  /**
   * Get recipe view analytics - Organization filtered with pagination
   */
  async getRecipeViewAnalytics(
    organizationId: string | null | undefined,
    dateRange: string = "last_30_days",
    startDate?: string,
    endDate?: string,
    page?: number,
    limit?: number,
    recipeName?: string,
    sort: string = "created_at:desc"
  ): Promise<any> {
    try {
      const { startDate: defaultStart, endDate: defaultEnd } =
        this.getDateRange(dateRange);
      const finalStartDate = startDate ? new Date(startDate) : defaultStart;
      const finalEndDate = endDate ? new Date(endDate) : defaultEnd;

      const orgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";
      const recipeFilter = recipeName
        ? `AND r.recipe_name ILIKE :recipeName`
        : "";

      // Parse sort parameter
      const [sortField, sortOrder] = sort.split(":");
      const validSortFields = ["created_at", "recipe_name", "view_count"];
      const finalSortField = validSortFields.includes(sortField)
        ? sortField
        : "created_at";
      const finalSortOrder = sortOrder === "asc" ? "ASC" : "DESC";

      let query = `
        SELECT
          r.id as recipe_id,
          r.recipe_name,
          COUNT(a.id) as view_count,
          MAX(a.created_at) as last_viewed
        FROM mo_recipe_analytics a
        LEFT JOIN mo_recipe r ON a.entity_id = r.id
        WHERE a.event_type = 'recipe_view'
        AND a.created_at BETWEEN :startDate AND :endDate
        ${orgFilter}
        ${recipeFilter}
        GROUP BY r.id, r.recipe_name
        ORDER BY ${finalSortField === "recipe_name" ? "r.recipe_name" : finalSortField === "view_count" ? "COUNT(a.id)" : "MAX(a.created_at)"} ${finalSortOrder}
      `;

      const replacements: any = {
        startDate: finalStartDate,
        endDate: finalEndDate,
        organizationId,
        recipeName: recipeName ? `%${recipeName}%` : undefined,
      };

      // Add pagination if provided
      if (page && limit) {
        const offset = (page - 1) * limit;
        query += ` LIMIT :limit OFFSET :offset`;
        replacements.limit = limit;
        replacements.offset = offset;
      }

      const dataResult = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      return {
        status: true,
        data: dataResult,
        total: dataResult.length,
      };
    } catch {
      return {
        status: false,
        data: [],
        total: 0,
      };
    }
  }

  /**
   * Get analytics summary - Organization filtered
   */
  async getAnalyticsSummary(options: {
    organizationId: string | null | undefined;
    page?: number;
    limit?: number;
    dateRange?: string;
    event_type?: string;
    entity_type?: string;
    entity_id?: number;
    start_date?: string;
    end_date?: string;
  }): Promise<any> {
    try {
      const { organizationId, dateRange = "last_30_days" } = options;
      const { startDate, endDate } = this.getDateRange(dateRange);

      const orgFilter = organizationId
        ? `AND organization_id = :organizationId`
        : "";

      // Get summary statistics
      const summaryResult = await sequelize.query(
        `SELECT
           COUNT(CASE WHEN event_type = 'recipe_view' THEN 1 END) as total_views,
           COUNT(CASE WHEN event_type = 'cta_click' THEN 1 END) as total_cta_clicks,
           COUNT(CASE WHEN event_type = 'contact_form_submit' THEN 1 END) as total_contacts,
           COUNT(DISTINCT entity_id) as unique_recipes,
           COUNT(DISTINCT user_id) as unique_users
         FROM mo_recipe_analytics
         WHERE created_at BETWEEN :startDate AND :endDate
         ${orgFilter}`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      return {
        status: true,
        data: summaryResult[0] || {
          total_views: 0,
          total_cta_clicks: 0,
          total_contacts: 0,
          unique_recipes: 0,
          unique_users: 0,
        },
      };
    } catch {
      return {
        status: false,
        data: {
          total_views: 0,
          total_cta_clicks: 0,
          total_contacts: 0,
          unique_recipes: 0,
          unique_users: 0,
        },
      };
    }
  }

  /**
   * Get recipe view statistics for specific recipe - Organization filtered
   */
  async getRecipeViewStatistics(
    recipeId: number,
    organizationId: string | null | undefined
  ): Promise<any> {
    try {
      const orgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           r.id,
           r.recipe_name,
           COUNT(a.id) as total_views,
           COUNT(DISTINCT a.user_id) as unique_viewers,
           MAX(a.created_at) as last_viewed,
           MIN(a.created_at) as first_viewed
         FROM mo_recipe r
         LEFT JOIN mo_recipe_analytics a ON r.id = a.entity_id AND a.event_type = 'recipe_view'
         WHERE r.id = :recipeId
         ${orgFilter}
         GROUP BY r.id, r.recipe_name`,
        {
          replacements: { recipeId, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      return {
        status: true,
        data: result[0] || {
          id: recipeId,
          recipe_name: null,
          total_views: 0,
          unique_viewers: 0,
          last_viewed: null,
          first_viewed: null,
        },
      };
    } catch {
      return {
        status: false,
        data: {
          id: recipeId,
          recipe_name: null,
          total_views: 0,
          unique_viewers: 0,
          last_viewed: null,
          first_viewed: null,
        },
      };
    }
  }

  /**
   * Reset recipe view statistics for specific recipe - Organization filtered
   */
  async resetRecipeViewStatistics(
    recipeId: number,
    organizationId: string | null | undefined,
    userIds?: number[] | string
  ): Promise<any> {
    try {
      const orgFilter = organizationId
        ? `AND organization_id = :organizationId`
        : "";

      // Build user filter if userIds provided
      let userFilter = "";
      let userIdArray: number[] = [];

      if (userIds) {
        if (Array.isArray(userIds)) {
          userIdArray = userIds;
        } else if (typeof userIds === "string") {
          // Try to parse as JSON array or comma-separated values
          try {
            userIdArray = JSON.parse(userIds);
          } catch {
            userIdArray = userIds
              .split(",")
              .map((id) => parseInt(id.trim()))
              .filter((id) => !isNaN(id));
          }
        }

        if (userIdArray.length > 0) {
          userFilter = `AND user_id IN (${userIdArray.map(() => "?").join(",")})`;
        }
      }

      const whereClause = `WHERE entity_id = :recipeId AND event_type = 'recipe_view' ${orgFilter} ${userFilter}`;

      const deleteResult = await sequelize.query(
        `DELETE FROM mo_recipe_analytics ${whereClause}`,
        {
          replacements: {
            recipeId,
            organizationId,
            ...userIdArray.reduce((acc, userId, index) => {
              acc[index] = userId;
              return acc;
            }, {} as any),
          },
          type: QueryTypes.DELETE,
        }
      );

      return {
        status: true,
        message: `Recipe view statistics reset successfully`,
        deleted_records: Array.isArray(deleteResult) ? deleteResult.length : 0,
      };
    } catch {
      return {
        status: false,
        message: "Failed to reset recipe view statistics",
        deleted_records: 0,
      };
    }
  }
}

export default new AnalyticsService();
