"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_codes_1 = require("http-status-codes");
const settings_service_1 = __importDefault(require("../services/settings.service"));
const Settings_1 = require("../models/Settings");
const transaction_helper_1 = require("../helper/transaction.helper");
const validation_helper_1 = require("../helper/validation.helper");
/**
 * @description Get recipe settings configuration
 * @route GET /api/v1/private/settings/recipe-configuration
 * @access Private
 */
const getRecipeConfiguration = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
    try {
        // Input validation and sanitization
        const sanitizedQuery = validation_helper_1.ValidationHelper.sanitizeInput(req.query);
        // Get effective organization ID (handles admin users)
        const effectiveOrganizationId = yield validation_helper_1.ValidationHelper.getEffectiveOrganizationId(req.user, sanitizedQuery.organization_id);
        // Get all recipe and public settings
        const recipeSettings = yield settings_service_1.default.getSettingsByCategory(Settings_1.SettingCategory.RECIPE, effectiveOrganizationId || undefined);
        const publicSettings = yield settings_service_1.default.getSettingsByCategory(Settings_1.SettingCategory.PUBLIC, effectiveOrganizationId || undefined);
        const structuredSettings = {
            // Private Recipe Visibility Settings
            privateRecipeVisibilitySettings: {
                highlightChanges: (_a = recipeSettings["recipe.highlight_changes"]) !== null && _a !== void 0 ? _a : false,
            },
            // Public Recipe Settings
            publicRecipeSettings: {
                publicStoreAccess: recipeSettings["recipe.public_store_enabled"] || false,
            },
            // Public Recipe Call-To-Action (CTA)
            publicRecipeCallToAction: {
                contactForm: (_b = publicSettings["recipe.cta_contact_form"]) !== null && _b !== void 0 ? _b : true,
                contactInfo: {
                    enabled: publicSettings["recipe.cta_contact_info"] || false,
                    name: publicSettings["recipe.contact_info_name"] || "",
                    phone: publicSettings["recipe.contact_info_phone"] || "",
                    email: publicSettings["recipe.contact_info_email"] || "",
                    link: publicSettings["recipe.contact_info_link"] || "",
                },
                customCtaLink: {
                    enabled: publicSettings["recipe.cta_custom_link"] || false,
                    text: publicSettings["recipe.custom_cta_text"] || "",
                    link: publicSettings["recipe.custom_cta_link"] || "",
                },
                none: publicSettings["recipe.cta_none"] || false,
            },
            // Recipe Details to Display Publicly
            recipeDetailsToDisplayPublicly: {
                category: (_c = publicSettings["recipe.display_category"]) !== null && _c !== void 0 ? _c : true,
                ingredients: (_d = publicSettings["recipe.display_ingredients"]) !== null && _d !== void 0 ? _d : true,
                nutritionalInformation: (_e = publicSettings["recipe.display_nutritional_information"]) !== null && _e !== void 0 ? _e : true,
                allergenInformation: (_f = publicSettings["recipe.display_allergen_information"]) !== null && _f !== void 0 ? _f : true,
                preparationSteps: (_g = publicSettings["recipe.display_preparation_steps"]) !== null && _g !== void 0 ? _g : true,
                totalTime: (_h = publicSettings["recipe.display_total_time"]) !== null && _h !== void 0 ? _h : false,
                yieldPortioning: (_j = publicSettings["recipe.display_yield_portioning"]) !== null && _j !== void 0 ? _j : false,
                cost: (_k = publicSettings["recipe.display_cost"]) !== null && _k !== void 0 ? _k : false,
                dietarySuitability: (_l = publicSettings["recipe.display_dietary_suitability"]) !== null && _l !== void 0 ? _l : false,
                cuisineType: (_m = publicSettings["recipe.display_cuisine_type"]) !== null && _m !== void 0 ? _m : false,
                media: (_o = publicSettings["recipe.display_media"]) !== null && _o !== void 0 ? _o : false,
                links: (_p = publicSettings["recipe.display_links"]) !== null && _p !== void 0 ? _p : false,
                scale: (_q = publicSettings["recipe.display_scale"]) !== null && _q !== void 0 ? _q : false,
                serveIn: (_r = publicSettings["recipe.display_serve_in"]) !== null && _r !== void 0 ? _r : false,
                garnish: (_s = publicSettings["recipe.display_garnish"]) !== null && _s !== void 0 ? _s : false,
            },
        };
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_DATA_RETRIEVED"),
            data: structuredSettings,
        });
    }
    catch (error) {
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error fetching recipe configuration");
    }
});
/**
 * @description Update recipe settings configuration
 * @route PUT /api/v1/private/settings/recipe-configuration
 * @access Private
 */
const updateRecipeConfiguration = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c;
    try {
        // Input validation and sanitization
        const sanitizedBody = validation_helper_1.ValidationHelper.sanitizeInput(req.body);
        const uiSettings = sanitizedBody;
        // Get effective organization ID (handles admin users)
        const effectiveOrganizationId = yield validation_helper_1.ValidationHelper.getEffectiveOrganizationId(req.user, sanitizedBody.organization_id);
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        // Validate user ID
        if (!userId) {
            return res.status(http_status_codes_1.StatusCodes.UNAUTHORIZED).json({
                status: false,
                message: "User authentication required",
            });
        }
        // Convert UI structure back to flat settings
        const flatSettings = {};
        // Private Recipe Visibility Settings
        if (uiSettings.privateRecipeVisibilitySettings) {
            flatSettings["recipe.highlight_changes"] =
                uiSettings.privateRecipeVisibilitySettings.highlightChanges;
        }
        // Public Recipe Settings
        if (uiSettings.publicRecipeSettings) {
            flatSettings["recipe.public_store_enabled"] =
                uiSettings.publicRecipeSettings.publicStoreAccess;
        }
        // Public Recipe Call-To-Action
        if (uiSettings.publicRecipeCallToAction) {
            const cta = uiSettings.publicRecipeCallToAction;
            // Determine which option was explicitly enabled (true)
            const contactFormEnabled = cta.contactForm === true;
            const contactInfoEnabled = ((_b = cta.contactInfo) === null || _b === void 0 ? void 0 : _b.enabled) === true;
            const customLinkEnabled = ((_c = cta.customCtaLink) === null || _c === void 0 ? void 0 : _c.enabled) === true;
            const noneEnabled = cta.none === true;
            // Helper to decide final flag values (only one true)
            const only = (flag) => (flag ? true : false);
            // Contact Form
            if (cta.contactForm !== undefined || contactFormEnabled) {
                flatSettings["recipe.cta_contact_form"] = only(contactFormEnabled);
            }
            // Contact Info
            if (cta.contactInfo) {
                flatSettings["recipe.cta_contact_info"] = only(contactInfoEnabled);
                flatSettings["recipe.contact_info_name"] = cta.contactInfo.name || "";
                flatSettings["recipe.contact_info_phone"] = cta.contactInfo.phone || "";
                flatSettings["recipe.contact_info_email"] = cta.contactInfo.email || "";
                flatSettings["recipe.contact_info_link"] = cta.contactInfo.link || "";
            }
            // Custom CTA Link
            if (cta.customCtaLink) {
                flatSettings["recipe.cta_custom_link"] = only(customLinkEnabled);
                flatSettings["recipe.custom_cta_text"] = cta.customCtaLink.text || "";
                flatSettings["recipe.custom_cta_link"] = cta.customCtaLink.link || "";
            }
            // None
            if (cta.none !== undefined || noneEnabled) {
                flatSettings["recipe.cta_none"] = only(noneEnabled);
            }
            // Ensure exclusivity: if any one is true, others must become false (even if omitted)
            const anyEnabled = contactFormEnabled ||
                contactInfoEnabled ||
                customLinkEnabled ||
                noneEnabled;
            if (anyEnabled) {
                if (!contactFormEnabled)
                    flatSettings["recipe.cta_contact_form"] = false;
                if (!contactInfoEnabled)
                    flatSettings["recipe.cta_contact_info"] = false;
                if (!customLinkEnabled)
                    flatSettings["recipe.cta_custom_link"] = false;
                if (!noneEnabled)
                    flatSettings["recipe.cta_none"] = false;
            }
        }
        // Recipe Details to Display Publicly
        if (uiSettings.recipeDetailsToDisplayPublicly) {
            const details = uiSettings.recipeDetailsToDisplayPublicly;
            flatSettings["recipe.display_category"] = details.category;
            flatSettings["recipe.display_ingredients"] = details.ingredients;
            flatSettings["recipe.display_nutritional_information"] =
                details.nutritionalInformation;
            flatSettings["recipe.display_allergen_information"] =
                details.allergenInformation;
            flatSettings["recipe.display_preparation_steps"] =
                details.preparationSteps;
            flatSettings["recipe.display_total_time"] = details.totalTime;
            flatSettings["recipe.display_yield_portioning"] = details.yieldPortioning;
            flatSettings["recipe.display_cost"] = details.cost;
            flatSettings["recipe.display_dietary_suitability"] =
                details.dietarySuitability;
            flatSettings["recipe.display_cuisine_type"] = details.cuisineType;
            flatSettings["recipe.display_media"] = details.media;
            flatSettings["recipe.display_links"] = details.links;
            flatSettings["recipe.display_scale"] = details.scale;
            flatSettings["recipe.display_serve_in"] = details.serveIn;
            flatSettings["recipe.display_garnish"] = details.garnish;
        }
        // Update all settings
        yield settings_service_1.default.updateSettings(flatSettings, effectiveOrganizationId || undefined, userId);
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_DATA_UPDATED"),
        });
    }
    catch (error) {
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error updating recipe configuration");
    }
});
// ============================================================================
// EXPORT ONLY ESSENTIAL FUNCTIONS - Just 2 APIs needed!
// ============================================================================
exports.default = {
    getRecipeConfiguration, // GET /recipe-configuration - Get current/default settings
    updateRecipeConfiguration, // PUT /update-recipe-configuration - Update settings
};
