"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryTypes = exports.sequelize = exports.db = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const sequelize_1 = __importStar(require("sequelize"));
Object.defineProperty(exports, "QueryTypes", { enumerable: true, get: function () { return sequelize_1.QueryTypes; } });
const basename = path_1.default.basename(__filename);
const config = global.db;
const db = {};
exports.db = db;
let sequelize;
if (global.config.use_env_variable) {
    exports.sequelize = sequelize = new sequelize_1.default.Sequelize(global.config.use_env_variable, config);
}
else {
    exports.sequelize = sequelize = new sequelize_1.default.Sequelize(config.database, config.username, config.password, config);
}
sequelize
    .authenticate()
    .then(() => {
})
    .catch((err) => {
});
// Load models in specific order to avoid dependency issues
const modelFiles = [
    "User.ts",
    "Item.ts", // Load Item first since other models depend on it
    "Category.ts",
    "FoodAttributes.ts",
    "RecipeMeasure.ts",
    "Ingreditant.ts", // Note: typo in filename but correct modelName
    "Recipe.ts",
    "Settings.ts",
    "Analytics.ts",
    "ContactUs.ts",
    // Junction tables last
    "IngredientAttributes.ts",
    "IngredientCategory.ts",
    "IngredientConversion.ts",
    "RecipeAttributes.ts",
    "RecipeCategory.ts",
    "RecipeHistory.ts",
    "RecipeIngredients.ts",
    "RecipeResources.ts",
    "RecipeSettings.ts",
    "RecipeSteps.ts",
    "RecipeUser.ts",
];
// Load models in the specified order
modelFiles.forEach((file) => {
    // Try both .ts and .js extensions
    const tsPath = path_1.default.join(__dirname, file);
    const jsPath = path_1.default.join(__dirname, file.replace(".ts", ".js"));
    let filePath = tsPath;
    if (!fs_1.default.existsSync(tsPath) && fs_1.default.existsSync(jsPath)) {
        filePath = jsPath;
    }
    if (fs_1.default.existsSync(filePath)) {
        try {
            // eslint-disable-next-line @typescript-eslint/no-require-imports
            const model = require(filePath);
            // Handle both default exports and named exports
            let modelClass = model.default;
            if (!modelClass) {
                const modelKey = Object.keys(model).find((key) => model[key] &&
                    typeof model[key] === "function" &&
                    model[key].prototype instanceof sequelize_1.default.Model);
                if (modelKey) {
                    modelClass = model[modelKey];
                }
            }
            if (modelClass && modelClass.name) {
                db[modelClass.name] = modelClass;
            }
        }
        catch (error) {
            console.error(`❌ Error loading model ${file}:`, error);
        }
    }
});
// Load any remaining models that weren't in the list
fs_1.default.readdirSync(__dirname)
    .filter((file) => {
    return (file.indexOf(".") !== 0 &&
        file !== basename &&
        file.slice(-3) === ".ts" &&
        !modelFiles.includes(file));
})
    .forEach((file) => {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const model = require(path_1.default.join(__dirname, file));
        // Handle both default exports and named exports
        let modelClass = model.default;
        if (!modelClass) {
            const modelKey = Object.keys(model).find((key) => model[key] &&
                typeof model[key] === "function" &&
                model[key].prototype instanceof sequelize_1.default.Model);
            if (modelKey) {
                modelClass = model[modelKey];
            }
        }
        if (modelClass && modelClass.name) {
            db[modelClass.name] = modelClass;
        }
    }
    catch (error) {
        console.error(`❌ Error loading additional model ${file}:`, error);
    }
});
// Set up model associations
Object.keys(db).forEach((modelName) => {
    if (db[modelName].associate) {
        try {
            db[modelName].associate(db);
        }
        catch (error) {
            console.error(`❌ Error setting up associations for ${modelName}:`, error);
        }
    }
});
db.sequelize = sequelize;
db.Sequelize = sequelize_1.default;
db.Op = sequelize_1.Op;
db.QueryTypes = sequelize_1.QueryTypes;
