"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecipeCategory = exports.RecipeCategoryStatus = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var RecipeCategoryStatus;
(function (RecipeCategoryStatus) {
    RecipeCategoryStatus["active"] = "active";
    RecipeCategoryStatus["inactive"] = "inactive";
})(RecipeCategoryStatus || (exports.RecipeCategoryStatus = RecipeCategoryStatus = {}));
class RecipeCategory extends sequelize_1.Model {
}
exports.RecipeCategory = RecipeCategory;
RecipeCategory.init({
    recipe_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        references: {
            model: "mo_recipe",
            key: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
    },
    category_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        references: {
            model: "mo_category",
            key: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
    },
    status: {
        type: sequelize_1.DataTypes.ENUM(Object.values(RecipeCategoryStatus)),
        allowNull: false,
        defaultValue: RecipeCategoryStatus.active,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_recipe_category",
    modelName: "RecipeCategory",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    // Composite primary key
    indexes: [
        {
            unique: true,
            fields: ["recipe_id", "category_id"],
            name: "primary_recipe_category",
        },
        {
            fields: ["organization_id"],
            name: "idx_recipe_category_organization",
        },
        {
            fields: ["status"],
            name: "idx_recipe_category_status",
        },
    ],
});
// Define associations
RecipeCategory.associate = (models) => {
    // RecipeCategory belongs to Recipe
    RecipeCategory.belongsTo(models.Recipe, {
        foreignKey: "recipe_id",
        as: "recipe",
    });
    // RecipeCategory belongs to Category
    RecipeCategory.belongsTo(models.Category, {
        foreignKey: "category_id",
        as: "category",
    });
    // RecipeCategory belongs to User (created_by)
    RecipeCategory.belongsTo(models.User, {
        foreignKey: "created_by",
        as: "creator",
    });
    // RecipeCategory belongs to User (updated_by)
    RecipeCategory.belongsTo(models.User, {
        foreignKey: "updated_by",
        as: "updater",
    });
};
exports.default = RecipeCategory;
