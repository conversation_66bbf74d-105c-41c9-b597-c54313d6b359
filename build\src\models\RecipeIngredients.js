"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecipeIngredients = exports.RecipeIngredientsStatus = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var RecipeIngredientsStatus;
(function (RecipeIngredientsStatus) {
    RecipeIngredientsStatus["active"] = "active";
    RecipeIngredientsStatus["inactive"] = "inactive";
})(RecipeIngredientsStatus || (exports.RecipeIngredientsStatus = RecipeIngredientsStatus = {}));
class RecipeIngredients extends sequelize_1.Model {
}
exports.RecipeIngredients = RecipeIngredients;
RecipeIngredients.init({
    recipe_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        references: {
            model: "mo_recipe",
            key: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
    },
    ingredient_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        references: {
            model: "mo_ingredients",
            key: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
    },
    recipe_ingredient_status: {
        type: sequelize_1.DataTypes.ENUM(Object.values(RecipeIngredientsStatus)),
        allowNull: false,
        defaultValue: RecipeIngredientsStatus.active,
    },
    ingredient_quantity: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
    },
    ingredient_measure: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        references: {
            model: "mo_recipe_measure",
            key: "id",
        },
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
    },
    ingredient_wastage: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
    },
    ingredient_cost: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
    },
    ingredient_cooking_method: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        references: {
            model: "mo_food_attributes",
            key: "id",
        },
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
    },
    preparation_method: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        references: {
            model: "mo_food_attributes",
            key: "id",
        },
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_recipe_ingredients",
    modelName: "RecipeIngredients",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
        {
            unique: true,
            fields: ["recipe_id", "ingredient_id"],
            name: "unique_recipe_ingredient",
        },
        {
            fields: ["organization_id"],
            name: "idx_recipe_ingredients_organization",
        },
        {
            fields: ["recipe_ingredient_status"],
            name: "idx_recipe_ingredients_status",
        },
        {
            fields: ["created_by"],
            name: "idx_recipe_ingredients_created_by",
        },
        {
            fields: ["updated_by"],
            name: "idx_recipe_ingredients_updated_by",
        },
    ],
});
// Define associations
RecipeIngredients.associate = (models) => {
    // RecipeIngredients belongs to Recipe
    RecipeIngredients.belongsTo(models.Recipe, {
        foreignKey: "recipe_id",
        as: "recipe",
    });
    // RecipeIngredients belongs to Ingredient
    RecipeIngredients.belongsTo(models.Ingredient, {
        foreignKey: "ingredient_id",
        as: "ingredient",
    });
    // RecipeIngredients belongs to RecipeMeasure (ingredient_measure)
    RecipeIngredients.belongsTo(models.RecipeMeasure, {
        foreignKey: "ingredient_measure",
        as: "measureUnit",
    });
    // RecipeIngredients belongs to FoodAttributes (ingredient_cooking_method)
    RecipeIngredients.belongsTo(models.FoodAttributes, {
        foreignKey: "ingredient_cooking_method",
        as: "cookingMethod",
    });
    // RecipeIngredients belongs to FoodAttributes (preparation_method)
    RecipeIngredients.belongsTo(models.FoodAttributes, {
        foreignKey: "preparation_method",
        as: "preparationMethod",
    });
    // RecipeIngredients belongs to User (created_by)
    RecipeIngredients.belongsTo(models.User, {
        foreignKey: "created_by",
        as: "creator",
    });
    // RecipeIngredients belongs to User (updated_by)
    RecipeIngredients.belongsTo(models.User, {
        foreignKey: "updated_by",
        as: "updater",
    });
};
exports.default = RecipeIngredients;
