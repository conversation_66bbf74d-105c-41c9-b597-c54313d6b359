"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.filterPublicRecipeFields = filterPublicRecipeFields;
/**
 * Helper function to filter recipe data based on organization settings
 * @param recipeData The complete recipe data object
 * @param organizationSettings The organization settings that control field visibility
 * @returns Filtered recipe data containing only allowed fields based on organization settings
 */
function filterPublicRecipeFields(recipeData, organizationSettings) {
    if (!recipeData)
        return null;
    // Default settings if not provided
    const settings = (organizationSettings === null || organizationSettings === void 0 ? void 0 : organizationSettings.recipeDetailsToDisplayPublicly) || {
        category: true,
        ingredients: true,
        nutritionalInformation: true,
        allergenInformation: true,
        preparationSteps: true,
        totalTime: false,
        yieldPortioning: false,
        cost: false,
        dietarySuitability: false,
        cuisineType: false,
        media: false,
        links: false,
        scale: false,
        serveIn: false,
        garnish: false
    };
    // Map settings to recipe fields
    const allowedFields = new Set([
        // Always allowed basic fields
        'id',
        'recipe_title',
        'recipe_public_title',
        'recipe_description',
        'recipe_slug',
        'recipe_status',
        'has_recipe_public_visibility',
        'recipe_impression',
        'created_at',
        'updated_at',
        'organization_settings', // Always include organization settings
        // Conditionally allowed fields based on settings
        ...(settings.category ? ['categories'] : []),
        ...(settings.ingredients ? ['ingredients'] : []),
        ...(settings.nutritionalInformation ? [
            'nutrition_attributes',
            'vitamin_a',
            'vitamin_c',
            'calcium',
            'iron'
        ] : []),
        ...(settings.allergenInformation ? ['allergen_attributes'] : []),
        ...(settings.preparationSteps ? ['steps'] : []),
        ...(settings.totalTime ? [
            'recipe_preparation_time',
            'recipe_cook_time'
        ] : []),
        ...(settings.yieldPortioning ? [
            'recipe_yield',
            'recipe_yield_unit',
            'recipe_total_portions',
            'recipe_single_portion_size'
        ] : []),
        ...(settings.cost ? [
            'ingredient_costs',
            'total_cost',
            'cost_per_portion'
        ] : []),
        ...(settings.dietarySuitability ? ['dietary_attributes'] : []),
        ...(settings.cuisineType ? ['cuisine_attributes'] : []),
        ...(settings.media ? [
            'recipe_placeholder',
            'resources'
        ] : []),
        ...(settings.links ? ['resources'] : []),
        ...(settings.serveIn ? ['recipe_serve_in'] : []),
        ...(settings.garnish ? ['recipe_garnish'] : [])
    ]);
    // Helper function to check if a field or its parent is allowed
    const isFieldAllowed = (field) => {
        return Array.from(allowedFields).some(allowedField => field === allowedField ||
            field.startsWith(`${allowedField}.`) ||
            allowedField.startsWith(`${field}.`));
    };
    // Recursive function to filter nested objects
    const filterObject = (obj, parentKey = '') => {
        if (!obj || typeof obj !== 'object')
            return obj;
        if (Array.isArray(obj)) {
            return obj.map(item => filterObject(item, parentKey));
        }
        const filtered = {};
        for (const [key, value] of Object.entries(obj)) {
            const fullKey = parentKey ? `${parentKey}.${key}` : key;
            if (isFieldAllowed(fullKey)) {
                filtered[key] = filterObject(value, fullKey);
            }
        }
        return filtered;
    };
    // Filter the main recipe data
    return filterObject(recipeData);
}
