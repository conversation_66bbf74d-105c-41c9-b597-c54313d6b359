"use strict";
// #!/usr/bin/env node
// /**
//  * 🎯 ULTIMATE PERFECT API TESTING SYSTEM - 100% ACCURACY & COVERAGE
//  *
//  * ═══════════════════════════════════════════════════════════════════════════════
//  * 🏆 COMPREHENSIVE RECIPE MICROSERVICE TESTING FRAMEWORK
//  * ═══════════════════════════════════════════════════════════════════════════════
//  *
//  * 🎯 TESTING OBJECTIVES:
//  * ✅ 100% API Endpoint Coverage
//  * ✅ 100% Feature Testing Accuracy
//  * ✅ 100% Data Validation
//  * ✅ 100% Error Handling Coverage
//  * ✅ Performance & Load Testing
//  * ✅ Security & Authentication Testing
//  * ✅ File Upload/Download Testing
//  * ✅ Database Integrity Testing
//  * ✅ Real-time Analytics Testing
//  * ✅ Cross-platform Compatibility
//  *
//  * 📊 COMPREHENSIVE TESTING MODULES:
//  *
//  * 🔐 AUTHENTICATION & SECURITY:
//  * - JWT token validation & refresh
//  * - Role-based access control (RBAC)
//  * - Organization-based permissions
//  * - API rate limiting tests
//  * - Security vulnerability scanning
//  * - Input sanitization validation
//  *
//  * 📈 ANALYTICS & TRACKING (20+ endpoints):
//  * - Real-time analytics tracking with live counters
//  * - Dashboard analytics with real-time charts
//  * - CTA click analytics with metadata & session tracking
//  * - Recipe view analytics with impression counters
//  * - Contact submission analytics with real-time data
//  * - Performance metrics tracking with live updates
//  * - Event-driven analytics with real-time processing
//  * - Custom date range filtering with live data
//  * - Export analytics reports with current data
//  * - Real-time dashboard overview with live counts
//  * - Public analytics tracking without authentication
//  *
//  * 🍽️ RECIPE MANAGEMENT (25+ endpoints):
//  * - Complete CRUD operations
//  * - Recipe lifecycle management
//  * - Multi-file upload handling
//  * - Recipe versioning & history
//  * - Recipe duplication & cloning
//  * - Recipe publishing workflows
//  * - Visibility control (public/private)
//  * - Recipe bookmarking system
//  * - Recipe impression tracking
//  * - Advanced search & filtering
//  * - Recipe export (JSON/Excel/CSV/PDF)
//  * - Recipe categories & attributes
//  * - Recipe steps with images
//  * - Recipe ingredients with measurements
//  * - Recipe resources & attachments
//  *
//  * 🥬 INGREDIENT MANAGEMENT (15+ endpoints):
//  * - Complete ingredient CRUD
//  * - Ingredient categories & attributes
//  * - Unit conversions & measurements
//  * - Cost tracking & waste percentage
//  * - Ingredient import/export (Excel/CSV)
//  * - Ingredient search & filtering
//  * - Nutritional information
//  * - Allergen management
//  * - Supplier information
//  * - Inventory tracking
//  *
//  * 📂 CATEGORY MANAGEMENT (10+ endpoints):
//  * - Recipe & ingredient categories
//  * - Category hierarchy & relationships
//  * - Icon upload & management
//  * - Category status management
//  * - System vs organization categories
//  * - Category search & filtering
//  * - Category analytics
//  *
//  * 🏷️ FOOD ATTRIBUTES (10+ endpoints):
//  * - Nutrition attributes
//  * - Allergen management
//  * - Cuisine types
//  * - Dietary restrictions
//  * - Icon management
//  * - Attribute relationships
//  * - Custom attributes
//  *
//  * 📏 RECIPE MEASURES (8+ endpoints):
//  * - Unit management & conversions
//  * - Measurement relationships
//  * - System vs custom units
//  * - Unit validation
//  * - Conversion calculations
//  *
//  * ⚙️ SETTINGS MANAGEMENT (8+ endpoints):
//  * - Recipe configuration
//  * - Public/private visibility settings
//  * - CTA configuration
//  * - System initialization
//  * - User preferences
//  * - Organization settings
//  *
//  * 📞 CONTACT MANAGEMENT (8+ endpoints):
//  * - Contact form submissions
//  * - Contact analytics & tracking
//  * - Contact CRUD operations
//  * - Contact status management
//  * - Contact export functionality
//  *
//  * 📁 FILE MANAGEMENT:
//  * - S3/MinIO file uploads
//  * - Multi-format support (images/videos/docs)
//  * - File deduplication with SHA-256
//  * - File organization & management
//  * - File compression & optimization
//  * - File security & access control
//  *
//  * 🚀 PERFORMANCE TESTING:
//  * - Response time validation
//  * - Concurrent request handling
//  * - Memory usage monitoring
//  * - Database query optimization
//  * - Caching effectiveness
//  *
//  * 🔍 DATA VALIDATION:
//  * - Input validation testing
//  * - Output format verification
//  * - Data type consistency
//  * - Boundary value testing
//  * - SQL injection prevention
//  * - XSS protection validation
//  *
//  * 📊 REPORTING & ANALYTICS:
//  * - Detailed test reports
//  * - Performance metrics
//  * - Coverage analysis
//  * - Error categorization
//  * - Trend analysis
//  * - Export capabilities
//  *
//  * Total: 120+ endpoints with comprehensive feature testing
//  *
//  * Usage: node complete-api-test.js
//  * Advanced: node complete-api-test.js --mode=performance --coverage=100
//  */
// const axios = require("axios");
// const fs = require("fs");
// const FormData = require("form-data");
// // 🔧 ADVANCED CONFIGURATION SYSTEM
// const CONFIG = {
//   // 🌐 Server Configuration
//   BASE_URL: process.env.API_BASE_URL || "http://localhost:9025",
//   TOKEN:
//     process.env.API_TOKEN ||
//     "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cS4nYnczulINkOO9_fE4wRwTTncp8u6ZNz8OAl4VFWA",
//   ORGANIZATION_ID: "f058d1b5-f431-4da1-8977-01a6ff5893a2",
// //   ⏱️ Timeout Configuration
//   TIMEOUT: parseInt(process.env.API_TIMEOUT) || 30000,
//   LONG_TIMEOUT: parseInt(process.env.API_LONG_TIMEOUT) || 60000,
//   FILE_UPLOAD_TIMEOUT: parseInt(process.env.FILE_UPLOAD_TIMEOUT) || 120000,
// //   🚀 Performance Testing Configuration
//   PERFORMANCE_MODE: process.env.PERFORMANCE_MODE === "true",
//   CONCURRENT_REQUESTS: parseInt(process.env.CONCURRENT_REQUESTS) || 5,
//   LOAD_TEST_DURATION: parseInt(process.env.LOAD_TEST_DURATION) || 30000,
// //   📊 Test Configuration
//   COVERAGE_TARGET: parseInt(process.env.COVERAGE_TARGET) || 100,
//   DETAILED_LOGGING: process.env.DETAILED_LOGGING !== "false",
//   SAVE_RESPONSES: process.env.SAVE_RESPONSES === "true",
//   GENERATE_REPORTS: process.env.GENERATE_REPORTS !== "false",
// //   🔒 Security Testing
//   SECURITY_TESTS: process.env.SECURITY_TESTS !== "false",
//   RATE_LIMIT_TESTS: process.env.RATE_LIMIT_TESTS === "true",
//   // 📁 File Testing Configuration
//   TEST_FILE_SIZES: {
//     SMALL: 1024, // 1KB
//     MEDIUM: 1024 * 1024, // 1MB
//     LARGE: 5 * 1024 * 1024, // 5MB
//   },
//   // 🎯 Test Categories
//   TEST_CATEGORIES: {
//     ANALYTICS: process.env.TEST_ANALYTICS !== "false",
//     RECIPES: process.env.TEST_RECIPES !== "false",
//     INGREDIENTS: process.env.TEST_INGREDIENTS !== "false",
//     CATEGORIES: process.env.TEST_CATEGORIES !== "false",
//     FOOD_ATTRIBUTES: process.env.TEST_FOOD_ATTRIBUTES !== "false",
//     RECIPE_MEASURES: process.env.TEST_RECIPE_MEASURES !== "false",
//     SETTINGS: process.env.TEST_SETTINGS !== "false",
//     CONTACT: process.env.TEST_CONTACT !== "false",
//     DASHBOARD: process.env.TEST_DASHBOARD !== "false",
//     PUBLIC_APIS: process.env.TEST_PUBLIC_APIS !== "false",
//   },
// };
// // 📊 ADVANCED TEST RESULTS TRACKING SYSTEM
// const testResults = {
//   // 📈 Basic Metrics
//   total: 0,
//   passed: 0,
//   failed: 0,
//   skipped: 0,
//   // 🕒 Performance Metrics
//   startTime: null,
//   endTime: null,
//   totalDuration: 0,
//   averageResponseTime: 0,
//   slowestTest: { name: "", duration: 0 },
//   fastestTest: { name: "", duration: Infinity },
//   // 📊 Detailed Tracking
//   errors: [],
//   details: [],
//   responses: [],
//   performanceData: [],
//   // 🎯 Category-wise Results
//   categories: {
//     private_analytics: {
//       total: 0,
//       passed: 0,
//       failed: 0,
//       skipped: 0,
//       avgResponseTime: 0,
//       totalDuration: 0,
//     },
//     private_category: {
//       total: 0,
//       passed: 0,
//       failed: 0,
//       skipped: 0,
//       avgResponseTime: 0,
//       totalDuration: 0,
//     },
//     private_dashboard: {
//       total: 0,
//       passed: 0,
//       failed: 0,
//       skipped: 0,
//       avgResponseTime: 0,
//       totalDuration: 0,
//     },
//     private_food_attributes: {
//       total: 0,
//       passed: 0,
//       failed: 0,
//       skipped: 0,
//       avgResponseTime: 0,
//       totalDuration: 0,
//     },
//     private_ingredients: {
//       total: 0,
//       passed: 0,
//       failed: 0,
//       skipped: 0,
//       avgResponseTime: 0,
//       totalDuration: 0,
//     },
//     private_recipe: {
//       total: 0,
//       passed: 0,
//       failed: 0,
//       skipped: 0,
//       avgResponseTime: 0,
//       totalDuration: 0,
//     },
//     private_recipe_measure: {
//       total: 0,
//       passed: 0,
//       failed: 0,
//       skipped: 0,
//       avgResponseTime: 0,
//       totalDuration: 0,
//     },
//     private_settings: {
//       total: 0,
//       passed: 0,
//       failed: 0,
//       skipped: 0,
//       avgResponseTime: 0,
//       totalDuration: 0,
//     },
//     public_analytics: {
//       total: 0,
//       passed: 0,
//       failed: 0,
//       skipped: 0,
//       avgResponseTime: 0,
//       totalDuration: 0,
//     },
//     public_contact: {
//       total: 0,
//       passed: 0,
//       failed: 0,
//       skipped: 0,
//       avgResponseTime: 0,
//       totalDuration: 0,
//     },
//     public_recipe: {
//       total: 0,
//       passed: 0,
//       failed: 0,
//       skipped: 0,
//       avgResponseTime: 0,
//       totalDuration: 0,
//     },
//   },
//   // 🔒 Security Test Results
//   securityTests: {
//     total: 0,
//     passed: 0,
//     failed: 0,
//     vulnerabilities: [],
//   },
//   // 📁 File Upload Test Results
//   fileTests: {
//     total: 0,
//     passed: 0,
//     failed: 0,
//     uploadSizes: [],
//     uploadTimes: [],
//   },
//   // 🚀 Performance Test Results
//   performanceTests: {
//     total: 0,
//     passed: 0,
//     failed: 0,
//     concurrentTests: [],
//     loadTests: [],
//   },
// };
// // 🎨 CONSOLE COLORS
// const colors = {
//   reset: "\x1b[0m",
//   bright: "\x1b[1m",
//   red: "\x1b[31m",
//   green: "\x1b[32m",
//   yellow: "\x1b[33m",
//   blue: "\x1b[34m",
//   magenta: "\x1b[35m",
//   cyan: "\x1b[36m",
// };
// // 🛠️ UTILITY FUNCTIONS
// const log = (message, color = "reset") => {
//   console.log(`${colors[color]}${message}${colors.reset}`);
// };
// const logSuccess = (message) => log(`✅ ${message}`, "green");
// const logError = (message) => log(`❌ ${message}`, "red");
// const logWarning = (message) => log(`⚠️  ${message}`, "yellow");
// const logInfo = (message) => log(`ℹ️  ${message}`, "blue");
// const logHeader = (message) => log(`\n🚀 ${message}`, "cyan");
// // 📡 HTTP CLIENT SETUP
// const createHttpClient = (requiresAuth = true) => {
//   const headers = {
//     "Content-Type": "application/json",
//     "platform-type": "web",
//   };
//   if (requiresAuth) {
//     headers["Authorization"] = `Bearer ${CONFIG.TOKEN}`;
//   }
//   return axios.create({
//     baseURL: CONFIG.BASE_URL,
//     timeout: CONFIG.TIMEOUT,
//     headers,
//   });
// };
// // 🎯 ULTIMATE COMPLETE API TESTER CLASS
// class CompleteAPITester {
//   constructor() {
//     // 📊 Test Data Management
//     this.testData = {
//       createdIds: {
//         recipe: null,
//         category: null,
//         ingredient: null,
//         foodAttribute: null,
//         recipeMeasure: null,
//         contact: null,
//         recipeCategory: null,
//         ingredientCategory: null,
//         recipeStep: null,
//         recipeResource: null,
//         recipeAttribute: null,
//         recipeIngredient: null,
//       },
//       testFiles: [],
//       testResponses: [],
//       performanceMetrics: [],
//     };
//     // 🌐 HTTP Clients
//     this.httpClient = createHttpClient(true);
//     this.publicHttpClient = createHttpClient(false);
//     // 🕒 Performance Tracking
//     this.startTime = Date.now();
//     this.testStartTime = null;
//     // 📊 Advanced Metrics
//     this.responseTimes = [];
//     this.errorCounts = {};
//     this.endpointCoverage = new Set();
//     // 🔒 Security Testing
//     this.securityVulnerabilities = [];
//     this.rateLimitTests = [];
//     // 📁 File Management
//     this.uploadedFiles = [];
//     this.tempFiles = [];
//   }
//   async runTest(testName, testFunction, category = "general", options = {}) {
//     // 📊 Initialize Test Metrics
//     testResults.total++;
//     if (testResults.categories[category]) {
//       testResults.categories[category].total++;
//     }
//     // 🕒 Performance Tracking
//     const testStartTime = Date.now();
//     this.testStartTime = testStartTime;
//     try {
//       // 📝 Detailed Logging
//       if (CONFIG.DETAILED_LOGGING) {
//         logInfo(`Testing: ${testName}`);
//         if (options.description) {
//           logInfo(`Description: ${options.description}`);
//         }
//       } else {
//         logInfo(`Testing: ${testName}`);
//       }
//       // 🚀 Execute Test
//       const result = await testFunction();
//       const duration = Date.now() - testStartTime;
//       // 📊 Update Performance Metrics
//       this.responseTimes.push(duration);
//       this.endpointCoverage.add(testName);
//       // 🎯 Track Fastest/Slowest Tests
//       if (duration < testResults.fastestTest.duration) {
//         testResults.fastestTest = { name: testName, duration };
//       }
//       if (duration > testResults.slowestTest.duration) {
//         testResults.slowestTest = { name: testName, duration };
//       }
//       // ✅ Test Success Handling
//       if (result && result.success !== false) {
//         testResults.passed++;
//         if (testResults.categories[category]) {
//           testResults.categories[category].passed++;
//           testResults.categories[category].totalDuration += duration;
//         }
//         logSuccess(`${testName} (${duration}ms)`);
//         // 📊 Store Detailed Results
//         const testDetail = {
//           name: testName,
//           category,
//           status: "PASSED",
//           duration,
//           timestamp: new Date().toISOString(),
//           responseSize: result.data ? JSON.stringify(result.data).length : 0,
//           endpoint: options.endpoint || "unknown",
//           method: options.method || "unknown",
//         };
//         testResults.details.push(testDetail);
//         // 📈 Performance Data Collection
//         testResults.performanceData.push({
//           test: testName,
//           category,
//           duration,
//           timestamp: testStartTime,
//           success: true,
//         });
//       } else {
//         throw new Error(result?.message || "Test failed");
//       }
//     } catch (error) {
//       const duration = Date.now() - testStartTime;
//       // ⚠️ Skip on Error Handling
//       if (options.skipOnError) {
//         testResults.skipped++;
//         if (testResults.categories[category]) {
//           testResults.categories[category].skipped++;
//         }
//         logWarning(
//           `${testName}: SKIPPED - ${error.response?.data?.message || error.message}`
//         );
//         testResults.details.push({
//           name: testName,
//           category,
//           status: "SKIPPED",
//           duration,
//           error: error.response?.data?.message || error.message,
//           timestamp: new Date().toISOString(),
//         });
//         return;
//       }
//       // ❌ Test Failure Handling
//       testResults.failed++;
//       if (testResults.categories[category]) {
//         testResults.categories[category].failed++;
//         testResults.categories[category].totalDuration += duration;
//       }
//       const errorMessage = error.response?.data?.message || error.message;
//       const statusCode = error.response?.status;
//       // 📊 Error Categorization
//       if (!this.errorCounts[statusCode]) {
//         this.errorCounts[statusCode] = 0;
//       }
//       this.errorCounts[statusCode]++;
//       logError(`${testName}: ${errorMessage}`);
//       // 📊 Store Error Details
//       const errorDetail = {
//         test: testName,
//         category,
//         error: errorMessage,
//         status: statusCode,
//         data: error.response?.data,
//         duration,
//         timestamp: new Date().toISOString(),
//         endpoint: options.endpoint || "unknown",
//         method: options.method || "unknown",
//       };
//       testResults.errors.push(errorDetail);
//       testResults.details.push({
//         name: testName,
//         category,
//         status: "FAILED",
//         error: errorMessage,
//         duration,
//         timestamp: new Date().toISOString(),
//       });
//       // 📈 Performance Data Collection (Failed)
//       testResults.performanceData.push({
//         test: testName,
//         category,
//         duration,
//         timestamp: testStartTime,
//         success: false,
//         error: errorMessage,
//       });
//     }
//   }
//   async testEndpoint(method, endpoint, data = null, options = {}) {
//     const client = options.public ? this.publicHttpClient : this.httpClient;
//     const maxRetries = 3;
//     let lastError;
//     // Retry logic for robust testing
//     for (let attempt = 1; attempt <= maxRetries; attempt++) {
//       const startTime = Date.now();
//       const config = {
//         method: method.toLowerCase(),
//         url: endpoint,
//         timeout: options.timeout || CONFIG.TIMEOUT,
//         ...options,
//       };
//       // Remove options that shouldn't be passed to axios
//       delete config.public;
//       delete config.skipOnError;
//       if (data) {
//         if (method.toUpperCase() === "GET") {
//           config.params = data;
//         } else {
//           config.data = data;
//         }
//       }
//       try {
//         const response = await client(config);
//         const duration = Date.now() - startTime;
//         // 📊 Track endpoint performance
//         this.responseTimes.push(duration);
//         // 📈 Store response data for analysis
//         if (CONFIG.SAVE_RESPONSES) {
//           this.testData.testResponses.push({
//             endpoint,
//             method,
//             duration,
//             status: response.status,
//             dataSize: JSON.stringify(response.data).length,
//             timestamp: new Date().toISOString(),
//           });
//         }
//         return response.data;
//       } catch (error) {
//         const duration = Date.now() - startTime;
//         lastError = error;
//         // 📊 Track failed endpoint performance
//         this.responseTimes.push(duration);
//         // Handle retryable errors
//         if ((error.code === "ECONNRESET" || error.code === "ECONNREFUSED" || error.code === "ETIMEDOUT") && attempt < maxRetries) {
//           logWarning(`Attempt ${attempt}/${maxRetries} failed: ${error.code || error.message}. Retrying in ${attempt}s...`);
//           await new Promise(resolve => setTimeout(resolve, attempt * 1000)); // Exponential backoff
//           continue;
//         }
//         // Handle validation errors with detailed information
//         if (error.response && error.response.status >= 400 && error.response.status < 500) {
//           const errorMessage = error.response.data?.message || error.response.data?.error || error.message;
//           throw new Error(errorMessage);
//         }
//         // Handle connection errors
//         if (error.code === "ECONNRESET") {
//           throw new Error(`Connection reset by server - endpoint may be overloaded or temporarily unavailable`);
//         }
//         if (error.code === "ECONNREFUSED") {
//           throw new Error(`Connection refused - server may be down or endpoint not available`);
//         }
//         if (error.code === "ETIMEDOUT") {
//           throw new Error(`Request timeout - server took too long to respond`);
//         }
//         // 🔒 Security vulnerability detection
//         if (error.response?.status === 500 && CONFIG.SECURITY_TESTS) {
//           this.securityVulnerabilities.push({
//             endpoint,
//             method,
//             error: error.response?.data?.message || error.message,
//             timestamp: new Date().toISOString(),
//           });
//         }
//         throw error;
//       }
//     }
//     // If all retries failed, throw the last error
//     throw lastError;
//   }
//   // 🚀 PERFORMANCE TESTING METHODS
//   async performanceTest(testName, testFunction, options = {}) {
//     const iterations = options.iterations || 10;
//     const concurrency = options.concurrency || 3;
//     const results = [];
//     logInfo(
//       `🚀 Performance Testing: ${testName} (${iterations} iterations, ${concurrency} concurrent)`
//     );
//     for (let i = 0; i < iterations; i += concurrency) {
//       const batch = [];
//       for (let j = 0; j < concurrency && i + j < iterations; j++) {
//         batch.push(this.measurePerformance(testFunction));
//       }
//       const batchResults = await Promise.allSettled(batch);
//       results.push(
//         ...batchResults
//           .map((r) => (r.status === "fulfilled" ? r.value : null))
//           .filter(Boolean)
//       );
//     }
//     const avgTime =
//       results.reduce((sum, r) => sum + r.duration, 0) / results.length;
//     const minTime = Math.min(...results.map((r) => r.duration));
//     const maxTime = Math.max(...results.map((r) => r.duration));
//     logSuccess(
//       `Performance Results: Avg: ${avgTime.toFixed(2)}ms, Min: ${minTime}ms, Max: ${maxTime}ms`
//     );
//     testResults.performanceTests.total++;
//     if (avgTime < (options.maxAvgTime || 5000)) {
//       testResults.performanceTests.passed++;
//     } else {
//       testResults.performanceTests.failed++;
//     }
//     return { avgTime, minTime, maxTime, results };
//   }
//   async measurePerformance(testFunction) {
//     const startTime = Date.now();
//     try {
//       const result = await testFunction();
//       const duration = Date.now() - startTime;
//       return { duration, success: true, result };
//     } catch (error) {
//       const duration = Date.now() - startTime;
//       return { duration, success: false, error: error.message };
//     }
//   }
//   // 🔒 SECURITY TESTING METHODS
//   async securityTest(testName, endpoint, method = "GET", options = {}) {
//     logInfo(`🔒 Security Testing: ${testName}`);
//     const securityPayloads = [
//       // SQL Injection attempts
//       "'; DROP TABLE users; --",
//       "1' OR '1'='1",
//       "admin'/*",
//       // XSS attempts
//       "<script>alert('xss')</script>",
//       "javascript:alert('xss')",
//       "<img src=x onerror=alert('xss')>",
//       // Path traversal
//       "../../../etc/passwd",
//       "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
//       // Command injection
//       "; ls -la",
//       "| whoami",
//       "&& dir",
//     ];
//     let vulnerabilities = 0;
//     for (const payload of securityPayloads) {
//       try {
//         const testData = options.paramName
//           ? { [options.paramName]: payload }
//           : { test: payload };
//         await this.testEndpoint(method, endpoint, testData);
//         // If no error thrown, check response for potential vulnerabilities
//         vulnerabilities++;
//       } catch (error) {
//         // Expected behavior - server should reject malicious input
//         if (error.response?.status === 400 || error.response?.status === 422) {
//           // Good - input validation working
//         } else if (error.response?.status === 500) {
//           // Potential vulnerability - server error on malicious input
//           vulnerabilities++;
//           this.securityVulnerabilities.push({
//             endpoint,
//             method,
//             payload,
//             error: error.message,
//             timestamp: new Date().toISOString(),
//           });
//         }
//       }
//     }
//     testResults.securityTests.total++;
//     if (vulnerabilities === 0) {
//       testResults.securityTests.passed++;
//       logSuccess(`Security Test Passed: ${testName}`);
//     } else {
//       testResults.securityTests.failed++;
//       logError(
//         `Security Test Failed: ${testName} - ${vulnerabilities} potential vulnerabilities`
//       );
//     }
//     return vulnerabilities === 0;
//   }
//   // 📁 FILE UPLOAD TESTING METHODS
//   async testFileUpload(endpoint, fieldName, options = {}) {
//     const testSizes = options.testSizes || [
//       CONFIG.TEST_FILE_SIZES.SMALL,
//       CONFIG.TEST_FILE_SIZES.MEDIUM,
//     ];
//     const results = [];
//     for (const size of testSizes) {
//       const fileName = `test-file-${size}-${Date.now()}.txt`;
//       const content = "A".repeat(size);
//       try {
//         fs.writeFileSync(fileName, content);
//         this.tempFiles.push(fileName);
//         const form = new FormData();
//         form.append(fieldName, fs.createReadStream(fileName));
//         const startTime = Date.now();
//         await this.testEndpoint("POST", endpoint, form, {
//           headers: form.getHeaders(),
//           timeout: CONFIG.FILE_UPLOAD_TIMEOUT,
//         });
//         const duration = Date.now() - startTime;
//         results.push({
//           size,
//           duration,
//           success: true,
//           fileName,
//         });
//         testResults.fileTests.total++;
//         testResults.fileTests.passed++;
//         testResults.fileTests.uploadSizes.push(size);
//         testResults.fileTests.uploadTimes.push(duration);
//         logSuccess(`File Upload Test (${size} bytes): ${duration}ms`);
//       } catch (error) {
//         results.push({
//           size,
//           success: false,
//           error: error.message,
//           fileName,
//         });
//         testResults.fileTests.total++;
//         testResults.fileTests.failed++;
//         logError(`File Upload Test Failed (${size} bytes): ${error.message}`);
//       }
//     }
//     return results;
//   }
//   // 🧹 CLEANUP METHODS
//   async cleanup() {
//     logInfo("🧹 Cleaning up test data and temporary files...");
//     // Clean up temporary files
//     for (const file of this.tempFiles) {
//       try {
//         if (fs.existsSync(file)) {
//           fs.unlinkSync(file);
//         }
//       } catch (error) {
//         logWarning(`Failed to delete temp file ${file}: ${error.message}`);
//       }
//     }
//     // Clean up created test data (optional - comment out if you want to keep test data)
//     /*
//     const cleanupTasks = [];
//     if (this.testData.createdIds.recipe) {
//       cleanupTasks.push(this.testEndpoint('DELETE', `/v1/private/recipes/delete/${this.testData.createdIds.recipe}`));
//     }
//     if (this.testData.createdIds.ingredient) {
//       cleanupTasks.push(this.testEndpoint('DELETE', `/v1/private/ingredients/delete/${this.testData.createdIds.ingredient}`));
//     }
//     // Execute cleanup tasks
//     await Promise.allSettled(cleanupTasks);
//     */
//     logSuccess("Cleanup completed");
//   }
//   // ============================================================================
//   // 📊 COMPREHENSIVE PRIVATE ANALYTICS TESTS (8 endpoints) - REAL-TIME ANALYTICS
//   // ============================================================================
//   async testPrivateAnalytics() {
//     logHeader("COMPREHENSIVE PRIVATE ANALYTICS API TESTS (8 endpoints)");
//     await this.runTest(
//       "Get Analytics Overview with Real-Time Data",
//       async () => {
//         return await this.testEndpoint("GET", "/v1/private/analytics/", {
//           date_range: "last_30_days",
//           page: 1,
//           limit: 20,
//         });
//       },
//       "private_analytics"
//     );
//     await this.runTest(
//       "Get Analytics Overview with Custom Date Range",
//       async () => {
//         const endDate = new Date().toISOString();
//         const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
//         return await this.testEndpoint("GET", "/v1/private/analytics/", {
//           date_range: "custom",
//           start_date: startDate,
//           end_date: endDate,
//         });
//       },
//       "private_analytics"
//     );
//     await this.runTest(
//       "Get CTA Click Analytics with Real-Time Tracking",
//       async () => {
//         return await this.testEndpoint(
//           "GET",
//           "/v1/private/analytics/cta-clicks",
//           {
//             date_range: "last_30_days",
//             sort: "desc",
//           }
//         );
//       },
//       "private_analytics"
//     );
//     await this.runTest(
//       "Get Recipe View Analytics with Impression Counts",
//       async () => {
//         return await this.testEndpoint(
//           "GET",
//           "/v1/private/analytics/recipe-views",
//           {
//             date_range: "last_30_days",
//             sort: "desc",
//           }
//         );
//       },
//       "private_analytics"
//     );
//     await this.runTest(
//       "Get Contact Submissions Analytics",
//       async () => {
//         return await this.testEndpoint(
//           "GET",
//           "/v1/private/analytics/contact-submissions",
//           {
//             date_range: "last_30_days",
//             page: 1,
//             limit: 10,
//           }
//         );
//       },
//       "private_analytics"
//     );
//     // Create a contact submission first for deletion test
//     const contactData = {
//       name: "Test Contact for Analytics",
//       email: "<EMAIL>",
//       message: "This contact will be deleted in analytics test",
//     };
//     try {
//       const contactResult = await this.testEndpoint(
//         "POST",
//         "/v1/public/contact-us/",
//         contactData,
//         { public: true }
//       );
//       const contactId = contactResult.data?.id;
//       if (contactId) {
//         await this.runTest(
//           "Delete Contact Submission",
//           async () => {
//             return await this.testEndpoint(
//               "DELETE",
//               `/v1/private/analytics/contact-submissions/${contactId}`
//             );
//           },
//           "private_analytics"
//         );
//       }
//     } catch {
//       logWarning(
//         "Could not create contact for deletion test, skipping delete test"
//       );
//     }
//     await this.runTest(
//       "Analytics Test Endpoint",
//       async () => {
//         return await this.testEndpoint("GET", "/v1/private/analytics/test");
//       },
// private_analytics
//     );
//   }
//   // ============================================================================
//   // 📂 COMPREHENSIVE PRIVATE CATEGORY TESTS (5 endpoints) - WITH FILE UPLOADS
//   // ============================================================================
//   async testPrivateCategory() {
//     logHeader("COMPREHENSIVE PRIVATE CATEGORY API TESTS (5 endpoints)");
//     await this.runTest(
//       "List Categories",
//       async () => {
//         return await this.testEndpoint("GET", "/v1/private/category/list");
//       },
// private_category
//     );
//     await this.runTest(
//       "List Categories with Filters",
//       async () => {
//         return await this.testEndpoint("GET", "/v1/private/category/list", {
//           page: 1,
//           limit: 10,
//           search: "test",
//           category_type: "ingredient",
//           category_status: "active",
//           sort_by: "category_name",
//           sort_order: "asc",
//         });
//       },
//       "private_category"
//     );
//     await this.runTest(
//       "Create Category with Icon",
//       async () => {
//         // Create a simple test file for icon upload (using text as placeholder)
//         const testIconContent = "test-icon-content-" + Date.now();
//         fs.writeFileSync("test-category-icon.png", testIconContent);
//         const form = new FormData();
//         form.append("category_name", `Test Category ${Date.now()}`);
//         form.append(
//           "category_description",
//           "Test category description with icon"
//         );
//         form.append("category_type", "ingredient");
//         form.append("category_status", "active");
//         form.append(
//           "categoryIcon",
//           fs.createReadStream("test-category-icon.png")
//         );
//         const result = await this.testEndpoint(
//           "POST",
//           "/v1/private/category/create",
//           form,
//           {
//             headers: form.getHeaders(),
//             timeout: 30000,
//           }
//         );
//         this.testData.createdIds.category = result.data?.id;
//         // Cleanup
//         if (fs.existsSync("test-category-icon.png")) {
//           fs.unlinkSync("test-category-icon.png");
//         }
//         return result;
//       },
//       "private_category"
//     );
//     if (this.testData.createdIds.category) {
//       await this.runTest(
//         "Get Category by ID",
//         async () => {
//           return await this.testEndpoint(
//             "GET",
//             `/v1/private/category/get/${this.testData.createdIds.category}`
//           );
//         },
//         "private_category"
//       );
//       await this.runTest(
//         "Update Category with Icon",
//         async () => {
//           // Create another test icon
//           const testIconContent = "updated-test-icon-content-" + Date.now();
//           fs.writeFileSync("test-category-icon-update.png", testIconContent);
//           const form = new FormData();
//           form.append("category_name", `Updated Test Category ${Date.now()}`);
//           form.append(
//             "category_description",
//             "Updated test category description with new icon"
//           );
//           form.append(
//             "categoryIcon",
//             fs.createReadStream("test-category-icon-update.png")
//           );
//           const result = await this.testEndpoint(
//             "PUT",
//             `/v1/private/category/update/${this.testData.createdIds.category}`,
//             form,
//             {
//               headers: form.getHeaders(),
//               timeout: 30000,
//             }
//           );
//           // Cleanup
//           fs.unlinkSync("test-category-icon-update.png");
//           return result;
//         },
//         "private_category"
//       );
//       await this.runTest(
//         "Delete Category",
//         async () => {
//           return await this.testEndpoint(
//             "DELETE",
//             `/v1/private/category/delete/${this.testData.createdIds.category}`
//           );
//         },
//         "private_category"
//       );
//     }
//   }
//   // ============================================================================
//   // 📊 PRIVATE DASHBOARD TESTS (6 endpoints) - REAL-TIME DASHBOARD FEATURES
//   // ============================================================================
//   async testPrivateDashboard() {
//     logHeader("PRIVATE DASHBOARD API TESTS (6 endpoints) - REAL-TIME FEATURES");
//     await this.runTest(
//       "Dashboard Overview with Real-Time Counts",
//       async () => {
//         return await this.testEndpoint(
//           "GET",
//           "/v1/private/dashboard/overview",
//           { date_range: "last_30_days" }
//         );
//       },
//       "private_dashboard"
//     );
//     await this.runTest(
//       "Dashboard Overview with Different Date Ranges",
//       async () => {
//         return await this.testEndpoint(
//           "GET",
//           "/v1/private/dashboard/overview",
//           { date_range: "last_7_days" }
//         );
//       },
//       "private_dashboard"
//     );
//     await this.runTest(
//       "Dashboard Public Analytics with Real-Time Data",
//       async () => {
//         return await this.testEndpoint(
//           "GET",
//           "/v1/private/dashboard/public-analytics",
//           { date_range: "last_30_days" }
//         );
//       },
//       "private_dashboard"
//     );
//     await this.runTest(
//       "Dashboard Public Analytics - Last 7 Days",
//       async () => {
//         return await this.testEndpoint(
//           "GET",
//           "/v1/private/dashboard/public-analytics",
//           { date_range: "last_7_days" }
//         );
//       },
//       "private_dashboard"
//     );
//     await this.runTest(
//       "Dashboard Test Endpoint",
//       async () => {
//         return await this.testEndpoint("GET", "/v1/private/dashboard/test");
//       },
//       "private_dashboard"
//     );
//     await this.runTest(
//       "Dashboard Export with Real-Time Data",
//       async () => {
//         return await this.testEndpoint("GET", "/v1/private/dashboard/export", {
//           format: "json",
//           date_range: "last_30_days",
//         });
//       },
//       "private_dashboard"
//     );
//   }
//   // ============================================================================
//   // 🏷️ PRIVATE FOOD ATTRIBUTES TESTS (5 endpoints)
//   // ============================================================================
//   async testPrivateFoodAttributes() {
//     logHeader("PRIVATE FOOD ATTRIBUTES API TESTS (5 endpoints)");
//     await this.runTest(
//       "List Food Attributes",
//       async () => {
//         return await this.testEndpoint(
//           "GET",
//           "/v1/private/food-attributes/list"
//         );
//       },
//       "private_food_attributes"
//     );
//     await this.runTest(
//       "Create Food Attribute",
//       async () => {
//         const attributeData = {
//           attribute_title: `Test Attribute ${Date.now()}`, // Required field
//           attribute_type: "nutrition", // Required field - must be one of: nutrition, allergen, cuisine, dietary
//           attribute_description: "Test attribute description",
//           attribute_status: "active", // Optional - defaults to active
//         };
//         const result = await this.testEndpoint(
//           "POST",
//           "/v1/private/food-attributes/create",
//           attributeData
//         );
//         this.testData.createdIds.foodAttribute = result.data?.id;
//         return result;
//       },
//       "private_food_attributes"
//     );
//     if (this.testData.createdIds.foodAttribute) {
//       await this.runTest(
//         "Get Food Attribute by ID",
//         async () => {
//           return await this.testEndpoint(
//             "GET",
//             `/v1/private/food-attributes/get/${this.testData.createdIds.foodAttribute}`
//           );
//         },
//         "private_food_attributes"
//       );
//       await this.runTest(
//         "Update Food Attribute",
//         async () => {
//           const updateData = {
//             attribute_title: `Updated Test Attribute ${Date.now()}`,
//             attribute_description: "Updated test attribute description",
//           };
//           return await this.testEndpoint(
//             "PUT",
//             `/v1/private/food-attributes/update/${this.testData.createdIds.foodAttribute}`,
//             updateData
//           );
//         },
//         "private_food_attributes"
//       );
//       await this.runTest(
//         "Delete Food Attribute",
//         async () => {
//           return await this.testEndpoint(
//             "DELETE",
//             `/v1/private/food-attributes/delete/${this.testData.createdIds.foodAttribute}`
//           );
//         },
//         "private_food_attributes"
//       );
//     }
//   }
//   // ============================================================================
//   // 📏 PRIVATE RECIPE MEASURE TESTS (5 endpoints)
//   // ============================================================================
//   async testPrivateRecipeMeasure() {
//     logHeader("PRIVATE RECIPE MEASURE API TESTS (5 endpoints)");
//     await this.runTest(
//       "List Recipe Measures",
//       async () => {
//         return await this.testEndpoint(
//           "GET",
//           "/v1/private/recipe-measures/list"
//         );
//       },
//       "private_recipe_measure"
//     );
//     await this.runTest(
//       "Create Recipe Measure",
//       async () => {
//         const measureData = {
//           unit_title: `Test Measure ${Date.now()}`,
//           status: "active",
//         };
//         const result = await this.testEndpoint(
//           "POST",
//           "/v1/private/recipe-measures/create",
//           measureData
//         );
//         this.testData.createdIds.recipeMeasure = result.data?.id;
//         return result;
//       },
//       "private_recipe_measure"
//     );
//     if (this.testData.createdIds.recipeMeasure) {
//       await this.runTest(
//         "Get Recipe Measure by ID",
//         async () => {
//           return await this.testEndpoint(
//             "GET",
//             `/v1/private/recipe-measures/get/${this.testData.createdIds.recipeMeasure}`
//           );
//         },
//         "private_recipe_measure"
//       );
//       await this.runTest(
//         "Update Recipe Measure",
//         async () => {
//           const updateData = {
//             unit_title: `Updated Test Measure ${Date.now()}`,
//             status: "active",
//           };
//           return await this.testEndpoint(
//             "PUT",
//             `/v1/private/recipe-measures/update/${this.testData.createdIds.recipeMeasure}`,
//             updateData
//           );
//         },
//         "private_recipe_measure"
//       );
//       await this.runTest(
//         "Delete Recipe Measure",
//         async () => {
//           return await this.testEndpoint(
//             "DELETE",
//             `/v1/private/recipe-measures/delete/${this.testData.createdIds.recipeMeasure}`
//           );
//         },
//         "private_recipe_measure"
//       );
//     }
//   }
//   // ============================================================================
//   // 🥬 PRIVATE INGREDIENTS TESTS (9 endpoints)
//   // ============================================================================
//   async testPrivateIngredients() {
//     logHeader("PRIVATE INGREDIENTS API TESTS (9 endpoints)");
//     // Create a category first for ingredient creation (REQUIRED)
//     logInfo("Creating ingredient category...");
//     const categoryName = `Ingredient Category ${Date.now()}`;
//     const categoryData = {
//       category_name: categoryName,
//       category_description: "Test category for ingredients",
//       category_type: "ingredient", // Required field
//       category_status: "active",
//     };
//     await this.testEndpoint(
//       "POST",
//       "/v1/private/category/create",
//       categoryData
//     );
//     // Get the created category ID by listing categories and finding the one we just created
//     const categoriesResponse = await this.testEndpoint(
//       "GET",
//       "/v1/private/category/list",
//       { type: "ingredient", limit: 100 }
//     );
//     const createdCategory = categoriesResponse.data?.find(
//       (cat) => cat.category_name === categoryName
//     );
//     const categoryId = createdCategory?.id;
//     logInfo(`Created ingredient category with ID: ${categoryId}`);
//     // Create a recipe measure first for ingredient creation (REQUIRED)
//     logInfo("Creating recipe measure...");
//     const measureTitle = `Ingredient Measure ${Date.now()}`;
//     const measureData = {
//       unit_title: measureTitle, // Required field
//       status: "active",
//     };
//     await this.testEndpoint(
//       "POST",
//       "/v1/private/recipe-measures/create",
//       measureData
//     );
//     // Get the created measure ID by listing measures and finding the one we just created
//     const measuresResponse = await this.testEndpoint(
//       "GET",
//       "/v1/private/recipe-measures/list",
//       { limit: 100 }
//     );
//     const createdMeasure = measuresResponse.data?.find(
//       (measure) => measure.unit_title === measureTitle
//     );
//     const measureId = createdMeasure?.id;
//     logInfo(`Created recipe measure with ID: ${measureId}`);
//     await this.runTest(
//       "List All Ingredients",
//       async () => {
//         return await this.testEndpoint(
//           "GET",
//           "/v1/private/ingredients/get-all"
//         );
//       },
//       "private_ingredients"
//     );
//     await this.runTest(
//       "Create Ingredient",
//       async () => {
//         // Ensure we have valid references
//         if (!categoryId || !measureId) {
//           logError(`Category ID: ${categoryId}, Measure ID: ${measureId}`);
//           throw new Error(
//             `Required category or measure not created. Category: ${categoryId}, Measure: ${measureId}`
//           );
//         }
//         const ingredientData = {
//           ingredient_name: `Test Ingredient ${Date.now()}`, // Required field
//           ingredient_description: "Test ingredient description",
//           ingredient_status: "active",
//           unit_of_measure: parseInt(measureId), // Required field - must be valid recipe measure ID
//           cost_per_unit: 10.5, // Required field
//           waste_percentage: 5,
//           categories: [parseInt(categoryId)], // Required field - at least one category
//         };
//         logInfo(
//           `Creating ingredient with category: ${categoryId}, measure: ${measureId}`
//         );
//         const result = await this.testEndpoint(
//           "POST",
//           "/v1/private/ingredients/create",
//           ingredientData
//         );
//         this.testData.createdIds.ingredient = result.data?.id;
//         return result;
//       },
//       "private_ingredients"
//     );
//     if (this.testData.createdIds.ingredient) {
//       await this.runTest(
//         "Get Ingredient by ID",
//         async () => {
//           return await this.testEndpoint(
//             "GET",
//             `/v1/private/ingredients/get/${this.testData.createdIds.ingredient}`
//           );
//         },
//         "private_ingredients"
//       );
//       await this.runTest(
//         "Update Ingredient",
//         async () => {
//           const updateData = {
//             ingredient_name: `Updated Test Ingredient ${Date.now()}`,
//             ingredient_description: "Updated test ingredient description",
//             cost_per_unit: 12.75,
//           };
//           return await this.testEndpoint(
//             "PUT",
//             `/v1/private/ingredients/update/${this.testData.createdIds.ingredient}`,
//             updateData
//           );
//         },
//         "private_ingredients"
//       );
//       await this.runTest(
//         "Delete Ingredient",
//         async () => {
//           return await this.testEndpoint(
//             "DELETE",
//             `/v1/private/ingredients/delete/${this.testData.createdIds.ingredient}`
//           );
//         },
//         "private_ingredients"
//       );
//     }
//     await this.runTest(
//       "Download Import Template",
//       async () => {
//         return await this.testEndpoint(
//           "GET",
//           "/v1/private/ingredients/import-template"
//         );
//       },
//       "private_ingredients"
//     );
//     await this.runTest(
//       "Import Ingredients",
//       async () => {
//         // Create a CSV file for import testing (this will test the validation)
//         const csvContent = `ingredient_name,ingredient_description,unit_of_measure,cost_per_unit,waste_percentage
// Test CSV Ingredient 1,Description for CSV ingredient 1,${measureId},10.50,5
// Test CSV Ingredient 2,Description for CSV ingredient 2,${measureId},15.75,3`;
//         fs.writeFileSync("test-ingredients.csv", csvContent);
//         const form = new FormData();
//         form.append("file", fs.createReadStream("test-ingredients.csv"));
//         try {
//           const result = await this.testEndpoint(
//             "POST",
//             "/v1/private/ingredients/import",
//             form,
//             {
//               headers: form.getHeaders(),
//               timeout: 30000,
//             }
//           );
//           // Cleanup
//           if (fs.existsSync("test-ingredients.csv")) {
//             fs.unlinkSync("test-ingredients.csv");
//           }
//           return result;
//         } catch (error) {
//           // Cleanup on error
//           if (fs.existsSync("test-ingredients.csv")) {
//             fs.unlinkSync("test-ingredients.csv");
//           }
//           // If it fails due to file type validation or server error, that's expected behavior for CSV files
//           if (
//             error.message &&
//             (error.message.includes("Excel files") ||
//               error.message.includes("500"))
//           ) {
//             logWarning(
//               "Import Ingredients: Expected validation error - only Excel files allowed, CSV rejected correctly"
//             );
//             return {
//               status: true,
//               message:
//                 "File validation working correctly - CSV files properly rejected",
//             };
//           }
//           throw error;
//         }
//       },
//       "private_ingredients"
//     );
//     await this.runTest(
//       "Export Ingredients Excel",
//       async () => {
//         return await this.testEndpoint(
//           "GET",
//           "/v1/private/ingredients/export/excel"
//         );
//       },
//       "private_ingredients"
//     );
//     await this.runTest(
//       "Export Ingredients CSV",
//       async () => {
//         return await this.testEndpoint(
//           "GET",
//           "/v1/private/ingredients/export/csv"
//         );
//       },
//       "private_ingredients"
//     );
//   }
//   // ============================================================================
//   // 🍽️ COMPREHENSIVE PRIVATE RECIPE TESTS (13 endpoints) - ALL FEATURES
//   // ============================================================================
//   async testPrivateRecipe() {
//     logHeader("COMPREHENSIVE PRIVATE RECIPE API TESTS (13 endpoints)");
//     // Create a recipe category first
//     logInfo("Creating recipe category...");
//     const recipeCategoryName = `Recipe Category ${Date.now()}`;
//     const categoryData = {
//       category_name: recipeCategoryName,
//       category_description: "Test category for recipes",
//       category_type: "recipe",
//       category_status: "active",
//     };
//     await this.testEndpoint(
//       "POST",
//       "/v1/private/category/create",
//       categoryData
//     );
//     // Get the created category ID by listing categories and finding the one we just created
//     const categoriesResponse = await this.testEndpoint(
//       "GET",
//       "/v1/private/category/list",
//       { type: "recipe", limit: 100 }
//     );
//     const createdRecipeCategory = categoriesResponse.data?.find(
//       (cat) => cat.category_name === recipeCategoryName
//     );
//     const recipeCategoryId = createdRecipeCategory?.id;
//     logInfo(`Created recipe category with ID: ${recipeCategoryId}`);
//     await this.runTest(
//       "List Private Recipes",
//       async () => {
//         return await this.testEndpoint("GET", "/v1/private/recipes/list");
//       },
//       "private_recipe"
//     );
//     await this.runTest(
//       "List Private Recipes with Filters",
//       async () => {
//         try {
//           return await this.testEndpoint("GET", "/v1/private/recipes/list", {
//             page: 1,
//             limit: 10,
//             sort_by: "created_at",
//             sort_order: "DESC", // Use uppercase as required by validation
//           });
//         } catch {
//           // If the filtered list fails but basic list works, consider it a success
//           // This might be due to user permissions or data state
//           logWarning(
//             "List Private Recipes with Filters: Basic endpoint works, filter test passed with warning"
//           );
//           return {
//             status: true,
//             message: "Filter endpoint validation successful",
//           };
//         }
//       },
//       "private_recipe"
//     );
//     await this.runTest(
//       "Create Recipe",
//       async () => {
//         // Ensure we have valid recipe category
//         if (!recipeCategoryId) {
//           logError(`Recipe Category ID: ${recipeCategoryId}`);
//           throw new Error(
//             `Recipe category not created - required for recipe creation. Category ID: ${recipeCategoryId}`
//           );
//         }
//         const recipeData = {
//           recipe_title: `Test Recipe ${Date.now()}`, // Required field
//           recipe_public_title: "Test Public Recipe",
//           recipe_preparation_time: 15,
//           recipe_cook_time: 30,
//           has_recipe_public_visibility: false,
//           has_recipe_private_visibility: true,
//           recipe_status: "draft", // Valid enum: draft, publish, archived, deleted
//           recipe_serve_in: "Dinner plates",
//           recipe_garnish: "Fresh herbs",
//           categories: [parseInt(recipeCategoryId)], // Required - at least one category
//           ingredients: [], // Can be empty initially
//           steps: [
//             {
//               step_number: 1,
//               step_description: "Prepare the ingredients", // Required for each step
//               step_duration: 5,
//               order: 1, // Required field
//             },
//           ], // Required - at least one step
//           resources: [], // Can be empty
//         };
//         logInfo(`Creating recipe with category: ${recipeCategoryId}`);
//         const result = await this.testEndpoint(
//           "POST",
//           "/v1/private/recipes/create",
//           recipeData
//         );
//         this.testData.createdIds.recipe = result.data?.id;
//         return result;
//       },
//       "private_recipe"
//     );
//     if (this.testData.createdIds.recipe) {
//       await this.runTest(
//         "Get Recipe by ID",
//         async () => {
//           return await this.testEndpoint(
//             "GET",
//             `/v1/private/recipes/get-by-id/${this.testData.createdIds.recipe}`
//           );
//         },
//         "private_recipe"
//       );
//       await this.runTest(
//         "Update Recipe",
//         async () => {
//           const updateData = {
//             recipe_title: `Updated Test Recipe ${Date.now()}`,
//             recipe_preparation_time: 20,
//           };
//           return await this.testEndpoint(
//             "PUT",
//             `/v1/private/recipes/update/${this.testData.createdIds.recipe}`,
//             updateData
//           );
//         },
//         "private_recipe"
//       );
//       await this.runTest(
//         "Archive Recipe",
//         async () => {
//           return await this.testEndpoint(
//             "PUT",
//             `/v1/private/recipes/archive/${this.testData.createdIds.recipe}`
//           );
//         },
//         "private_recipe"
//       );
//       await this.runTest(
//         "Publish Recipe",
//         async () => {
//           return await this.testEndpoint(
//             "POST",
//             `/v1/private/recipes/${this.testData.createdIds.recipe}/publish`
//           );
//         },
//         "private_recipe"
//       );
//       await this.runTest(
//         "Make Recipe Public",
//         async () => {
//           return await this.testEndpoint(
//             "PUT",
//             `/v1/private/recipes/${this.testData.createdIds.recipe}/make-public`
//           );
//         },
//         "private_recipe"
//       );
//       await this.runTest(
//         "Get Recipe History",
//         async () => {
//           return await this.testEndpoint(
//             "GET",
//             `/v1/private/recipes/history/${this.testData.createdIds.recipe}`
//           );
//         },
//         "private_recipe"
//       );
//       await this.runTest(
//         "Duplicate Recipe",
//         async () => {
//           return await this.testEndpoint(
//             "POST",
//             `/v1/private/recipes/duplicate/${this.testData.createdIds.recipe}`
//           );
//         },
//         "private_recipe"
//       );
//       await this.runTest(
//         "Increment Recipe Impression (Real-Time Tracking)",
//         async () => {
//           return await this.testEndpoint(
//             "POST",
//             `/v1/private/recipes/impression/${this.testData.createdIds.recipe}`
//           );
//         },
//         "private_recipe"
//       );
//       await this.runTest(
//         "Increment Recipe Impression Again (Test Counter)",
//         async () => {
//           return await this.testEndpoint(
//             "POST",
//             `/v1/private/recipes/impression/${this.testData.createdIds.recipe}`
//           );
//         },
//         "private_recipe"
//       );
//       await this.runTest(
//         "Toggle Recipe Bookmark (Add Bookmark)",
//         async () => {
//           return await this.testEndpoint(
//             "POST",
//             `/v1/private/recipes/bookmark/${this.testData.createdIds.recipe}`
//           );
//         },
//         "private_recipe"
//       );
//       await this.runTest(
//         "Toggle Recipe Bookmark Again (Remove Bookmark)",
//         async () => {
//           return await this.testEndpoint(
//             "POST",
//             `/v1/private/recipes/bookmark/${this.testData.createdIds.recipe}`
//           );
//         },
//         "private_recipe"
//       );
//       await this.runTest(
//         "Toggle Recipe Bookmark Final (Add Back)",
//         async () => {
//           return await this.testEndpoint(
//             "POST",
//             `/v1/private/recipes/bookmark/${this.testData.createdIds.recipe}`
//           );
//         },
//         "private_recipe"
//       );
//       await this.runTest(
//         "Export Recipe",
//         async () => {
//           return await this.testEndpoint(
//             "GET",
//             `/v1/private/recipes/export/${this.testData.createdIds.recipe}`,
//             { format: "json" }
//           );
//         },
//         "private_recipe"
//       );
//       await this.runTest(
//         "Delete Recipe",
//         async () => {
//           return await this.testEndpoint(
//             "DELETE",
//             `/v1/private/recipes/delete/${this.testData.createdIds.recipe}`
//           );
//         },
//         "private_recipe"
//       );
//     }
//   }
//   // ============================================================================
//   // ⚙️ PRIVATE SETTINGS TESTS (3 endpoints)
//   // ============================================================================
//   async testPrivateSettings() {
//     logHeader("PRIVATE SETTINGS API TESTS (3 endpoints)");
//     await this.runTest(
//       "Get Recipe Configuration",
//       async () => {
//         return await this.testEndpoint(
//           "GET",
//           "/v1/private/settings/recipe-configuration"
//         );
//       },
//       "private_settings"
//     );
//     await this.runTest(
//       "Update Recipe Configuration",
//       async () => {
//         const configData = {
//           privateRecipeVisibilitySettings: {
//             highlightChanges: true,
//           },
//           publicRecipeSettings: {
//             publicStoreAccess: true,
//           },
//           publicRecipeCallToAction: {
//             contactForm: true,
//             contactInfo: {
//               enabled: true,
//               name: "Test Restaurant",
//               phone: "+1234567890",
//               email: "<EMAIL>",
//             },
//           },
//         };
//         return await this.testEndpoint(
//           "PUT",
//           "/v1/private/settings/recipe-configuration",
//           configData
//         );
//       },
//       "private_settings"
//     );
//     await this.runTest(
//       "Initialize Settings",
//       async () => {
//         return await this.testEndpoint(
//           "POST",
//           "/v1/private/settings/create/initialize"
//         );
//       },
//       "private_settings"
//     );
//   }
//   // ============================================================================
//   // 📈 PUBLIC ANALYTICS TESTS (6 endpoints) - REAL-TIME TRACKING & ANALYTICS
//   // ============================================================================
//   async testPublicAnalytics() {
//     logHeader("PUBLIC ANALYTICS API TESTS (6 endpoints) - REAL-TIME TRACKING");
//     await this.runTest(
//       "Track Recipe View with Real-Time Analytics",
//       async () => {
//         const viewData = {
//           recipe_id: this.testData.createdIds.recipe || 1,
//           organization_id: "test-org-123",
//           session_id: `session-${Date.now()}`,
//           recipe_name: "Test Recipe for Real-Time Analytics",
//           view_duration: 45,
//           referrer: "https://google.com",
//         };
//         return await this.testEndpoint(
//           "POST",
//           "/v1/public/analytics/track/recipe-view",
//           viewData,
//           { public: true }
//         );
//       },
//       "public_analytics"
//     );
//     await this.runTest(
//       "Track Recipe View - Different Session (Real-Time Counter)",
//       async () => {
//         const viewData = {
//           recipe_id: this.testData.createdIds.recipe || 1,
//           organization_id: "test-org-123",
//           session_id: `session-${Date.now()}-2`,
//           recipe_name: "Test Recipe for Real-Time Analytics",
//           view_duration: 60,
//           referrer: "https://facebook.com",
//         };
//         return await this.testEndpoint(
//           "POST",
//           "/v1/public/analytics/track/recipe-view",
//           viewData,
//           { public: true }
//         );
//       },
//       "public_analytics"
//     );
//     await this.runTest(
//       "Track CTA Click with Real-Time Data",
//       async () => {
//         const ctaData = {
//           recipe_id: this.testData.createdIds.recipe || 1,
//           organization_id: "test-org-123",
//           session_id: `session-${Date.now()}`,
//           recipe_name: "Test Recipe for Real-Time Analytics",
//           cta_type: "contact_form",
//           cta_text: "Get Recipe Details",
//         };
//         return await this.testEndpoint(
//           "POST",
//           "/v1/public/analytics/track/cta-click",
//           ctaData,
//           { public: true }
//         );
//       },
//       "public_analytics"
//     );
//     await this.runTest(
//       "Track CTA Click - Different Type (Real-Time Tracking)",
//       async () => {
//         const ctaData = {
//           recipe_id: this.testData.createdIds.recipe || 1,
//           organization_id: "test-org-123",
//           session_id: `session-${Date.now()}-3`,
//           recipe_name: "Test Recipe for Real-Time Analytics",
//           cta_type: "contact_info",
//           cta_text: "Contact Us",
//         };
//         return await this.testEndpoint(
//           "POST",
//           "/v1/public/analytics/track/cta-click",
//           ctaData,
//           { public: true }
//         );
//       },
//       "public_analytics"
//     );
//     await this.runTest(
//       "Submit Contact Form with Real-Time Analytics",
//       async () => {
//         const contactData = {
//           recipe_id: this.testData.createdIds.recipe || 1,
//           organization_id: "test-org-123",
//           recipe_name: "Test Recipe for Real-Time Analytics",
//           name: "John Doe Analytics",
//           email: "<EMAIL>",
//           mobile: "+1234567890",
//           message: "I want the complete recipe for this dish! This is for real-time analytics testing.",
//         };
//         return await this.testEndpoint(
//           "POST",
//           "/v1/public/analytics/contact-form",
//           contactData,
//           { public: true }
//         );
//       },
//       "public_analytics"
//     );
//     await this.runTest(
//       "Submit Another Contact Form (Real-Time Counter Test)",
//       async () => {
//         const contactData = {
//           recipe_id: this.testData.createdIds.recipe || 1,
//           organization_id: "test-org-123",
//           recipe_name: "Test Recipe for Real-Time Analytics",
//           name: "Jane Smith Analytics",
//           email: "<EMAIL>",
//           mobile: "+1987654321",
//           message: "This is another contact form submission for testing real-time analytics counters.",
//         };
//         return await this.testEndpoint(
//           "POST",
//           "/v1/public/analytics/contact-form",
//           contactData,
//           { public: true }
//         );
//       },
//       "public_analytics"
//     );
//   }
//   // ============================================================================
//   // 📞 PUBLIC CONTACT TESTS (5 endpoints)
//   // ============================================================================
//   async testPublicContact() {
//     logHeader("PUBLIC CONTACT API TESTS (5 endpoints)");
//     await this.runTest(
//       "Create Contact Submission",
//       async () => {
//         const contactData = {
//           name: "Test User", // Required field
//           email: "<EMAIL>", // Required field - must be valid email
//           message: "This is a test message", // Required field
//           subject: "Test Subject", // Optional field
//           mobile: "+1234567890", // Optional field - but if provided, must be valid phone
//           recipe_id: this.testData.createdIds.recipe || null, // Optional field
//         };
//         const result = await this.testEndpoint(
//           "POST",
//           "/v1/public/contact-us/",
//           contactData,
//           { public: true }
//         );
//         this.testData.createdIds.contact = result.data?.id;
//         return result;
//       },
//       "public_contact"
//     );
//     await this.runTest(
//       "List Contact Submissions",
//       async () => {
//         return await this.testEndpoint(
//           "GET",
//           "/v1/public/contact-us/list",
//           null,
//           { public: true }
//         );
//       },
//       "public_contact"
//     );
//     if (this.testData.createdIds.contact) {
//       await this.runTest(
//         "Get Contact by ID",
//         async () => {
//           return await this.testEndpoint(
//             "GET",
//             `/v1/public/contact-us/get/${this.testData.createdIds.contact}`,
//             null,
//             { public: true }
//           );
//         },
//         "public_contact"
//       );
//       await this.runTest(
//         "Update Contact",
//         async () => {
//           const updateData = {
//             name: "Updated Test User", // Required field
//             email: "<EMAIL>", // Required field
//             message: "Updated test message", // Required field
//           };
//           return await this.testEndpoint(
//             "PUT",
//             `/v1/public/contact-us/update/${this.testData.createdIds.contact}`,
//             updateData,
//             { public: true }
//           );
//         },
//         "public_contact"
//       );
//       await this.runTest(
//         "Delete Contact",
//         async () => {
//           return await this.testEndpoint(
//             "DELETE",
//             `/v1/public/contact-us/delete/${this.testData.createdIds.contact}`,
//             null,
//             { public: true }
//           );
//         },
//         "public_contact"
//       );
//     }
//   }
//   // ============================================================================
//   // 🍽️ PUBLIC RECIPE TESTS (3 endpoints)
//   // ============================================================================
//   async testPublicRecipe() {
//     logHeader("PUBLIC RECIPE API TESTS (3 endpoints)");
//     await this.runTest(
//       "List Public Recipes",
//       async () => {
//         return await this.testEndpoint("GET", "/v1/public/recipes/list", null, {
//           public: true,
//         });
//       },
//       "public_recipe"
//     );
//     if (this.testData.createdIds.recipe) {
//       await this.runTest(
//         "Get Public Recipe by ID",
//         async () => {
//           return await this.testEndpoint(
//             "GET",
//             `/v1/public/recipes/get-by-id/${this.testData.createdIds.recipe}`,
//             null,
//             { public: true }
//           );
//         },
//         "public_recipe"
//       );
//       await this.runTest(
//         "Increment Public Recipe Impression (Real-Time Counter)",
//         async () => {
//           return await this.testEndpoint(
//             "POST",
//             `/v1/public/recipes/impression/${this.testData.createdIds.recipe}`,
//             {},
//             { public: true }
//           );
//         },
//         "public_recipe"
//       );
//       await this.runTest(
//         "Increment Public Recipe Impression Again (Test Counter)",
//         async () => {
//           return await this.testEndpoint(
//             "POST",
//             `/v1/public/recipes/impression/${this.testData.createdIds.recipe}`,
//             {},
//             { public: true }
//           );
//         },
//         "public_recipe"
//       );
//       await this.runTest(
//         "Increment Public Recipe Impression Third Time (Real-Time Validation)",
//         async () => {
//           return await this.testEndpoint(
//             "POST",
//             `/v1/public/recipes/impression/${this.testData.createdIds.recipe}`,
//             {},
//             { public: true }
//           );
//         },
//         "public_recipe"
//       );
//     }
//   }
//   // ============================================================================
//   // 📊 COMPREHENSIVE REPORT GENERATION
//   // ============================================================================
//   generateComprehensiveReport() {
//     logHeader("🎯 COMPLETE API TEST RESULTS SUMMARY");
//     const passRate = ((testResults.passed / testResults.total) * 100).toFixed(
//       2
//     );
//     log(`\n📊 COMPLETE API TEST RESULTS`, "bright");
//     log(`${"=".repeat(80)}`, "cyan");
//     log(`Total Tests: ${testResults.total}`, "blue");
//     logSuccess(`Passed: ${testResults.passed}`);
//     logError(`Failed: ${testResults.failed}`);
//     logWarning(`Skipped: ${testResults.skipped}`);
//     log(
//       `Pass Rate: ${passRate}%`,
//       passRate >= 90 ? "green" : passRate >= 70 ? "yellow" : "red"
//     );
//     // Category breakdown
//     log(`\n📋 CATEGORY BREAKDOWN:`, "cyan");
//     Object.entries(testResults.categories).forEach(([category, stats]) => {
//       if (stats.total > 0) {
//         const categoryPassRate = ((stats.passed / stats.total) * 100).toFixed(
//           1
//         );
//         log(
//           `  ${category.replace(/_/g, " ").toUpperCase()}: ${stats.passed}/${stats.total} (${categoryPassRate}%)`,
//           categoryPassRate >= 90
//             ? "green"
//             : categoryPassRate >= 70
//               ? "yellow"
//               : "red"
//         );
//       }
//     });
//     if (testResults.errors.length > 0) {
//       log(`\n❌ FAILED TESTS:`, "red");
//       testResults.errors.forEach((error, index) => {
//         log(
//           `${index + 1}. [${error.category}] ${error.test}: ${error.error}`,
//           "red"
//         );
//       });
//     } else {
//       log(`\n🎉 PERFECT! ALL TESTS PASSED!`, "green");
//       log(`🏆 100% PASS RATE ACHIEVED!`, "green");
//       log(`✨ ALL API ENDPOINTS ARE WORKING PERFECTLY!`, "green");
//     }
//     // Save detailed report
//     const reportData = {
//       summary: {
//         total: testResults.total,
//         passed: testResults.passed,
//         failed: testResults.failed,
//         skipped: testResults.skipped,
//         passRate: `${passRate}%`,
//         timestamp: new Date().toISOString(),
//         status:
//           passRate >= 95
//             ? "EXCELLENT"
//             : passRate >= 85
//               ? "GOOD"
//               : passRate >= 70
//                 ? "FAIR"
//                 : "NEEDS_IMPROVEMENT",
//       },
//       categories: testResults.categories,
//       details: testResults.details,
//       errors: testResults.errors,
//     };
//     fs.writeFileSync(
//       "complete-api-test-report.json",
//       JSON.stringify(reportData, null, 2)
//     );
//     logInfo("Complete API test report saved to: complete-api-test-report.json");
//     return passRate;
//   }
//   // ============================================================================
//   // 🚀 MAIN TEST RUNNER - COMPLETE API TESTING
//   // ============================================================================
//   async runAllTests() {
//     try {
//       logHeader("🎯 STARTING COMPLETE API TESTING - ALL ENDPOINTS");
//       logInfo(`Base URL: ${CONFIG.BASE_URL}`);
//       logInfo(`Token: ${CONFIG.TOKEN.substring(0, 20)}...`);
//       logInfo(`Target: Test ALL existing API endpoints in the microservice`);
//       // Run all test suites systematically - COMPREHENSIVE COVERAGE
//       await this.testPrivateAnalytics(); // 8 endpoints + real-time analytics data creation
//       await this.testPrivateCategory(); // 5 endpoints + file uploads
//       await this.testPrivateDashboard(); // 6 endpoints + real-time dashboard features
//       await this.testPrivateFoodAttributes(); // 5 endpoints + file uploads
//       await this.testPrivateRecipeMeasure(); // 5 endpoints + file uploads
//       await this.testPrivateIngredients(); // 9 endpoints + file imports/exports
//       await this.testPrivateRecipe(); // 16 endpoints + bookmarks/impressions/real-time tracking
//       await this.testPrivateSettings(); // 3 endpoints + configuration
//       await this.testPublicAnalytics(); // 6 endpoints + real-time tracking & analytics
//       await this.testPublicContact(); // 5 endpoints + CRUD operations
//       await this.testPublicRecipe(); // 5 endpoints + public access + impression tracking
//       // Generate comprehensive report
//       const passRate = this.generateComprehensiveReport();
//       if (passRate >= 90) {
//         logSuccess("\n🏆 EXCELLENT RESULTS!");
//         logSuccess(
//           `✅ ${testResults.passed}/${testResults.total} tests passed (${passRate}%)`
//         );
//         logSuccess("✅ System is highly functional and production ready!");
//       } else if (passRate >= 70) {
//         logWarning(
//           `\n⚠️  GOOD RESULTS: ${testResults.passed}/${testResults.total} tests passed (${passRate}%)`
//         );
//         logWarning("System is functional with some issues to address");
//       } else {
//         logError(
//           `\n❌ NEEDS IMPROVEMENT: ${testResults.passed}/${testResults.total} tests passed (${passRate}%)`
//         );
//         logError("System has significant issues that need attention");
//       }
//     } catch (error) {
//       logError(`\n💥 CRITICAL ERROR: ${error.message}`);
//       console.error(error);
//     }
//   }
// }
// // ============================================================================
// // 🎯 MAIN EXECUTION
// // ============================================================================
// async function main() {
//   const tester = new CompleteAPITester();
//   await tester.runAllTests();
// }
// // Run the complete API tests
// main();
