"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const celebrate_1 = require("celebrate");
const createIngredientValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object().keys({
        ingredient_name: celebrate_1.Joi.string().min(2).max(100).trim().required().messages({
            "string.min": "Ingredient name must be at least 2 characters long",
            "string.max": "Ingredient name cannot exceed 100 characters",
            "any.required": "Ingredient name is required",
        }),
        ingredient_description: celebrate_1.Joi.string().max(1000).allow(null, "").messages({
            "string.max": "Description cannot exceed 1000 characters",
        }),
        ingredient_status: celebrate_1.Joi.string()
            .valid("active", "inactive")
            .default("active")
            .messages({
            "any.only": "Status must be either active or inactive",
        }),
        waste_percentage: celebrate_1.Joi.number()
            .min(0)
            .max(100)
            .precision(2)
            .allow(null)
            .messages({
            "number.min": "Waste percentage cannot be negative",
            "number.max": "Waste percentage cannot exceed 100%",
        }),
        unit_of_measure: celebrate_1.Joi.number().positive().required().messages({
            "number.positive": "Unit of measure must be a positive number",
            "any.required": "Unit of measure is required",
        }),
        cost_per_unit: celebrate_1.Joi.number().positive().precision(2).required().messages({
            "number.positive": "Cost per unit must be a positive number",
            "any.required": "Cost per unit is required",
        }),
        categories: celebrate_1.Joi.array()
            .items(celebrate_1.Joi.number().integer().positive())
            .min(1)
            .unique()
            .required()
            .messages({
            "array.min": "At least one category is required",
            "array.unique": "Duplicate categories are not allowed",
            "any.required": "Categories are required",
        }),
        dietary_attributes: celebrate_1.Joi.array()
            .items(celebrate_1.Joi.number().integer().positive())
            .unique()
            .messages({
            "array.unique": "Duplicate dietary attributes are not allowed",
        }),
        allergy_attributes: celebrate_1.Joi.array()
            .items(celebrate_1.Joi.number().integer().positive())
            .unique()
            .messages({
            "array.unique": "Duplicate allergy attributes are not allowed",
        }),
        nutrition_attributes: celebrate_1.Joi.array()
            .items(celebrate_1.Joi.object({
            attribute_id: celebrate_1.Joi.number()
                .integer()
                .positive()
                .required()
                .messages({
                "number.positive": "Nutrition attribute ID must be positive",
                "any.required": "Nutrition attribute ID is required",
            }),
            unit_of_measure: celebrate_1.Joi.string()
                .max(100)
                .allow(null, "")
                .messages({
                "string.max": "Unit of measure cannot exceed 100 characters",
            }),
            unit: celebrate_1.Joi.number().positive().precision(3).allow(null).messages({
                "number.positive": "Unit value must be positive",
            }),
        }))
            .unique("attribute_id")
            .messages({
            "array.unique": "Duplicate nutrition attributes are not allowed",
        }),
        conversions: celebrate_1.Joi.array()
            .items(celebrate_1.Joi.object({
            from_measure: celebrate_1.Joi.number()
                .integer()
                .positive()
                .required()
                .messages({
                "number.positive": "From measure must be positive",
                "any.required": "From measure is required",
            }),
            from_measure_value: celebrate_1.Joi.number()
                .positive()
                .precision(2)
                .required()
                .messages({
                "number.positive": "From measure value must be positive",
                "any.required": "From measure value is required",
            }),
            to_measure: celebrate_1.Joi.number()
                .integer()
                .positive()
                .required()
                .invalid(celebrate_1.Joi.ref("from_measure"))
                .messages({
                "number.positive": "To measure must be positive",
                "any.required": "To measure is required",
                "any.invalid": "To measure cannot be the same as from measure",
            }),
            to_measure_value: celebrate_1.Joi.number()
                .positive()
                .precision(2)
                .required()
                .messages({
                "number.positive": "To measure value must be positive",
                "any.required": "To measure value is required",
            }),
        }))
            .custom((value, helpers) => {
            // Check for duplicate conversions
            const seen = new Set();
            for (const conversion of value) {
                const key = `${conversion.from_measure}-${conversion.to_measure}`;
                if (seen.has(key)) {
                    return helpers.error("array.unique", {
                        message: `Duplicate conversion found: from measure ${conversion.from_measure} to measure ${conversion.to_measure}`,
                    });
                }
                seen.add(key);
            }
            return value;
        })
            .messages({
            "array.unique": "Duplicate conversions are not allowed",
        }),
    }),
});
const updateIngredientValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object().keys({
        ingredient_name: celebrate_1.Joi.string().min(2).max(100).trim().messages({
            "string.min": "Ingredient name must be at least 2 characters long",
            "string.max": "Ingredient name cannot exceed 100 characters",
        }),
        ingredient_description: celebrate_1.Joi.string().max(1000).allow(null, "").messages({
            "string.max": "Description cannot exceed 1000 characters",
        }),
        ingredient_status: celebrate_1.Joi.string().valid("active", "inactive").messages({
            "any.only": "Status must be either active or inactive",
        }),
        waste_percentage: celebrate_1.Joi.number()
            .min(0)
            .max(100)
            .precision(2)
            .allow(null)
            .messages({
            "number.min": "Waste percentage cannot be negative",
            "number.max": "Waste percentage cannot exceed 100%",
        }),
        unit_of_measure: celebrate_1.Joi.number().integer().positive().messages({
            "number.positive": "Unit of measure must be a positive number",
        }),
        cost_per_unit: celebrate_1.Joi.number().positive().precision(2).messages({
            "number.positive": "Cost per unit must be a positive number",
        }),
        categories: celebrate_1.Joi.array()
            .items(celebrate_1.Joi.number().integer().positive())
            .min(1)
            .unique()
            .messages({
            "array.min": "At least one category is required",
            "array.unique": "Duplicate categories are not allowed",
        }),
        dietary_attributes: celebrate_1.Joi.array()
            .items(celebrate_1.Joi.number().integer().positive())
            .unique()
            .messages({
            "array.unique": "Duplicate dietary attributes are not allowed",
        }),
        allergy_attributes: celebrate_1.Joi.array()
            .items(celebrate_1.Joi.number().integer().positive())
            .unique()
            .messages({
            "array.unique": "Duplicate allergy attributes are not allowed",
        }),
        nutrition_attributes: celebrate_1.Joi.array()
            .items(celebrate_1.Joi.object({
            attribute_id: celebrate_1.Joi.number()
                .integer()
                .positive()
                .required()
                .messages({
                "number.positive": "Nutrition attribute ID must be positive",
                "any.required": "Nutrition attribute ID is required",
            }),
            unit_of_measure: celebrate_1.Joi.string()
                .max(100)
                .allow(null, "")
                .messages({
                "string.max": "Unit of measure cannot exceed 100 characters",
            }),
            unit: celebrate_1.Joi.number().positive().precision(3).allow(null).messages({
                "number.positive": "Unit value must be positive",
            }),
        }))
            .unique("attribute_id")
            .messages({
            "array.unique": "Duplicate nutrition attributes are not allowed",
        }),
        conversions: celebrate_1.Joi.array()
            .items(celebrate_1.Joi.object({
            from_measure: celebrate_1.Joi.number()
                .integer()
                .positive()
                .required()
                .messages({
                "number.positive": "From measure must be positive",
                "any.required": "From measure is required",
            }),
            from_measure_value: celebrate_1.Joi.number()
                .positive()
                .precision(2)
                .required()
                .messages({
                "number.positive": "From measure value must be positive",
                "any.required": "From measure value is required",
            }),
            to_measure: celebrate_1.Joi.number()
                .integer()
                .positive()
                .required()
                .invalid(celebrate_1.Joi.ref("from_measure"))
                .messages({
                "number.positive": "To measure must be positive",
                "any.required": "To measure is required",
                "any.invalid": "To measure cannot be the same as from measure",
            }),
            to_measure_value: celebrate_1.Joi.number()
                .positive()
                .precision(2)
                .required()
                .messages({
                "number.positive": "To measure value must be positive",
                "any.required": "To measure value is required",
            }),
        }))
            .custom((value, helpers) => {
            // Check for duplicate conversions
            const seen = new Set();
            for (const conversion of value) {
                const key = `${conversion.from_measure}-${conversion.to_measure}`;
                if (seen.has(key)) {
                    return helpers.error("array.unique", {
                        message: `Duplicate conversion found: from measure ${conversion.from_measure} to measure ${conversion.to_measure}`,
                    });
                }
                seen.add(key);
            }
            return value;
        })
            .messages({
            "array.unique": "Duplicate conversions are not allowed",
        }),
    }),
});
const getIngredientValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().integer().positive().required().messages({
            "number.positive": "Ingredient ID must be a positive number",
            "any.required": "Ingredient ID is required",
        }),
    }),
});
const importIngredientsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object()
        .keys({
    // File validation will be handled by multer middleware
    // This validator can be used for additional body parameters if needed
    })
        .unknown(true), // Allow unknown fields for file upload
});
const getIngredientsListValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: celebrate_1.Joi.object().keys({
        page: celebrate_1.Joi.number()
            .integer()
            .min(1)
            .default(1)
            .messages({
            'number.min': 'Page must be at least 1'
        }),
        size: celebrate_1.Joi.number()
            .integer()
            .min(1)
            .max(100)
            .default(10)
            .messages({
            'number.min': 'Size must be at least 1',
            'number.max': 'Size cannot exceed 100'
        }),
        search: celebrate_1.Joi.string()
            .max(255)
            .allow('')
            .messages({
            'string.max': 'Search term cannot exceed 255 characters'
        }),
        ingredient_status: celebrate_1.Joi.string()
            .valid('active', 'inactive', 'all')
            .default('active')
            .messages({
            'any.only': 'Status must be active, inactive, or all'
        }),
        category: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().integer().positive(), celebrate_1.Joi.string().pattern(/^\d+(,\d+)*$/))
            .messages({
            'alternatives.match': 'Category must be a positive number or comma-separated numbers'
        }),
        sort_by: celebrate_1.Joi.string()
            .valid('ingredient_name', 'cost_per_unit', 'waste_percentage', 'created_at', 'updated_at')
            .default('ingredient_name')
            .messages({
            'any.only': 'Sort by must be one of: ingredient_name, cost_per_unit, waste_percentage, created_at, updated_at'
        }),
        sort_order: celebrate_1.Joi.string()
            .valid('ASC', 'DESC', 'asc', 'desc')
            .default('ASC')
            .messages({
            'any.only': 'Sort order must be ASC or DESC'
        }),
        download: celebrate_1.Joi.string()
            .valid('excel', 'csv')
            .messages({
            'any.only': 'Download format must be excel or csv'
        })
    })
});
const deleteIngredientValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number()
            .integer()
            .positive()
            .required()
            .messages({
            'number.positive': 'Ingredient ID must be a positive number',
            'any.required': 'Ingredient ID is required'
        })
    })
});
// Default export object
exports.default = {
    createIngredientValidator,
    updateIngredientValidator,
    getIngredientValidator,
    getIngredientsListValidator,
    deleteIngredientValidator,
    importIngredientsValidator,
};
