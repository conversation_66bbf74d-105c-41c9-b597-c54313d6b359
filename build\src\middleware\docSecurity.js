"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const auth_1 = __importDefault(require("./auth"));
const secureDocs = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Ensure userAuth is awaited if it is an async function or called properly as middleware
        yield (0, auth_1.default)(req, res, next);
    }
    catch (error) {
        console.error(error); // Use console.error for errors
        return res.status(401).send({ status: false, message: error.message });
    }
});
exports.default = secureDocs;
