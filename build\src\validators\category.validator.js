"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const celebrate_1 = require("celebrate");
const Category_1 = require("../models/Category");
const createCategoryValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object()
        .keys({
        category_name: celebrate_1.Joi.string().min(2).max(100).required(),
        category_description: celebrate_1.Joi.string().allow(null, "").optional(),
        category_type: celebrate_1.Joi.string()
            .valid(...Object.values(Category_1.CategoryType))
            .required(),
        category_status: celebrate_1.Joi.string()
            .valid(...Object.values(Category_1.CategoryStatus))
            .optional(),
        is_system_category: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.boolean(), celebrate_1.Joi.string().valid("true", "false"))
            .optional(),
    })
        .unknown(true), // Allow file uploads and other fields
});
const updateCategoryValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object()
        .keys({
        category_name: celebrate_1.Joi.string().min(2).max(100).optional(),
        category_description: celebrate_1.Joi.string().allow(null, "").optional(),
        category_type: celebrate_1.Joi.string()
            .valid(...Object.values(Category_1.CategoryType))
            .optional(),
        category_status: celebrate_1.Joi.string()
            .valid(...Object.values(Category_1.CategoryStatus))
            .optional(),
        is_system_category: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.boolean(), celebrate_1.Joi.string().valid("true", "false"))
            .optional(),
    })
        .unknown(true), // Allow file uploads and other fields
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().integer().min(1).required(),
    }),
});
const deleteCategoryValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
});
const getCategoryValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
});
const getCategoriesListValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: celebrate_1.Joi.object().keys({
        page: celebrate_1.Joi.number().integer().min(1).default(1),
        limit: celebrate_1.Joi.number().integer().min(1).max(100).default(10),
        search: celebrate_1.Joi.string().max(100).optional(),
        status: celebrate_1.Joi.string()
            .valid(...Object.values(Category_1.CategoryStatus))
            .optional(),
        type: celebrate_1.Joi.string()
            .valid(...Object.values(Category_1.CategoryType))
            .optional(),
        sort_by: celebrate_1.Joi.string()
            .valid("category_name", "created_at", "updated_at")
            .default("created_at"),
        sort_order: celebrate_1.Joi.string().valid("asc", "desc").default("desc"),
    }),
});
// Default export object
exports.default = {
    createCategoryValidator,
    updateCategoryValidator,
    deleteCategoryValidator,
    getCategoryValidator,
    getCategoriesListValidator,
};
