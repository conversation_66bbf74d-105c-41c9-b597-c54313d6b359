"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Recipe = exports.RecipeComplexityLevel = exports.RecipeServeIn = exports.RecipeYieldUnit = exports.RecipeServingMethod = exports.RecipeStatus = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var RecipeStatus;
(function (RecipeStatus) {
    RecipeStatus["draft"] = "draft";
    RecipeStatus["publish"] = "publish";
    RecipeStatus["archived"] = "archived";
    RecipeStatus["deleted"] = "deleted";
})(RecipeStatus || (exports.RecipeStatus = RecipeStatus = {}));
var RecipeServingMethod;
(function (RecipeServingMethod) {
    RecipeServingMethod["individual_plates"] = "individual_plates";
    RecipeServingMethod["family_style"] = "family_style";
    RecipeServingMethod["buffet_style"] = "buffet_style";
    RecipeServingMethod["shared_platters"] = "shared_platters";
    RecipeServingMethod["tasting_portions"] = "tasting_portions";
    RecipeServingMethod["cocktail_style"] = "cocktail_style";
    RecipeServingMethod["picnic_style"] = "picnic_style";
})(RecipeServingMethod || (exports.RecipeServingMethod = RecipeServingMethod = {}));
var RecipeYieldUnit;
(function (RecipeYieldUnit) {
    RecipeYieldUnit["servings"] = "servings";
    RecipeYieldUnit["portions"] = "portions";
    RecipeYieldUnit["cups"] = "cups";
    RecipeYieldUnit["liters"] = "liters";
    RecipeYieldUnit["pieces"] = "pieces";
    RecipeYieldUnit["slices"] = "slices";
    RecipeYieldUnit["bowls"] = "bowls";
    RecipeYieldUnit["plates"] = "plates";
})(RecipeYieldUnit || (exports.RecipeYieldUnit = RecipeYieldUnit = {}));
var RecipeServeIn;
(function (RecipeServeIn) {
    RecipeServeIn["dinner_plates"] = "dinner_plates";
    RecipeServeIn["salad_plates"] = "salad_plates";
    RecipeServeIn["bowls"] = "bowls";
    RecipeServeIn["soup_bowls"] = "soup_bowls";
    RecipeServeIn["ramekins"] = "ramekins";
    RecipeServeIn["glasses"] = "glasses";
    RecipeServeIn["mugs"] = "mugs";
    RecipeServeIn["platters"] = "platters";
    RecipeServeIn["serving_dishes"] = "serving_dishes";
})(RecipeServeIn || (exports.RecipeServeIn = RecipeServeIn = {}));
var RecipeComplexityLevel;
(function (RecipeComplexityLevel) {
    RecipeComplexityLevel["low"] = "low";
    RecipeComplexityLevel["medium"] = "medium";
    RecipeComplexityLevel["hard"] = "hard";
})(RecipeComplexityLevel || (exports.RecipeComplexityLevel = RecipeComplexityLevel = {}));
class Recipe extends sequelize_1.Model {
}
exports.Recipe = Recipe;
Recipe.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    recipe_title: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
    },
    recipe_public_title: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    recipe_description: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    recipe_preparation_time: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
    },
    recipe_cook_time: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
    },
    has_recipe_public_visibility: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
    },
    has_recipe_private_visibility: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
    },
    recipe_status: {
        type: sequelize_1.DataTypes.ENUM(Object.values(RecipeStatus)),
        allowNull: false,
        defaultValue: RecipeStatus.draft,
    },
    recipe_serve_in: {
        type: sequelize_1.DataTypes.ENUM(Object.values(RecipeServeIn)),
        allowNull: true,
    },
    recipe_complexity_level: {
        type: sequelize_1.DataTypes.ENUM(Object.values(RecipeComplexityLevel)),
        allowNull: true,
    },
    recipe_garnish: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    recipe_head_chef_tips: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    recipe_foh_tips: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    recipe_impression: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
    },
    recipe_yield: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
    },
    recipe_yield_unit: {
        type: sequelize_1.DataTypes.ENUM(Object.values(RecipeYieldUnit)),
        allowNull: true,
    },
    recipe_total_portions: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
    },
    recipe_single_portion_size: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
    },
    recipe_serving_method: {
        type: sequelize_1.DataTypes.ENUM(Object.values(RecipeServingMethod)),
        allowNull: true,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    recipe_placeholder: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        references: {
            model: "nv_items",
            key: "id",
        },
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    recipe_slug: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
    },
    ingredient_costs_updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        comment: "Timestamp when ingredient costs were last calculated for this recipe",
    },
    nutrition_values_updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        comment: "Timestamp when nutrition values were last calculated for this recipe",
    },
    vitamin_a: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
        comment: "Vitamin A content in the recipe",
    },
    vitamin_c: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
        comment: "Vitamin C content in the recipe",
    },
    calcium: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
        comment: "Calcium content in the recipe",
    },
    iron: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
        comment: "Iron content in the recipe",
    },
    is_ingredient_cooking_method: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
        comment: "Whether cooking method is applied to ingredients",
    },
    is_preparation_method: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
        comment: "Whether preparation method is applied to ingredients",
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_recipe",
    modelName: "Recipe",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
        {
            unique: true,
            fields: ["recipe_slug", "organization_id"],
            name: "unique_recipe_slug_per_org",
        },
        {
            fields: ["organization_id"],
            name: "idx_recipe_organization",
        },
        {
            fields: ["recipe_status"],
            name: "idx_recipe_status",
        },
        {
            fields: ["created_by"],
            name: "idx_recipe_created_by",
        },
        {
            fields: ["updated_by"],
            name: "idx_recipe_updated_by",
        },
    ],
});
// Define associations
Recipe.associate = (models) => {
    // Recipe belongs to User (created_by)
    Recipe.belongsTo(models.User, {
        foreignKey: "created_by",
        as: "creator",
    });
    // Recipe belongs to User (updated_by)
    Recipe.belongsTo(models.User, {
        foreignKey: "updated_by",
        as: "updater",
    });
    // Recipe belongs to Item (recipe_placeholder)
    Recipe.belongsTo(models.Item, {
        foreignKey: "recipe_placeholder",
        as: "placeholderItem",
        constraints: true,
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
    });
    // Many-to-many association with Category through RecipeCategory
    Recipe.belongsToMany(models.Category, {
        through: models.RecipeCategory,
        foreignKey: "recipe_id",
        otherKey: "category_id",
        as: "categories",
    });
    // Many-to-many association with FoodAttributes through RecipeAttributes
    Recipe.belongsToMany(models.FoodAttributes, {
        through: models.RecipeAttributes,
        foreignKey: "recipe_id",
        otherKey: "attributes_id",
        as: "attributes",
    });
    // Many-to-many association with Ingredient through RecipeIngredients
    Recipe.belongsToMany(models.Ingredient, {
        through: models.RecipeIngredients,
        foreignKey: "recipe_id",
        otherKey: "ingredient_id",
        as: "ingredients",
    });
    // Many-to-many association with User through RecipeUser (bookmarks)
    Recipe.belongsToMany(models.User, {
        through: models.RecipeUser,
        foreignKey: "recipe_id",
        otherKey: "user_id",
        as: "bookmarkedByUsers",
    });
    // One-to-many association with RecipeSteps
    Recipe.hasMany(models.RecipeSteps, {
        foreignKey: "recipe_id",
        as: "steps",
    });
    // One-to-many association with RecipeResources
    Recipe.hasMany(models.RecipeResources, {
        foreignKey: "recipe_id",
        as: "resources",
    });
    // One-to-many association with RecipeHistory
    Recipe.hasMany(models.RecipeHistory, {
        foreignKey: "recipe_id",
        as: "history",
    });
};
exports.default = Recipe;
