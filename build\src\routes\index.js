"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.publicRoutes = exports.privateRoutes = void 0;
const private_1 = __importDefault(require("./private"));
exports.privateRoutes = private_1.default;
const public_1 = __importDefault(require("./public"));
exports.publicRoutes = public_1.default;
