import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum RecipeAttributesStatus {
  active = "active",
  inactive = "inactive",
}

interface RecipeAttributesAttributes {
  recipe_id: number;
  attributes_id: number;
  unit_of_measure?: string;
  unit?: number;
  status: RecipeAttributesStatus;
  organization_id?: string;
  attribute_description?: string;
  may_contain?: boolean;
  created_by: number;
  updated_by: number;
  created_at?: Date;
  updated_at?: Date;
}

export class RecipeAttributes
  extends Model<RecipeAttributesAttributes, never>
  implements RecipeAttributesAttributes {
  recipe_id!: number;
  attributes_id!: number;
  unit_of_measure?: string;
  unit?: number;
  status!: RecipeAttributesStatus;
  organization_id?: string;
  attribute_description?: string;
  may_contain?: boolean;
  created_by!: number;
  updated_by!: number;
  created_at!: Date;
  updated_at!: Date;
}

RecipeAttributes.init(
  {
    recipe_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: "mo_recipe",
        key: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    },
    attributes_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: "mo_food_attributes",
        key: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    },
    unit_of_measure: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: "Unit of measure as text (e.g., 'grams', 'cups', 'tablespoons')",
    },
    unit: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM(Object.values(RecipeAttributesStatus)),
      allowNull: false,
      defaultValue: RecipeAttributesStatus.active,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    attribute_description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    may_contain: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
      comment: "For allergens: true = may contain, false/null = contains",
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "mo_recipe_attributes",
    modelName: "RecipeAttributes",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        unique: true,
        fields: ["recipe_id", "attributes_id", "may_contain"],
        name: "unique_recipe_attributes_may_contain",
      },
      {
        fields: ["organization_id"],
        name: "idx_recipe_attributes_organization",
      },
      {
        fields: ["status"],
        name: "idx_recipe_attributes_status",
      },
      {
        fields: ["created_by"],
        name: "idx_recipe_attributes_created_by",
      },
      {
        fields: ["updated_by"],
        name: "idx_recipe_attributes_updated_by",
      },
    ],
  }
);

// Define associations
RecipeAttributes.associate = (models: any) => {
  // RecipeAttributes belongs to Recipe
  RecipeAttributes.belongsTo(models.Recipe, {
    foreignKey: "recipe_id",
    as: "recipe",
  });

  // RecipeAttributes belongs to FoodAttributes
  RecipeAttributes.belongsTo(models.FoodAttributes, {
    foreignKey: "attributes_id",
    as: "attribute",
  });

  // Note: unit_of_measure is now a text field, no longer a foreign key to RecipeMeasure

  // RecipeAttributes belongs to User (created_by)
  RecipeAttributes.belongsTo(models.User, {
    foreignKey: "created_by",
    as: "creator",
  });

  // RecipeAttributes belongs to User (updated_by)
  RecipeAttributes.belongsTo(models.User, {
    foreignKey: "updated_by",
    as: "updater",
  });
};

export default RecipeAttributes;
