"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Analytics = exports.AnalyticsEntityType = exports.AnalyticsEventType = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var AnalyticsEventType;
(function (AnalyticsEventType) {
    AnalyticsEventType["RECIPE_VIEW"] = "recipe_view";
    AnalyticsEventType["CTA_CLICK"] = "cta_click";
    AnalyticsEventType["CONTACT_FORM_SUBMIT"] = "contact_form_submit";
    AnalyticsEventType["RECIPE_BOOKMARK"] = "recipe_bookmark";
    AnalyticsEventType["RECIPE_SHARE"] = "recipe_share";
})(AnalyticsEventType || (exports.AnalyticsEventType = AnalyticsEventType = {}));
var AnalyticsEntityType;
(function (AnalyticsEntityType) {
    AnalyticsEntityType["RECIPE"] = "recipe";
    AnalyticsEntityType["CATEGORY"] = "category";
    AnalyticsEntityType["INGREDIENT"] = "ingredient";
    AnalyticsEntityType["DASHBOARD"] = "dashboard";
    AnalyticsEntityType["SETTINGS"] = "settings";
})(AnalyticsEntityType || (exports.AnalyticsEntityType = AnalyticsEntityType = {}));
class Analytics extends sequelize_1.Model {
    // Helper method to get typed metadata
    getMetadata() {
        return this.metadata
            ? typeof this.metadata === "string"
                ? JSON.parse(this.metadata)
                : this.metadata
            : {};
    }
    // Static method to track events easily
    static trackEvent(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield Analytics.create(Object.assign(Object.assign({}, data), { metadata: data.metadata ? JSON.stringify(data.metadata) : null }));
        });
    }
}
exports.Analytics = Analytics;
Analytics.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    event_type: {
        type: sequelize_1.DataTypes.ENUM(Object.values(AnalyticsEventType)),
        allowNull: false,
    },
    entity_type: {
        type: sequelize_1.DataTypes.ENUM(Object.values(AnalyticsEntityType)),
        allowNull: false,
    },
    entity_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
    },
    ip_address: {
        type: sequelize_1.DataTypes.STRING(45), // IPv6 support
        allowNull: true,
    },
    user_agent: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    metadata: {
        type: sequelize_1.DataTypes.JSON,
        allowNull: true,
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
    },
}, {
    sequelize: index_1.sequelize,
    modelName: "Analytics",
    tableName: "mo_recipe_analytics",
    timestamps: false,
});
exports.default = Analytics;
