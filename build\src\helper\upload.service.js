"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const crypto_1 = __importDefault(require("crypto"));
const client_s3_1 = require("@aws-sdk/client-s3");
const common_1 = require("./common");
// Import Item model and enums directly
const Item_1 = require("../models/Item");
// Setup MinIO client
const s3 = new client_s3_1.S3Client({
    endpoint: global.config.MINIO_ENDPOINT,
    region: "us-east-1",
    forcePathStyle: true,
    credentials: {
        accessKeyId: global.config.MINIO_ACCESS_KEY,
        secretAccessKey: global.config.MINIO_SECRET_KEY,
    },
});
/**
 * Determine the correct folder path based on field name and context
 * @param fieldName - The form field name (e.g., 'categoryIcon', 'attributeIcon', 'unitIcon')
 * @param fileName - The clean file name
 * @param req - The request object to get context
 * @param entityId - Optional entity ID (recipeId, ingredientId, etc.)
 * @param stepNumber - Optional step number for instruction media
 * @returns The folder path for the file
 */
const determineFolderPath = (fieldName, fileName, req, entityId, stepNumber) => {
    var _a, _b;
    const isSystemDefault = !((_a = req.user) === null || _a === void 0 ? void 0 : _a.organization_id);
    const orgName = isSystemDefault ? null : (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id;
    // Map field names to upload constants
    switch (fieldName) {
        // Category, Attribute, Unit Icons (no entity ID needed)
        case "categoryIcon":
            return common_1.RECIPE_FILE_UPLOAD_CONSTANT.CATEGORY_ICON.destinationPath(orgName, null, fileName);
        case "attributeIcon":
            return common_1.RECIPE_FILE_UPLOAD_CONSTANT.ATTRIBUTE_ICON.destinationPath(orgName, null, fileName);
        case "unitIcon":
            return common_1.RECIPE_FILE_UPLOAD_CONSTANT.UNIT_ICON.destinationPath(orgName, null, fileName);
        // Ingredient files
        case "ingredientIcon":
        case "ingredientImage":
            return common_1.RECIPE_FILE_UPLOAD_CONSTANT.INGREDIENT_IMAGE.destinationPath(orgName, entityId, fileName);
        // Recipe files - support multiple types
        case "recipeIcon":
        case "recipeImage":
        case "recipeImages":
            return common_1.RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_IMAGE.destinationPath(orgName, entityId, fileName);
        case "recipeVideo":
        case "recipeVideos":
            return common_1.RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_VIDEO.destinationPath(orgName, entityId, fileName);
        case "recipeDocument":
        case "recipeDocuments":
        case "recipePdf":
        case "recipePdfs":
        case "recipeFile":
        case "recipeFiles":
            return common_1.RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_DOCUMENT.destinationPath(orgName, entityId, fileName);
        case "recipeAudio":
        case "recipeAudios":
            return common_1.RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_AUDIO.destinationPath(orgName, entityId, fileName);
        case "recipeThumbnail":
        case "recipeThumbnails":
            return common_1.RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_THUMBNAIL.destinationPath(orgName, entityId, fileName);
        case "recipeInstructionMedia":
        case "stepMedia":
            return common_1.RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_INSTRUCTION_MEDIA.destinationPath(orgName, entityId, stepNumber || 1, fileName);
        case "nutritionLabel":
        case "nutritionLabels":
            return common_1.RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_NUTRITION_LABELS.destinationPath(orgName, entityId, fileName);
        default: {
            // Fallback for unknown field names
            const fallbackPath = isSystemDefault
                ? "recipe_defaults/recipe_misc"
                : `${orgName}/recipe_misc`;
            return `${fallbackPath}/${fileName}`;
        }
    }
};
/**
 * Auto-detect field name based on file MIME type for recipe uploads
 * @param mimeType - The file MIME type
 * @param originalFieldName - The original field name from the form
 * @returns Enhanced field name for proper routing
 */
const enhanceFieldNameByMimeType = (mimeType, originalFieldName) => {
    // If field name already specifies the type, use it as-is
    if (originalFieldName.includes("Video") ||
        originalFieldName.includes("Document") ||
        originalFieldName.includes("Image") ||
        originalFieldName.includes("Pdf")) {
        return originalFieldName;
    }
    // Auto-detect based on MIME type for generic field names
    if (originalFieldName.startsWith("recipe")) {
        if (mimeType.startsWith("video/")) {
            return "recipeVideo";
        }
        else if (mimeType.startsWith("audio/")) {
            return "recipeAudio";
        }
        else if (mimeType === "application/pdf") {
            return "recipeDocument";
        }
        else if (mimeType.startsWith("application/") ||
            mimeType.startsWith("text/")) {
            return "recipeDocument";
        }
        else if (mimeType.startsWith("image/")) {
            return "recipeImage";
        }
    }
    // Return original field name if no enhancement needed
    return originalFieldName;
};
/**
 * S3 upload service with hash verification to prevent duplicates
 * @param bucketName - The S3 bucket name
 * @param folderPath - Optional folder path within the bucket
 * @returns multer instance configured for S3 storage
 */
const multerS3 = (bucketName, folderPath = "") => {
    try {
        // Use memory storage for direct S3 upload (no local files)
        const storage = multer_1.default.memoryStorage();
        const upload = (0, multer_1.default)({
            storage,
            limits: {
                fileSize: 10 * 1024 * 1024, // 10MB limit
            },
            fileFilter: (req, file, cb) => {
                // Basic file type validation
                const allowedMimes = [
                    // Images
                    "image/jpeg",
                    "image/png",
                    "image/gif",
                    "image/webp",
                    "image/svg+xml",
                    // Videos
                    "video/mp4",
                    "video/webm",
                    "video/avi",
                    "video/mov",
                    "video/quicktime",
                    // Audio
                    "audio/mp3",
                    "audio/mpeg",
                    "audio/wav",
                    "audio/ogg",
                    "audio/aac",
                    "audio/m4a",
                    // Documents
                    "application/pdf",
                    "application/msword",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "text/plain",
                ];
                if (allowedMimes.includes(file.mimetype)) {
                    cb(null, true);
                }
                else {
                    cb(new Error(`File type ${file.mimetype} is not allowed`));
                }
            },
        });
        createBucketIfNotExists(bucketName).then().catch();
        // Custom middleware for single field with single files
        const s3UploadMiddleware = (fieldName, maxCount = 1) => {
            const multerMiddleware = upload.array(fieldName, maxCount);
            return (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
                multerMiddleware(req, res, (err) => __awaiter(void 0, void 0, void 0, function* () {
                    var _a, _b, _c, _d, _e, _f;
                    if (err) {
                        return next(err);
                    }
                    if (!req.files || ((_a = req.files) === null || _a === void 0 ? void 0 : _a.length) === 0) {
                        return next();
                    }
                    const uploadedFiles = [];
                    try {
                        for (const file of req.files) {
                            // Generate file hash to check for duplicates
                            const fileHash = crypto_1.default
                                .createHash("md5")
                                .update(file.buffer)
                                .digest("hex");
                            // Use clean filename without timestamp
                            const cleanFileName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, "_");
                            const fileName = cleanFileName;
                            // Enhance field name based on MIME type for better routing
                            const enhancedFieldName = enhanceFieldNameByMimeType(file.mimetype, fieldName);
                            // Determine the correct file path using the helper function
                            const filePath = determineFolderPath(enhancedFieldName, fileName, req);
                            const fileBuffer = file.buffer;
                            // Check if file already exists in the bucket
                            let fileExists = false;
                            let fileExistAtLocation = false;
                            try {
                                const getItem = yield Item_1.Item.findOne({
                                    where: {
                                        item_hash: fileHash,
                                        item_organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id,
                                    },
                                });
                                if (getItem && getItem.id) {
                                    fileExists = true;
                                    const checkS3FileExist = yield s3.send(new client_s3_1.GetObjectCommand({
                                        Bucket: bucketName,
                                        Key: getItem === null || getItem === void 0 ? void 0 : getItem.item_location,
                                    }));
                                    if (checkS3FileExist && (checkS3FileExist === null || checkS3FileExist === void 0 ? void 0 : checkS3FileExist.Body)) {
                                        fileExistAtLocation = true;
                                    }
                                }
                            }
                            catch (error) {
                                // File doesn't exist, continue with upload
                            }
                            if (!fileExists) {
                                // Upload file to S3
                                yield s3.send(new client_s3_1.PutObjectCommand({
                                    Bucket: bucketName,
                                    Key: filePath,
                                    Body: fileBuffer,
                                    ContentType: file.mimetype,
                                }));
                                const saveItem = {
                                    item_type: file.mimetype == "multipart/form-data"
                                        ? Item_1.item_type.VIDEO
                                        : file.mimetype == "application/octet-stream"
                                            ? Item_1.item_type.VIDEO
                                            : file.mimetype.split("/")[0] == "application"
                                                ? "pdf"
                                                : file.mimetype.split("/")[0],
                                    item_name: file.originalname,
                                    item_hash: fileHash,
                                    item_mime_type: file.mimetype,
                                    item_extension: path_1.default.extname(file.originalname),
                                    item_size: file.size,
                                    item_IEC: Item_1.item_IEC.B,
                                    item_status: Item_1.item_status.ACTIVE,
                                    item_external_location: Item_1.item_external_location.NO,
                                    item_location: filePath,
                                    item_organization_id: ((_c = req.user) === null || _c === void 0 ? void 0 : _c.organization_id) || null,
                                    created_by: ((_d = req.user) === null || _d === void 0 ? void 0 : _d.id) || null,
                                    updated_by: ((_e = req.user) === null || _e === void 0 ? void 0 : _e.id) || null,
                                };
                                const item = yield Item_1.Item.create(saveItem);
                                // Add file info to the request
                                uploadedFiles.push({
                                    originalname: file.originalname,
                                    filename: fileName,
                                    path: filePath,
                                    size: file.size,
                                    mimetype: file.mimetype,
                                    hash: fileHash,
                                    bucket: bucketName,
                                    item_id: item.id,
                                    type: item.item_type,
                                    isMovable: true,
                                });
                            }
                            else {
                                const getItem = yield Item_1.Item.findOne({
                                    where: {
                                        item_hash: fileHash,
                                        item_organization_id: (_f = req.user) === null || _f === void 0 ? void 0 : _f.organization_id,
                                    },
                                });
                                if (!fileExistAtLocation) {
                                    yield s3.send(new client_s3_1.PutObjectCommand({
                                        Bucket: bucketName,
                                        Key: filePath,
                                        Body: fileBuffer,
                                        ContentType: file.mimetype,
                                    }));
                                    yield Item_1.Item.update({
                                        item_location: filePath,
                                        item_status: Item_1.item_status === null || Item_1.item_status === void 0 ? void 0 : Item_1.item_status.ACTIVE,
                                    }, {
                                        where: {
                                            id: getItem === null || getItem === void 0 ? void 0 : getItem.id,
                                        },
                                    });
                                    uploadedFiles.push({
                                        originalname: file.originalname,
                                        filename: fileName,
                                        path: filePath,
                                        size: file.size,
                                        mimetype: file.mimetype,
                                        hash: fileHash,
                                        bucket: bucketName,
                                        item_id: getItem.id,
                                        type: getItem === null || getItem === void 0 ? void 0 : getItem.item_type,
                                        isMovable: true,
                                    });
                                }
                                else {
                                    uploadedFiles.push({
                                        originalname: file.originalname,
                                        filename: fileName,
                                        path: getItem === null || getItem === void 0 ? void 0 : getItem.item_location,
                                        size: file.size,
                                        mimetype: file.mimetype,
                                        hash: fileHash,
                                        bucket: bucketName,
                                        item_id: getItem.id,
                                        type: getItem === null || getItem === void 0 ? void 0 : getItem.item_type,
                                        isMovable: false,
                                    });
                                }
                            }
                        }
                        // Organize files by field name for proper access
                        req.files = { [fieldName]: uploadedFiles };
                        return next();
                    }
                    catch (error) {
                        console.error("Error uploading to S3:", error);
                        return next(error);
                    }
                }));
            });
        };
        // Custom middleware for single field with multiple files
        const s3UploadArrayMiddleware = (fieldName, maxCount = 10) => {
            const multerMiddleware = upload.array(fieldName, maxCount);
            return (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
                multerMiddleware(req, res, (err) => __awaiter(void 0, void 0, void 0, function* () {
                    var _a, _b, _c, _d, _e, _f;
                    if (err) {
                        return next(err);
                    }
                    if (!req.files || ((_a = req.files) === null || _a === void 0 ? void 0 : _a.length) === 0) {
                        return next();
                    }
                    const uploadedFiles = [];
                    try {
                        for (const file of req.files) {
                            // Generate file hash to check for duplicates
                            const fileHash = crypto_1.default
                                .createHash("md5")
                                .update(file.buffer)
                                .digest("hex");
                            // Use clean filename without timestamp
                            const cleanFileName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, "_");
                            const fileName = cleanFileName;
                            // Enhance field name based on MIME type for better routing
                            const enhancedFieldName = enhanceFieldNameByMimeType(file.mimetype, fieldName);
                            // Determine the correct file path using the helper function
                            const filePath = determineFolderPath(enhancedFieldName, fileName, req);
                            const fileBuffer = file.buffer;
                            let fileExists = false;
                            let fileExistAtLocation = false;
                            try {
                                const getItem = yield Item_1.Item.findOne({
                                    where: {
                                        item_hash: fileHash,
                                        item_organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id,
                                    },
                                });
                                if (getItem && getItem.id) {
                                    fileExists = true;
                                    const checkS3FileExist = yield s3.send(new client_s3_1.GetObjectCommand({
                                        Bucket: bucketName,
                                        Key: getItem === null || getItem === void 0 ? void 0 : getItem.item_location,
                                    }));
                                    if (checkS3FileExist && (checkS3FileExist === null || checkS3FileExist === void 0 ? void 0 : checkS3FileExist.Body)) {
                                        fileExistAtLocation = true;
                                    }
                                }
                            }
                            catch (_g) {
                                // File doesn't exist, continue with upload
                            }
                            if (!fileExists) {
                                // Upload file to S3
                                yield s3.send(new client_s3_1.PutObjectCommand({
                                    Bucket: bucketName,
                                    Key: filePath,
                                    Body: fileBuffer,
                                    ContentType: file.mimetype,
                                }));
                                const saveItem = {
                                    item_type: file.mimetype == "multipart/form-data"
                                        ? Item_1.item_type.VIDEO
                                        : file.mimetype == "application/octet-stream"
                                            ? Item_1.item_type.VIDEO
                                            : file.mimetype.split("/")[0] == "application"
                                                ? "pdf"
                                                : file.mimetype.split("/")[0],
                                    item_name: file.originalname,
                                    item_hash: fileHash,
                                    item_mime_type: file.mimetype,
                                    item_extension: path_1.default.extname(file.originalname),
                                    item_size: file.size,
                                    item_IEC: Item_1.item_IEC.B,
                                    item_status: Item_1.item_status.ACTIVE,
                                    item_external_location: Item_1.item_external_location.NO,
                                    item_location: filePath,
                                    item_organization_id: ((_c = req.user) === null || _c === void 0 ? void 0 : _c.organization_id) || null,
                                    created_by: ((_d = req.user) === null || _d === void 0 ? void 0 : _d.id) || null,
                                    updated_by: ((_e = req.user) === null || _e === void 0 ? void 0 : _e.id) || null,
                                };
                                const item = yield Item_1.Item.create(saveItem);
                                // Add file info to the request
                                uploadedFiles.push({
                                    originalname: file.originalname,
                                    filename: fileName,
                                    path: filePath,
                                    size: file.size,
                                    mimetype: file.mimetype,
                                    hash: fileHash,
                                    bucket: bucketName,
                                    item_id: item.id,
                                    type: item.item_type,
                                    isMovable: true,
                                });
                            }
                            else {
                                const getItem = yield Item_1.Item.findOne({
                                    where: {
                                        item_hash: fileHash,
                                        item_organization_id: (_f = req.user) === null || _f === void 0 ? void 0 : _f.organization_id,
                                    },
                                });
                                if (!fileExistAtLocation) {
                                    yield s3.send(new client_s3_1.PutObjectCommand({
                                        Bucket: bucketName,
                                        Key: filePath,
                                        Body: fileBuffer,
                                        ContentType: file.mimetype,
                                    }));
                                    yield Item_1.Item.update({
                                        item_location: filePath,
                                        item_status: Item_1.item_status === null || Item_1.item_status === void 0 ? void 0 : Item_1.item_status.ACTIVE,
                                    }, {
                                        where: {
                                            id: getItem === null || getItem === void 0 ? void 0 : getItem.id,
                                        },
                                    });
                                    uploadedFiles.push({
                                        originalname: file.originalname,
                                        filename: fileName,
                                        path: filePath,
                                        size: file.size,
                                        mimetype: file.mimetype,
                                        hash: fileHash,
                                        bucket: bucketName,
                                        item_id: getItem.id,
                                        type: getItem === null || getItem === void 0 ? void 0 : getItem.item_type,
                                        isMovable: true,
                                    });
                                }
                                else {
                                    uploadedFiles.push({
                                        originalname: file.originalname,
                                        filename: fileName,
                                        path: getItem === null || getItem === void 0 ? void 0 : getItem.item_location,
                                        size: file.size,
                                        mimetype: file.mimetype,
                                        hash: fileHash,
                                        bucket: bucketName,
                                        item_id: getItem.id,
                                        type: getItem === null || getItem === void 0 ? void 0 : getItem.item_type,
                                        isMovable: false,
                                    });
                                }
                            }
                        }
                        // Replace the files array with our processed files
                        req.files = uploadedFiles;
                        return next();
                    }
                    catch (error) {
                        console.error("Error uploading to S3:", error);
                        return next(error);
                    }
                }));
            });
        };
        // Custom middleware for multiple fields
        const s3UploadFieldsMiddleware = (fields) => {
            const multerMiddleware = upload.fields(fields);
            return (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
                multerMiddleware(req, res, (err) => __awaiter(void 0, void 0, void 0, function* () {
                    var _a, _b, _c, _d, _e;
                    if (err) {
                        return next(err);
                    }
                    if (!req.files) {
                        return next();
                    }
                    try {
                        // Process each field separately
                        const processedFiles = {};
                        for (const fieldName of Object.keys(req.files)) {
                            const fieldFiles = req.files[fieldName];
                            const uploadedFiles = [];
                            for (const file of fieldFiles) {
                                // Generate file hash to check for duplicates
                                const fileHash = crypto_1.default
                                    .createHash("md5")
                                    .update(file.buffer)
                                    .digest("hex");
                                // Use clean filename without timestamp
                                const cleanFileName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, "_");
                                const fileName = cleanFileName;
                                // Enhance field name based on MIME type for better routing
                                const enhancedFieldName = enhanceFieldNameByMimeType(file.mimetype, fieldName);
                                // Determine the correct file path using the helper function
                                const filePath = determineFolderPath(enhancedFieldName, fileName, req);
                                const fileBuffer = file.buffer;
                                let fileExists = false;
                                let fileExistAtLocation = false;
                                try {
                                    const getItem = yield Item_1.Item.findOne({
                                        where: {
                                            item_hash: fileHash,
                                            item_organization_id: (_a = req.user) === null || _a === void 0 ? void 0 : _a.organization_id,
                                        },
                                    });
                                    if (getItem && getItem.id) {
                                        fileExists = true;
                                        const checkS3FileExist = yield s3.send(new client_s3_1.GetObjectCommand({
                                            Bucket: bucketName,
                                            Key: getItem === null || getItem === void 0 ? void 0 : getItem.item_location,
                                        }));
                                        if (checkS3FileExist && (checkS3FileExist === null || checkS3FileExist === void 0 ? void 0 : checkS3FileExist.Body)) {
                                            fileExistAtLocation = true;
                                        }
                                    }
                                }
                                catch (_f) {
                                    // File doesn't exist, continue with upload
                                }
                                if (!fileExists) {
                                    // Upload file to S3
                                    yield s3.send(new client_s3_1.PutObjectCommand({
                                        Bucket: bucketName,
                                        Key: filePath,
                                        Body: fileBuffer,
                                        ContentType: file.mimetype,
                                    }));
                                    const saveItem = {
                                        item_type: file.mimetype == "multipart/form-data"
                                            ? Item_1.item_type.VIDEO
                                            : file.mimetype == "application/octet-stream"
                                                ? Item_1.item_type.VIDEO
                                                : file.mimetype.split("/")[0] == "application"
                                                    ? "pdf"
                                                    : file.mimetype.split("/")[0],
                                        item_name: file.originalname,
                                        item_hash: fileHash,
                                        item_mime_type: file.mimetype,
                                        item_extension: path_1.default.extname(file.originalname),
                                        item_size: file.size,
                                        item_IEC: Item_1.item_IEC.B,
                                        item_status: Item_1.item_status.ACTIVE,
                                        item_external_location: Item_1.item_external_location.NO,
                                        item_location: filePath,
                                        item_organization_id: ((_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id) || null,
                                        created_by: ((_c = req.user) === null || _c === void 0 ? void 0 : _c.id) || null,
                                        updated_by: ((_d = req.user) === null || _d === void 0 ? void 0 : _d.id) || null,
                                    };
                                    const item = yield Item_1.Item.create(saveItem);
                                    // Add file info to the request
                                    uploadedFiles.push({
                                        originalname: file.originalname,
                                        filename: fileName,
                                        path: filePath,
                                        size: file.size,
                                        mimetype: file.mimetype,
                                        hash: fileHash,
                                        bucket: bucketName,
                                        item_id: item.id,
                                        type: item.item_type,
                                        isMovable: true,
                                    });
                                }
                                else {
                                    const getItem = yield Item_1.Item.findOne({
                                        where: {
                                            item_hash: fileHash,
                                            item_organization_id: (_e = req.user) === null || _e === void 0 ? void 0 : _e.organization_id,
                                        },
                                    });
                                    if (!fileExistAtLocation) {
                                        yield s3.send(new client_s3_1.PutObjectCommand({
                                            Bucket: bucketName,
                                            Key: filePath,
                                            Body: fileBuffer,
                                            ContentType: file.mimetype,
                                        }));
                                        yield Item_1.Item.update({
                                            item_location: filePath,
                                            item_status: Item_1.item_status === null || Item_1.item_status === void 0 ? void 0 : Item_1.item_status.ACTIVE,
                                        }, {
                                            where: {
                                                id: getItem === null || getItem === void 0 ? void 0 : getItem.id,
                                            },
                                        });
                                        uploadedFiles.push({
                                            originalname: file.originalname,
                                            filename: fileName,
                                            path: filePath,
                                            size: file.size,
                                            mimetype: file.mimetype,
                                            hash: fileHash,
                                            bucket: bucketName,
                                            item_id: getItem.id,
                                            type: getItem === null || getItem === void 0 ? void 0 : getItem.item_type,
                                            isMovable: true,
                                        });
                                    }
                                    else {
                                        uploadedFiles.push({
                                            originalname: file.originalname,
                                            filename: fileName,
                                            path: getItem === null || getItem === void 0 ? void 0 : getItem.item_location,
                                            size: file.size,
                                            mimetype: file.mimetype,
                                            hash: fileHash,
                                            bucket: bucketName,
                                            item_id: getItem.id,
                                            type: getItem === null || getItem === void 0 ? void 0 : getItem.item_type,
                                            isMovable: false,
                                        });
                                    }
                                }
                            }
                            // Add processed files for this field
                            processedFiles[fieldName] = uploadedFiles;
                        }
                        // Replace the files object with our processed files
                        req.files = processedFiles;
                        return next();
                    }
                    catch (error) {
                        console.error("Error uploading to S3:", error);
                        return next(error);
                    }
                }));
            });
        };
        // Single file upload middleware (for .upload() method)
        const s3SingleUploadMiddleware = (fieldName) => {
            const multerMiddleware = upload.single(fieldName);
            return (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
                multerMiddleware(req, res, (err) => __awaiter(void 0, void 0, void 0, function* () {
                    var _a, _b, _c, _d, _e;
                    if (err) {
                        console.error("Multer error:", err);
                        return next(err);
                    }
                    if (!req.file) {
                        return next();
                    }
                    const uploadedFiles = [];
                    try {
                        const file = req.file;
                        // Generate file hash to check for duplicates
                        const fileHash = crypto_1.default
                            .createHash("md5")
                            .update(file.buffer)
                            .digest("hex");
                        // Use clean filename without timestamp
                        const cleanFileName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, "_");
                        const fileName = cleanFileName;
                        // Enhance field name based on MIME type for better routing
                        const enhancedFieldName = enhanceFieldNameByMimeType(file.mimetype, fieldName);
                        // Determine the correct file path using the helper function
                        const filePath = determineFolderPath(enhancedFieldName, fileName, req);
                        const fileBuffer = file.buffer;
                        // Check if file already exists in the bucket
                        let fileExists = false;
                        let fileExistAtLocation = false;
                        try {
                            const getItem = yield Item_1.Item.findOne({
                                where: {
                                    item_hash: fileHash,
                                    item_organization_id: (_a = req.user) === null || _a === void 0 ? void 0 : _a.organization_id,
                                },
                            });
                            if (getItem && getItem.id) {
                                fileExists = true;
                                const checkS3FileExist = yield s3.send(new client_s3_1.GetObjectCommand({
                                    Bucket: bucketName,
                                    Key: getItem === null || getItem === void 0 ? void 0 : getItem.item_location,
                                }));
                                if (checkS3FileExist && (checkS3FileExist === null || checkS3FileExist === void 0 ? void 0 : checkS3FileExist.Body)) {
                                    fileExistAtLocation = true;
                                }
                            }
                        }
                        catch (_f) {
                            // File doesn't exist, continue with upload
                        }
                        if (!fileExists) {
                            // Upload file to S3
                            yield s3.send(new client_s3_1.PutObjectCommand({
                                Bucket: bucketName,
                                Key: filePath,
                                Body: fileBuffer,
                                ContentType: file.mimetype,
                            }));
                            const saveItem = {
                                item_type: file.mimetype == "multipart/form-data"
                                    ? Item_1.item_type.VIDEO
                                    : file.mimetype == "application/octet-stream"
                                        ? Item_1.item_type.VIDEO
                                        : file.mimetype.split("/")[0] == "application"
                                            ? "pdf"
                                            : file.mimetype.split("/")[0],
                                item_name: file.originalname,
                                item_hash: fileHash,
                                item_mime_type: file.mimetype,
                                item_extension: path_1.default.extname(file.originalname),
                                item_size: file.size,
                                item_IEC: Item_1.item_IEC.B,
                                item_status: Item_1.item_status.ACTIVE,
                                item_external_location: Item_1.item_external_location.NO,
                                item_location: filePath,
                                item_organization_id: ((_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id) || null,
                                created_by: ((_c = req.user) === null || _c === void 0 ? void 0 : _c.id) || null,
                                updated_by: ((_d = req.user) === null || _d === void 0 ? void 0 : _d.id) || null,
                            };
                            const item = yield Item_1.Item.create(saveItem);
                            // Add file info to the request
                            uploadedFiles.push({
                                originalname: file.originalname,
                                filename: fileName,
                                path: filePath,
                                size: file.size,
                                mimetype: file.mimetype,
                                hash: fileHash,
                                bucket: bucketName,
                                item_id: item.id,
                                type: item.item_type,
                                isMovable: true,
                            });
                        }
                        else {
                            const getItem = yield Item_1.Item.findOne({
                                where: {
                                    item_hash: fileHash,
                                    item_organization_id: (_e = req.user) === null || _e === void 0 ? void 0 : _e.organization_id,
                                },
                            });
                            if (!fileExistAtLocation) {
                                // File exists in DB but not in S3, re-upload
                                yield s3.send(new client_s3_1.PutObjectCommand({
                                    Bucket: bucketName,
                                    Key: filePath,
                                    Body: fileBuffer,
                                    ContentType: file.mimetype,
                                }));
                                yield Item_1.Item.update({
                                    item_location: filePath,
                                    item_status: Item_1.item_status === null || Item_1.item_status === void 0 ? void 0 : Item_1.item_status.ACTIVE,
                                }, {
                                    where: {
                                        id: getItem === null || getItem === void 0 ? void 0 : getItem.id,
                                    },
                                });
                                uploadedFiles.push({
                                    originalname: file.originalname,
                                    filename: fileName,
                                    path: filePath,
                                    size: file.size,
                                    mimetype: file.mimetype,
                                    hash: fileHash,
                                    bucket: bucketName,
                                    item_id: getItem.id,
                                    type: getItem === null || getItem === void 0 ? void 0 : getItem.item_type,
                                    isMovable: true,
                                });
                            }
                            else {
                                uploadedFiles.push({
                                    originalname: file.originalname,
                                    filename: fileName,
                                    path: getItem === null || getItem === void 0 ? void 0 : getItem.item_location,
                                    size: file.size,
                                    mimetype: file.mimetype,
                                    hash: fileHash,
                                    bucket: bucketName,
                                    item_id: getItem.id,
                                    type: getItem === null || getItem === void 0 ? void 0 : getItem.item_type,
                                    isMovable: false,
                                });
                            }
                        }
                        // Organize files by field name for proper access
                        req.files = { [fieldName]: uploadedFiles };
                        return next();
                    }
                    catch (uploadError) {
                        console.error("Error uploading to S3:", uploadError);
                        return next(uploadError);
                    }
                }));
            });
        };
        return {
            upload: s3SingleUploadMiddleware,
            fields: s3UploadFieldsMiddleware,
            array: s3UploadArrayMiddleware,
        };
    }
    catch (error) {
        console.error("S3 upload configuration error:", error);
        // Return dummy middleware functions to prevent TypeScript errors
        const errorMiddleware = (_req, _res, next) => {
            return next(new Error("S3 upload configuration failed"));
        };
        return {
            upload: (_fieldName, _maxCount = 1) => errorMiddleware,
            fields: (_fields) => errorMiddleware,
            array: (_fieldName, _maxCount = 10) => errorMiddleware,
        };
    }
};
const createBucketIfNotExists = (bucketName) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield s3.send(new client_s3_1.HeadBucketCommand({ Bucket: bucketName }));
        // Bucket already exists
    }
    catch (error) {
        if (error.name === "NotFound" || error.name === "NoSuchBucket") {
            try {
                yield s3.send(new client_s3_1.CreateBucketCommand({ Bucket: bucketName }));
                console.log(`Bucket ${bucketName} created successfully`);
            }
            catch (createError) {
                console.error(`Error creating bucket: ${createError.message}`);
            }
        }
        else {
            console.error(`Error checking bucket: ${error.message}`);
        }
    }
});
/**
 * Move a file to a different path within the same bucket
 * @param bucketName - The bucket name
 * @param sourceKey - Current file path/key
 * @param destinationKey - New file path/key
 * @param deleteSource - Whether to delete the source file after copying (default: true)
 * @returns Object containing information about the move operation
 */
const moveFileInBucket = (bucketName_1, sourceKey_1, destinationKey_1, item_id_1, ...args_1) => __awaiter(void 0, [bucketName_1, sourceKey_1, destinationKey_1, item_id_1, ...args_1], void 0, function* (bucketName, sourceKey, destinationKey, item_id, deleteSource = true) {
    try {
        // Ensure bucket exists
        yield createBucketIfNotExists(bucketName);
        // Check if source file exists
        try {
            yield s3.send(new client_s3_1.HeadObjectCommand({
                Bucket: bucketName,
                Key: sourceKey,
            }));
        }
        catch (error) {
            return {
                success: false,
                error: `Source file does not exist: ${bucketName}/${sourceKey}`,
            };
        }
        // Check if source and destination are the same
        if (sourceKey === destinationKey) {
            return {
                success: true,
                sourceUrl: `${sourceKey}`,
                destinationUrl: `${destinationKey}`,
            };
        }
        yield Item_1.Item.update({
            item_location: destinationKey,
        }, {
            where: {
                id: item_id,
            },
        });
        // Copy the file to the destination path
        yield s3.send(new client_s3_1.CopyObjectCommand({
            CopySource: `${bucketName}/${sourceKey}`,
            Bucket: bucketName,
            Key: destinationKey,
        }));
        // Delete the source file if deleteSource is true
        if (deleteSource) {
            yield s3.send(new client_s3_1.DeleteObjectCommand({
                Bucket: bucketName,
                Key: sourceKey,
            }));
        }
        return {
            success: true,
            sourceUrl: `${sourceKey}`,
            destinationUrl: `${destinationKey}`,
        };
    }
    catch (error) {
        console.error("Error moving file within bucket:", error);
        return {
            success: false,
            error: error,
        };
    }
});
const deleteFileFromBucket = (bucketName, sourceKey) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield s3.send(new client_s3_1.DeleteObjectCommand({
            Bucket: bucketName,
            Key: sourceKey,
        }));
        return {
            success: true,
        };
    }
    catch (error) {
        console.error("Error delete file from bucket:", error);
        return {
            success: false,
            error: error,
        };
    }
});
const moveFilesLocalToS3 = (file_1, folderPath_1, _fileName_1, organization_id_1, ...args_1) => __awaiter(void 0, [file_1, folderPath_1, _fileName_1, organization_id_1, ...args_1], void 0, function* (file, folderPath, _fileName, organization_id, bucketName = process.env.NODE_ENV) {
    try {
        // Generate file hash to check for duplicates
        const fileHash = (0, common_1.getHash)(file);
        const fileName = file.originalname;
        const filePath = folderPath ? `${folderPath}/${fileName}` : fileName;
        // // Check if file already exists in the bucket
        // let fileExists = false;
        // try {
        //   if (fileHash.status) {
        //     const getItem: any = await Item.findOne({
        //       where: {
        //         item_hash: fileHash?.hash,
        //         item_organization_id: organization_id
        //       },
        //     });
        //     if (getItem && getItem.id) {
        //       fileExists = true;
        //     }
        //   }
        // } catch (error) {
        //   // File doesn't exist, continue with upload
        // }
        // Upload file to S3
        yield s3.send(new client_s3_1.PutObjectCommand({
            Bucket: bucketName,
            Key: filePath,
            Body: file.buffer,
            ContentType: file.mimetype,
        }));
        const saveItem = {
            item_type: file.mimetype == "multipart/form-data"
                ? Item_1.item_type.VIDEO
                : file.mimetype == "application/octet-stream"
                    ? Item_1.item_type.VIDEO
                    : file.mimetype.split("/")[0] == "application"
                        ? "pdf"
                        : file.mimetype.split("/")[0],
            item_name: file.filename,
            item_hash: fileHash === null || fileHash === void 0 ? void 0 : fileHash.hash,
            item_mime_type: file.mimetype,
            item_extension: path_1.default.extname(file.originalname),
            item_size: file.size,
            item_IEC: Item_1.item_IEC.B,
            item_status: Item_1.item_status.ACTIVE,
            item_external_location: Item_1.item_external_location.NO,
            item_location: filePath,
            item_organization_id: organization_id,
        };
        const item = yield Item_1.Item.create(saveItem);
        return {
            success: true,
            data: item,
        };
    }
    catch (error) {
        return {
            success: false,
            error: error,
        };
    }
});
/**
 * Upload a file buffer directly to S3
 * @param bucketName - S3 bucket name
 * @param key - The file path/key within the bucket
 * @param fileBuffer - The file data buffer
 * @param contentType - The file MIME type
 * @returns Object containing upload status and error if any
 */
const uploadFileToBucket = (bucketName_1, key_1, fileBuffer_1, ...args_1) => __awaiter(void 0, [bucketName_1, key_1, fileBuffer_1, ...args_1], void 0, function* (bucketName, key, fileBuffer, contentType = "application/octet-stream") {
    try {
        // Ensure bucket exists
        yield createBucketIfNotExists(bucketName);
        // Upload to S3
        yield s3.send(new client_s3_1.PutObjectCommand({
            Bucket: bucketName,
            Key: key,
            Body: fileBuffer,
            ContentType: contentType,
        }));
        return {
            success: true,
        };
    }
    catch (error) {
        console.error("Error uploading file to bucket:", error);
        return {
            success: false,
            error,
        };
    }
});
// Default export object
exports.default = {
    s3,
    multerS3,
    moveFileInBucket,
    deleteFileFromBucket,
    moveFilesLocalToS3,
    uploadFileToBucket,
};
