"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const contact_routes_1 = __importDefault(require("./contact.routes"));
const analytics_routes_1 = __importDefault(require("./analytics.routes"));
const recipe_routes_1 = __importDefault(require("./recipe.routes"));
const routes = express_1.default.Router();
// Contact Us routes (public - no authentication required)
routes.use("/contact-us", contact_routes_1.default);
// Analytics routes (public - no authentication required)
routes.use("/analytics", analytics_routes_1.default);
// Recipe routes (public - no authentication required)
routes.use("/recipes", recipe_routes_1.default);
exports.default = routes;
