"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_codes_1 = require("http-status-codes");
const sequelize_1 = require("sequelize");
const models_1 = require("../models");
const Category_1 = require("../models/Category");
const slugGenerator_1 = require("../helper/slugGenerator");
const common_1 = require("../helper/common");
const upload_service_1 = __importDefault(require("../helper/upload.service"));
const transaction_helper_1 = require("../helper/transaction.helper");
const validation_helper_1 = require("../helper/validation.helper");
// Get models from db object to ensure associations are set up
const Category = models_1.db.Category;
const Item = models_1.db.Item;
const RecipeCategory = models_1.db.RecipeCategory;
const IngredientCategory = models_1.db.IngredientCategory;
// Helper function to get the proper base URL
const getBaseUrl = () => {
    var _a;
    const baseUrl = (_a = global.config) === null || _a === void 0 ? void 0 : _a.API_BASE_URL;
    if (baseUrl &&
        baseUrl.includes("/backend-api/v1/public/user/get-file?location=")) {
        // API_BASE_URL already contains the full endpoint, return base part
        return baseUrl.replace("/backend-api/v1/public/user/get-file?location=", "");
    }
    else {
        // For development or when API_BASE_URL is just the base domain
        return (process.env.BASE_URL ||
            process.env.FRONTEND_URL ||
            "https://staging.namastevillage.theeasyaccess.com");
    }
};
/**
 * Create a new category
 * @route POST /api/v1/private/category
 * @access Private (Authenticated users)
 */
const createCategory = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d;
    const transactionManager = new transaction_helper_1.TransactionManager();
    const fileTracker = new transaction_helper_1.FileOperationTracker();
    try {
        // Input validation and sanitization
        const sanitizedBody = validation_helper_1.ValidationHelper.sanitizeInput(req.body);
        const { category_name, category_description, category_status, category_type, } = sanitizedBody;
        // Start transaction
        const transaction = yield transactionManager.start();
        const trimmed_category_name = category_name.trim();
        if (trimmed_category_name.length < 2) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("CATEGORY_NAME_TOO_SHORT"),
            });
        }
        // Check if user has default access (can create system defaults)
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        // Determine organization ID for creation
        const organizationIdForCreation = hasDefaultAccess
            ? null
            : (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id;
        // 🔍 ENHANCED UNIQUENESS VALIDATION
        // Check for existing category with same name (case-insensitive)
        // Ensures uniqueness within organization scope AND prevents conflicts with system defaults
        const existingCategoryByName = yield Category.findOne({
            where: {
                [sequelize_1.Op.and]: [
                    models_1.db.sequelize.where(models_1.db.sequelize.fn("LOWER", models_1.db.sequelize.col("category_name")), models_1.db.sequelize.fn("LOWER", trimmed_category_name)),
                    {
                        category_type: category_type, // Must be same type
                    },
                    {
                        [sequelize_1.Op.or]: [
                            { organization_id: organizationIdForCreation }, // Same organization
                            { organization_id: null }, // Default/system records
                        ],
                    },
                ],
            },
        });
        if (existingCategoryByName) {
            const conflictType = existingCategoryByName.organization_id
                ? "organization"
                : "system default";
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: `Category name "${trimmed_category_name}" already exists in ${conflictType} ${category_type} categories`,
                error_code: "CATEGORY_NAME_DUPLICATE",
                conflict_details: {
                    existing_category_id: existingCategoryByName.id,
                    conflict_type: conflictType,
                    category_type: category_type,
                },
            });
        }
        // 🔍 SLUG UNIQUENESS VALIDATION
        let categorySlug = trimmed_category_name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, "-")
            .replace(/^-+|-+$/g, "");
        const existingCategoryBySlug = yield Category.findOne({
            where: {
                [sequelize_1.Op.and]: [
                    { category_slug: categorySlug },
                    { category_type: category_type },
                    {
                        [sequelize_1.Op.or]: [
                            { organization_id: organizationIdForCreation },
                            { organization_id: null },
                        ],
                    },
                ],
            },
        });
        if (existingCategoryBySlug) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: `Category slug "${categorySlug}" already exists`,
                error_code: "CATEGORY_SLUG_DUPLICATE",
            });
        }
        // Generate unique slug from category name with transaction lock
        const checkSlugExists = (slug) => __awaiter(void 0, void 0, void 0, function* () {
            const existing = yield Category.findOne({
                where: {
                    category_slug: slug,
                    organization_id: organizationIdForCreation,
                },
                transaction, // Use transaction to prevent race conditions
                lock: true, // Add row-level lock
            });
            return !!existing;
        });
        categorySlug = yield (0, slugGenerator_1.generateUniqueSlug)(trimmed_category_name, checkSlugExists, {
            maxLength: 25,
            separator: "-",
            lowercase: true,
        });
        // Set system category flag based on default access
        const isSystemCategory = hasDefaultAccess;
        // Create category first
        const categoryAttributes = {
            category_name: trimmed_category_name,
            category_slug: categorySlug,
            category_description: category_description,
            category_status: category_status || Category_1.CategoryStatus.active,
            organization_id: organizationIdForCreation,
            category_type: category_type,
            is_system_category: isSystemCategory,
            created_by: ((_c = req.user) === null || _c === void 0 ? void 0 : _c.id) || null,
            updated_by: ((_d = req.user) === null || _d === void 0 ? void 0 : _d.id) || null,
        };
        const category = yield Category.create(categoryAttributes, { transaction });
        let iconItemId = null;
        let uploadedFile = null;
        // Handle file upload from multerS3 middleware (req.files format)
        if (req.files &&
            typeof req.files === "object" &&
            !Array.isArray(req.files)) {
            const files = req.files;
            if (files.categoryIcon && files.categoryIcon.length > 0) {
                uploadedFile = files.categoryIcon[0];
                iconItemId = uploadedFile.item_id;
            }
        }
        // Handle file path update after category creation
        if (uploadedFile && uploadedFile.isMovable) {
            try {
                // Get organization name for path generation
                // For system defaults (null org_id), pass null; otherwise convert to string
                const orgName = organizationIdForCreation
                    ? organizationIdForCreation.toString()
                    : null;
                // Generate the correct destination path
                const destinationPath = common_1.RECIPE_FILE_UPLOAD_CONSTANT.CATEGORY_ICON.destinationPath(orgName, category.id, uploadedFile.filename);
                // Track file operation for potential rollback
                fileTracker.trackMove(uploadedFile.path, destinationPath);
                // Move file to correct location
                const bucketName = process.env.NODE_ENV || "development";
                const moveResult = yield upload_service_1.default.moveFileInBucket(bucketName, uploadedFile.path, destinationPath, uploadedFile.item_id);
                if (!moveResult.success) {
                    throw new Error(`Failed to move category icon: ${moveResult.error}`);
                }
            }
            catch (error) {
                console.error("Error moving uploaded file:", error);
            }
        }
        // Update category with icon item_id if uploaded
        if (iconItemId) {
            yield category.update({ category_icon: iconItemId }, { transaction });
        }
        // Commit transaction
        yield transactionManager.commit();
        // Clear file operations after successful commit
        fileTracker.clear();
        return res.status(http_status_codes_1.StatusCodes.CREATED).json({
            status: true,
            message: res.__("CATEGORY_CREATED_SUCCESSFULLY"),
        });
    }
    catch (error) {
        yield transactionManager.rollback();
        yield fileTracker.rollback();
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error creating category");
    }
});
/**
 * Get all categories with advanced filtering and search
 * @route GET /api/v1/private/category
 * @access Private (Authenticated users)
 *
 * Query Parameters:
 * - page: Page number (default: 1)
 * - limit: Items per page (default: 10)
 * - search: Search in name and description
 * - status: Filter by category status (active, inactive)
 * - type: Filter by category type (recipe, ingredient)
 * - sort: Sort field (default: category_name)
 * - order: Sort order (ASC/DESC, default: ASC)
 */
const getAllCategories = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { page, limit, search = "", status, type, categoryUse, isSystemCategory, organizationId, sort = "category_name", order = "ASC", } = req.query;
        // Check if user has default access
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        // Build base where clause with organization isolation
        let whereClause = {};
        const searchConditions = [];
        // Organization-based access control
        if (hasDefaultAccess) {
            // Admin users can see all records, but can filter by organization
            if (organizationId !== undefined) {
                if (organizationId === "null" || organizationId === "") {
                    whereClause.organization_id = null; // System defaults
                }
                else {
                    whereClause.organization_id = organizationId;
                }
            }
        }
        else {
            // Regular users see their org records + system defaults
            whereClause = {
                [sequelize_1.Op.or]: [
                    { organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id },
                    { organization_id: null }, // System defaults
                    { is_system_category: true }, // System categories
                ],
            };
        }
        // Filter by category status (active, inactive)
        if (status) {
            whereClause.category_status = status;
        }
        // Filter by category type (recipe, ingredient)
        if (type) {
            whereClause.category_type = type;
        }
        // Filter by categoryUse (alias for type parameter)
        if (categoryUse) {
            whereClause.category_type = categoryUse;
        }
        // Filter by system category flag - handle both true and false values correctly
        if (isSystemCategory !== undefined) {
            if (isSystemCategory === "true") {
                whereClause.is_system_category = true;
            }
            else if (isSystemCategory === "false") {
                whereClause.is_system_category = false;
            }
        }
        // Search functionality (searches in name and description)
        if (search) {
            searchConditions.push({
                [sequelize_1.Op.or]: [
                    { category_name: { [sequelize_1.Op.like]: `%${search}%` } },
                    { category_description: { [sequelize_1.Op.like]: `%${search}%` } },
                ],
            });
        }
        // Combine search conditions with where clause
        if (searchConditions.length > 0) {
            if (Object.keys(whereClause).length > 0) {
                whereClause = {
                    [sequelize_1.Op.and]: [whereClause, ...searchConditions],
                };
            }
            else {
                whereClause = {
                    [sequelize_1.Op.and]: searchConditions,
                };
            }
        }
        // Handle pagination - if limit is not provided, show all records
        const pageNumber = page ? Number(page) : 1;
        const limitNumber = limit ? Number(limit) : null; // null means no limit
        const offset = limitNumber ? (pageNumber - 1) * limitNumber : 0;
        // Validate sort field to prevent SQL injection
        const allowedSortFields = [
            "category_name",
            "category_description",
            "category_status",
            "category_type",
            "is_system_category",
            "organization_id",
            "created_at",
            "updated_at",
            "created_by",
            "updated_by",
        ];
        const sortField = allowedSortFields.includes(sort)
            ? sort
            : "category_name";
        const sortOrder = order.toUpperCase() === "DESC" ? "DESC" : "ASC";
        // Build query options - conditionally add limit and offset
        const queryOptions = {
            where: whereClause,
            include: [
                {
                    model: Item,
                    as: "iconItem",
                    attributes: [
                        "id",
                        "item_location",
                        [
                            models_1.sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN CONCAT('${getBaseUrl()}/backend-api/v1/public/user/get-file?location=', iconItem.item_location)
                ELSE NULL
              END`),
                            "iconUrl",
                        ],
                        // Add computed hasIcon field at database level
                        [
                            models_1.sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN true
                ELSE false
              END`),
                            "hasIcon",
                        ],
                    ],
                    required: false, // LEFT JOIN to include categories without icons
                },
            ],
            order: [[sortField, sortOrder]],
            // Ensure we get raw data with computed fields
            raw: false,
            nest: true,
        };
        // Only add limit and offset if limit is provided
        if (limitNumber) {
            queryOptions.limit = limitNumber;
            queryOptions.offset = offset;
        }
        // Fetch categories with conditional pagination
        const { rows: categories, count } = yield Category.findAndCountAll(queryOptions);
        // Optimize user name fetching to prevent N+1 queries
        const userIds = new Set();
        categories.forEach((category) => {
            if (category.created_by)
                userIds.add(category.created_by);
            if (category.updated_by)
                userIds.add(category.updated_by);
        });
        // Fetch all user names in a single batch
        const userNamesMap = new Map();
        if (userIds.size > 0) {
            const userNamesPromises = Array.from(userIds).map((userId) => __awaiter(void 0, void 0, void 0, function* () {
                const userName = yield (0, common_1.getUserFullName)(userId);
                return { userId, userName };
            }));
            const userNamesResults = yield Promise.all(userNamesPromises);
            userNamesResults.forEach(({ userId, userName }) => {
                userNamesMap.set(userId, userName);
            });
        }
        // Add user names to the categories
        const categoriesWithUserNames = categories.map((category) => {
            const categoryData = category.toJSON ? category.toJSON() : category;
            // Add created_by and updated_by user names from cache
            if (categoryData.created_by) {
                categoryData.created_by_name =
                    userNamesMap.get(categoryData.created_by) || "Unknown User";
            }
            if (categoryData.updated_by) {
                categoryData.updated_by_name =
                    userNamesMap.get(categoryData.updated_by) || "Unknown User";
            }
            return categoryData;
        });
        // Calculate pagination info only if limit is provided
        let paginationInfo = {
            count: count,
            data: categoriesWithUserNames,
        };
        if (limitNumber) {
            const { total_pages } = (0, common_1.getPaginatedItems)(limitNumber, pageNumber, count || 0);
            paginationInfo = Object.assign(Object.assign({}, paginationInfo), { page: pageNumber, limit: limitNumber, total_pages: total_pages });
        }
        else {
            // When no limit is provided, show all records info
            paginationInfo = Object.assign(Object.assign({}, paginationInfo), { page: 1, limit: "all", total_pages: 1, showing_all_records: true });
        }
        return res.status(http_status_codes_1.StatusCodes.OK).json(Object.assign({ status: true, message: res.__("SUCCESS_DATA_FETCHED") }, paginationInfo));
    }
    catch (error) {
        console.log("Error in category.controller.ts - getAllCategories:", error);
        console.error("Error fetching categories:", error);
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            error: process.env.NODE_ENV === "development" ? error : undefined,
        });
    }
});
/**
 * Get single category by ID
 * @route GET /api/v1/private/category/:id
 * @access Private (Authenticated users)
 */
const getCategoryById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { id } = req.params;
        // Check if user has default access
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        let whereClause = { id };
        if (!hasDefaultAccess) {
            // Regular users can only see their org records + system defaults
            whereClause = {
                id,
                [sequelize_1.Op.or]: [
                    { organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id },
                    { organization_id: null }, // System defaults
                    { is_system_category: true }, // System categories
                ],
            };
        }
        const category = yield Category.findOne({
            where: whereClause,
            include: [
                {
                    model: Item,
                    as: "iconItem",
                    attributes: [
                        "id",
                        "item_name",
                        "item_location",
                        "item_mime_type",
                        // Add computed iconUrl field at database level
                        [
                            models_1.sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN CONCAT('${getBaseUrl()}/backend-api/v1/public/user/get-file?location=', iconItem.item_location)
                ELSE NULL
              END`),
                            "iconUrl",
                        ],
                        // Add computed hasIcon field at database level
                        [
                            models_1.sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN true
                ELSE false
              END`),
                            "hasIcon",
                        ],
                    ],
                    required: false, // LEFT JOIN to include category even without icon
                },
            ],
            raw: false,
            nest: true,
        });
        if (!category) {
            return res.status(http_status_codes_1.StatusCodes.OK).json({
                status: true,
                message: res.__("CATEGORY_NOT_FOUND"),
                data: {},
            });
        }
        // Add user names to the category
        const categoryData = category.toJSON ? category.toJSON() : category;
        // Add created_by and updated_by user names
        if (categoryData.created_by) {
            categoryData.created_by_name = yield (0, common_1.getUserFullName)(categoryData.created_by);
        }
        if (categoryData.updated_by) {
            categoryData.updated_by_name = yield (0, common_1.getUserFullName)(categoryData.updated_by);
        }
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_DATA_FETCHED"),
            data: categoryData,
        });
    }
    catch (error) {
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            error: process.env.NODE_ENV === "development" ? error : undefined,
        });
    }
});
/**
 * Update category by ID
 * @route PUT /api/v1/private/category/:id
 * @access Private (Authenticated users)
 */
const updateCategory = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d;
    const transactionManager = new transaction_helper_1.TransactionManager();
    const fileTracker = new transaction_helper_1.FileOperationTracker();
    try {
        const { id } = req.params;
        // Input validation and sanitization
        const sanitizedBody = validation_helper_1.ValidationHelper.sanitizeInput(req.body);
        const { category_name, category_description, category_status, category_type, is_system_category, } = sanitizedBody;
        // Start transaction
        const transaction = yield transactionManager.start();
        // Check if user has default access
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        // Build where clause based on user access
        let whereClause = { id };
        if (!hasDefaultAccess) {
            // Regular users can only update their org records
            whereClause = {
                id,
                organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id,
            };
        }
        else {
            // Super admin can update any record
            whereClause = { id };
        }
        // Find the category
        const category = yield Category.findOne({
            where: whereClause,
        });
        if (!category) {
            return res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("CATEGORY_NOT_FOUND"),
            });
        }
        // Prevent regular users from updating system default records
        if (category.organization_id === null && !hasDefaultAccess) {
            return res.status(http_status_codes_1.StatusCodes.FORBIDDEN).json({
                status: false,
                message: res.__("CANNOT_UPDATE_SYSTEM_DEFAULT"),
            });
        }
        // 🔍 BUSINESS VALIDATION: Check for duplicate category name (excluding current category)
        if (category_name && category_name.trim() !== category.category_name) {
            // Check both in same organization AND in default/system records
            const existingCategoryByName = yield Category.findOne({
                where: {
                    [sequelize_1.Op.and]: [
                        models_1.db.sequelize.where(models_1.db.sequelize.fn("LOWER", models_1.db.sequelize.col("category_name")), models_1.db.sequelize.fn("LOWER", category_name.trim())),
                        {
                            [sequelize_1.Op.or]: [
                                { organization_id: (_c = req.user) === null || _c === void 0 ? void 0 : _c.organization_id }, // Same organization
                                { organization_id: null }, // Default/system records
                            ],
                        },
                        { id: { [sequelize_1.Op.ne]: id } }, // Exclude current category
                    ],
                },
            });
            if (existingCategoryByName) {
                return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                    status: false,
                    message: res.__("CATEGORY_NAME_ALREADY_EXISTS"),
                });
            }
        }
        // Generate new slug if category name is being updated
        let newSlug = category.category_slug;
        if (category_name && category_name !== category.category_name) {
            const checkSlugExists = (slug) => __awaiter(void 0, void 0, void 0, function* () {
                var _a;
                // For system defaults, check globally; for org records, check within org
                const slugCheckOrgId = hasDefaultAccess && category.organization_id === null
                    ? null
                    : (_a = req.user) === null || _a === void 0 ? void 0 : _a.organization_id;
                const existing = yield Category.findOne({
                    where: {
                        category_slug: slug,
                        organization_id: slugCheckOrgId,
                        id: { [sequelize_1.Op.ne]: id },
                    },
                    transaction, // Use transaction to prevent race conditions
                    lock: true, // Add row-level lock
                });
                return !!existing;
            });
            newSlug = yield (0, slugGenerator_1.generateUniqueSlug)(category_name, checkSlugExists, {
                maxLength: 25,
                separator: "-",
                lowercase: true,
            });
        }
        let newIconItemId = category.category_icon;
        let iconWasUpdated = false;
        // Handle file upload from multerS3 middleware (req.files format)
        if (req.files &&
            typeof req.files === "object" &&
            !Array.isArray(req.files)) {
            const files = req.files;
            if (files.categoryIcon && files.categoryIcon.length > 0) {
                const uploadedFile = files.categoryIcon[0];
                // Handle file movement if needed
                if (uploadedFile.isMovable) {
                    const orgName = category.organization_id
                        ? category.organization_id.toString()
                        : null;
                    const destinationPath = common_1.RECIPE_FILE_UPLOAD_CONSTANT.CATEGORY_ICON.destinationPath(orgName, category.id, uploadedFile.filename);
                    // Track file operation for potential rollback
                    fileTracker.trackMove(uploadedFile.path, destinationPath);
                    const bucketName = process.env.NODE_ENV || "development";
                    const moveResult = yield upload_service_1.default.moveFileInBucket(bucketName, uploadedFile.path, destinationPath, uploadedFile.item_id);
                    if (!moveResult.success) {
                        throw new Error(`Failed to move category icon: ${moveResult.error}`);
                    }
                }
                newIconItemId = uploadedFile.item_id;
                iconWasUpdated = true;
            }
            else if (files.categoryIcon && files.categoryIcon.length === 0) {
                // Empty file array means user wants to remove the icon
                newIconItemId = null;
                iconWasUpdated = true;
            }
        }
        else if (req.body.categoryIcon === "" || req.body.categoryIcon === null) {
            // Handle explicit removal of icon via form data
            newIconItemId = null;
            iconWasUpdated = true;
        }
        // Update the category
        yield Category.update({
            category_name: category_name || category.category_name,
            category_slug: newSlug,
            category_description: category_description !== undefined
                ? category_description
                : category.category_description,
            category_icon: newIconItemId,
            category_status: category_status || category.category_status,
            category_type: category_type || category.category_type,
            is_system_category: is_system_category !== undefined
                ? is_system_category
                : category.is_system_category,
            updated_by: ((_d = req.user) === null || _d === void 0 ? void 0 : _d.id) || null,
        }, {
            where: whereClause,
            transaction,
        });
        // Fetch updated category using the same where clause
        const updatedCategory = yield Category.findOne({
            where: whereClause,
            transaction,
        });
        // Commit transaction
        yield transactionManager.commit();
        // Clear file operations after successful commit
        fileTracker.clear();
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("CATEGORY_UPDATED_SUCCESSFULLY"),
        });
    }
    catch (error) {
        yield transactionManager.rollback();
        yield fileTracker.rollback();
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error updating category");
    }
});
/**
 * Delete category by ID (hard delete if not in use, otherwise show usage message)
 * @route DELETE /api/v1/private/category/:id
 * @access Private (Authenticated users)
 */
const deleteCategory = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    const transactionManager = new transaction_helper_1.TransactionManager();
    try {
        const transaction = yield transactionManager.start();
        const { id } = req.params;
        // Check if user has default access
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        // Build where clause based on user access
        let whereClause = { id };
        if (!hasDefaultAccess) {
            // Regular users can only delete their org records
            whereClause = {
                id,
                organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id,
            };
        }
        else {
            // Super admin can delete any record
            whereClause = { id };
        }
        const category = yield Category.findOne({
            where: whereClause,
            transaction,
        });
        if (!category) {
            yield transactionManager.rollback();
            return res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("CATEGORY_NOT_FOUND"),
            });
        }
        // Prevent deletion of system default records (organization_id: null) by non-default users
        if (category.organization_id === null && !hasDefaultAccess) {
            yield transactionManager.rollback();
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("CANNOT_DELETE_SYSTEM_DEFAULT"),
            });
        }
        // Prevent deletion of system categories by non-default users
        if (category.is_system_category && !hasDefaultAccess) {
            yield transactionManager.rollback();
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("CANNOT_DELETE_SYSTEM_CATEGORY"),
            });
        }
        // Check if category is being used in recipes (any status)
        const recipeUsage = yield RecipeCategory.count({
            where: {
                category_id: id,
            },
            transaction,
        });
        // Check if category is being used in ingredients (any status)
        const ingredientUsage = yield IngredientCategory.count({
            where: {
                category_id: id,
            },
            transaction,
        });
        const totalUsage = recipeUsage + ingredientUsage;
        // If category is in use, prevent deletion
        if (totalUsage > 0) {
            yield transactionManager.rollback();
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("CATEGORY_IN_USE_CANNOT_DELETE"),
            });
        }
        // Hard delete the category and all its inactive relations
        yield IngredientCategory.destroy({
            where: { category_id: id },
            transaction,
        });
        yield RecipeCategory.destroy({
            where: { category_id: id },
            transaction,
        });
        yield Category.destroy({
            where: whereClause,
            transaction,
        });
        yield transactionManager.commit();
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("CATEGORY_DELETED_SUCCESSFULLY"),
        });
    }
    catch (error) {
        yield transactionManager.rollback();
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error deleting category");
    }
});
// Default export object
exports.default = {
    createCategory,
    getAllCategories,
    getCategoryById,
    updateCategory,
    deleteCategory,
};
