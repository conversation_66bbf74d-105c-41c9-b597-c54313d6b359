"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IngredientAttributes = exports.IngredientAttributesStatus = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var IngredientAttributesStatus;
(function (IngredientAttributesStatus) {
    IngredientAttributesStatus["active"] = "active";
    IngredientAttributesStatus["inactive"] = "inactive";
})(IngredientAttributesStatus || (exports.IngredientAttributesStatus = IngredientAttributesStatus = {}));
class IngredientAttributes extends sequelize_1.Model {
}
exports.IngredientAttributes = IngredientAttributes;
IngredientAttributes.init({
    ingredient_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        references: {
            model: "mo_ingredients",
            key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
    },
    attributes_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        references: {
            model: "mo_food_attributes",
            key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
    },
    ingredient_attributes_status: {
        type: sequelize_1.DataTypes.ENUM,
        values: Object.values(IngredientAttributesStatus),
        defaultValue: IngredientAttributesStatus.active,
    },
    unit_of_measure: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
        comment: "Unit of measure as text (e.g., 'grams', 'cups', 'tablespoons')",
    },
    unit: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_ingredients_attributes",
    modelName: "IngredientAttributes",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
        {
            unique: true,
            fields: ["ingredient_id", "attributes_id"],
            name: "primary_ingredient_attributes",
        },
        {
            fields: ["organization_id"],
            name: "idx_ingredient_attributes_organization",
        },
        {
            fields: ["ingredient_attributes_status"],
            name: "idx_ingredient_attributes_status",
        },
    ],
});
// Define associations
IngredientAttributes.associate = (models) => {
    // IngredientAttributes belongs to Ingredient
    IngredientAttributes.belongsTo(models.Ingredient, {
        foreignKey: "ingredient_id",
        as: "ingredient",
    });
    // IngredientAttributes belongs to FoodAttributes
    IngredientAttributes.belongsTo(models.FoodAttributes, {
        foreignKey: "attributes_id",
        as: "attribute",
    });
    // Note: unit_of_measure is now a string field, no foreign key relationship
};
