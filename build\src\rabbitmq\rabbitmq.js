"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const amqplib_1 = __importDefault(require("amqplib"));
// import { RABBITMQ_QUEUE } from "../helper/constant";
// import { createNotification } from "../helper/common";
let connection = null;
const RABBITMQ_URL = process.env.RABBITMQ_URL;
/** The getConnection() function in this code creates and reuses a single connection for RabbitMQ: */
const getConnection = () => __awaiter(void 0, void 0, void 0, function* () {
    if (!connection) {
        connection = yield amqplib_1.default.connect(RABBITMQ_URL);
    }
    return connection;
});
/* Create a new channel for each queue or operation
 A single connection can have multiple channels.
*/
const createChannel = () => __awaiter(void 0, void 0, void 0, function* () {
    const conn = yield getConnection();
    return yield conn.createChannel();
});
const consumeMessage = (queue) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const channel = yield createChannel();
        // Assert the queue (ensure the queue exists)
        yield channel.assertQueue(queue, { durable: true });
        console.log(`Waiting for messages in queue: ${queue}`);
        // Consume the message
        channel.consume(queue, (msg) => __awaiter(void 0, void 0, void 0, function* () {
            if (msg !== null) {
                // const message = JSON.parse(msg.content.toString());
                const routingKey = msg.fields.routingKey;
                console.log(`Received message with routingKey: ${routingKey}`);
                try {
                    // Handle the message based on the routing key
                    channel.ack(msg);
                }
                catch (error) {
                    console.error(`Error handling message for routingKey ${routingKey}:`, error);
                    // Optionally, reject the message instead of acknowledging it
                    channel.nack(msg, false, false); // Requeue: false
                }
            }
        }));
    }
    catch (error) {
        console.error(`Error handling message`, error);
    }
});
const publishMessage = (queue, message) => __awaiter(void 0, void 0, void 0, function* () {
    const channel = yield createChannel();
    try {
        yield channel.assertQueue(queue);
        channel.sendToQueue(queue, Buffer.from(JSON.stringify(message)), {
            persistent: true,
        });
        console.log(`Message published to queue "${queue}"`);
    }
    catch (err) {
        console.error("Error publishing message:", err);
    }
    finally {
        yield channel.close();
    }
});
exports.default = { consumeMessage, publishMessage, createChannel };
