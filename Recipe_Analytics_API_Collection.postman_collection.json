{"info": {"_postman_id": "recipe-analytics-api-collection", "name": "Recipe Analytics API - Complete Collection", "description": "Complete API collection for Recipe Analytics microservice with all endpoints, filters, and parameters", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "recipe-analytics-api"}, "item": [{"name": "Public Analytics APIs", "item": [{"name": "Track Recipe View", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": 123,\n  \"recipe_name\": \"Chocolate Cake\"\n}"}, "url": {"raw": "{{base_url}}/v1/public/analytics/track/recipe-view", "host": ["{{base_url}}"], "path": ["v1", "public", "analytics", "track", "recipe-view"]}, "description": "Track recipe views on public recipes. Only requires recipe_id (required) and optional recipe_name."}}, {"name": "Track CTA Click", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": 123,\n  \"recipe_name\": \"Chocolate Cake\",\n  \"cta_type\": \"contact_form\",\n  \"cta_text\": \"Get Recipe Details\"\n}"}, "url": {"raw": "{{base_url}}/v1/public/analytics/track/cta-click", "host": ["{{base_url}}"], "path": ["v1", "public", "analytics", "track", "cta-click"]}, "description": "Track CTA clicks on public recipes. Requires recipe_id and cta_type. Valid cta_type values: contact_info, contact_form, custom_cta"}}, {"name": "Submit Contact Form", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": 123,\n  \"recipe_name\": \"Chocolate Cake\",\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"mobile\": \"+1234567890\",\n  \"message\": \"I want the complete recipe for this chocolate cake!\"\n}"}, "url": {"raw": "{{base_url}}/v1/public/analytics/contact-form", "host": ["{{base_url}}"], "path": ["v1", "public", "analytics", "contact-form"]}, "description": "Submit contact form from public recipes. Required: recipe_id, name, email, message. Optional: recipe_name, mobile"}}], "description": "Public analytics endpoints that don't require authentication. Used for tracking user interactions on public recipe pages."}, {"name": "Public Recipe APIs", "item": [{"name": "Get Recipes List", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/public/recipes/list?page=1&limit=20&search=chocolate&categories=1,2,3&allergens=4,5&dietary=6,7&visibility=public&sort_by=created_at&sort_order=desc", "host": ["{{base_url}}"], "path": ["v1", "public", "recipes", "list"], "query": [{"key": "page", "value": "1", "description": "Page number (min: 1, default: 1)"}, {"key": "limit", "value": "20", "description": "Items per page (min: 1, max: 100, default: 20)"}, {"key": "search", "value": "chocolate", "description": "Search in recipe title, public title, serve in, garnish"}, {"key": "categories", "value": "1,2,3", "description": "Filter by category IDs (comma-separated)"}, {"key": "allergens", "value": "4,5", "description": "Filter by allergen attribute IDs (comma-separated)"}, {"key": "dietary", "value": "6,7", "description": "Filter by dietary attribute IDs (comma-separated)"}, {"key": "visibility", "value": "public", "description": "Recipe visibility: public, private"}, {"key": "sort_by", "value": "created_at", "description": "Sort by field: created_at, title, updated_at"}, {"key": "sort_order", "value": "desc", "description": "Sort order: asc, desc"}]}, "description": "Get paginated list of recipes with advanced filtering and search capabilities. Ultra-optimized with millisecond response times."}}, {"name": "Get Recipe by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/public/recipes/get-by-id/123", "host": ["{{base_url}}"], "path": ["v1", "public", "recipes", "get-by-id", "123"]}, "description": "Get detailed information about a specific recipe by its ID. Returns complete recipe data including ingredients, instructions, and metadata."}}, {"name": "Track Recipe Impression", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\",\n  \"referrer\": \"https://example.com\"\n}"}, "url": {"raw": "{{base_url}}/v1/public/recipes/impression/123", "host": ["{{base_url}}"], "path": ["v1", "public", "recipes", "impression", "123"]}, "description": "Track recipe impression/view for analytics purposes. Used to count recipe views and gather user engagement data."}}], "description": "Public recipe endpoints that don't require authentication. Used for browsing and viewing recipes on public pages."}, {"name": "Private Analytics APIs", "item": [{"name": "Get Analytics Summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/analytics?page=1&limit=20&event_type=recipe_view&entity_type=recipe&start_date=2024-01-01&end_date=2024-12-31", "host": ["{{base_url}}"], "path": ["v1", "private", "analytics"], "query": [{"key": "page", "value": "1", "description": "Page number (min: 1)"}, {"key": "limit", "value": "20", "description": "Items per page (min: 1, max: 50)"}, {"key": "event_type", "value": "recipe_view", "description": "Filter by event type: recipe_view, cta_click, contact_form_submit"}, {"key": "entity_type", "value": "recipe", "description": "Filter by entity type: recipe"}, {"key": "entity_id", "value": "123", "description": "Filter by specific entity ID", "disabled": true}, {"key": "start_date", "value": "2024-01-01", "description": "Start date (ISO format)"}, {"key": "end_date", "value": "2024-12-31", "description": "End date (ISO format)"}]}, "description": "Get analytics summary with filtering and pagination. All query parameters are optional."}}, {"name": "Get CTA Click Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/analytics/cta-clicks?date_range=last_30_days&search=chocolate&cta_type=contact_form&sort_order=desc&sort_by=clicks&page=1&limit=20", "host": ["{{base_url}}"], "path": ["v1", "private", "analytics", "cta-clicks"], "query": [{"key": "date_range", "value": "last_30_days", "description": "Date range: last_7_days, last_30_days, last_90_days, custom"}, {"key": "start_date", "value": "2024-01-01", "description": "Start date (required when date_range=custom)", "disabled": true}, {"key": "end_date", "value": "2024-12-31", "description": "End date (required when date_range=custom)", "disabled": true}, {"key": "search", "value": "chocolate", "description": "Search term for recipe name or CTA type"}, {"key": "cta_type", "value": "contact_form", "description": "Filter by CTA type: contact_info, contact_form, custom_cta"}, {"key": "sort", "value": "desc", "description": "Sort order: asc, desc"}, {"key": "sort_by", "value": "clicks", "description": "Sort by field: clicks, recipe_name, cta_type"}, {"key": "page", "value": "1", "description": "Page number (min: 1)"}, {"key": "limit", "value": "20", "description": "Items per page (min: 1, max: 100)"}]}, "description": "Get CTA click analytics with enhanced search and filtering. Shows recipe name, CTA type, clicks, and last clicked timestamp."}}, {"name": "Get Contact Submission Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/analytics/contact-submissions?date_range=last_30_days&search=<EMAIL>&recipe_id=123&sort_order=desc&sort_by=submitted_at&page=1&limit=20", "host": ["{{base_url}}"], "path": ["v1", "private", "analytics", "contact-submissions"], "query": [{"key": "date_range", "value": "last_30_days", "description": "Date range: last_7_days, last_30_days, last_90_days, custom"}, {"key": "start_date", "value": "2024-01-01", "description": "Start date (required when date_range=custom)", "disabled": true}, {"key": "end_date", "value": "2024-12-31", "description": "End date (required when date_range=custom)", "disabled": true}, {"key": "search", "value": "<EMAIL>", "description": "Search term for recipe name, user name, or email"}, {"key": "recipe_id", "value": "123", "description": "Filter by specific recipe ID"}, {"key": "sort", "value": "desc", "description": "Sort order: asc, desc"}, {"key": "sort_by", "value": "submitted_at", "description": "Sort by field: submitted_at, recipe_name, contact_name"}, {"key": "page", "value": "1", "description": "Page number (min: 1)"}, {"key": "limit", "value": "20", "description": "Items per page (min: 1, max: 100)"}]}, "description": "Get contact form submission analytics with search by recipe name and user email. Shows recipe name, contact details, and submission timestamp."}}, {"name": "Get Recipe View Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/analytics/recipe-views?date_range=last_30_days&start_date=2024-01-01&end_date=2024-12-31&sort=desc", "host": ["{{base_url}}"], "path": ["v1", "private", "analytics", "recipe-views"], "query": [{"key": "date_range", "value": "last_30_days", "description": "Date range: last_7_days, last_30_days, last_90_days, last_year, custom"}, {"key": "start_date", "value": "2024-01-01", "description": "Start date (required when date_range=custom)", "disabled": true}, {"key": "end_date", "value": "2024-12-31", "description": "End date (required when date_range=custom)", "disabled": true}, {"key": "sort", "value": "desc", "description": "Sort order by views: asc, desc"}]}, "description": "Get recipe view analytics showing total views, recent views, and last viewed timestamp for each recipe."}}, {"name": "Export Contact Submissions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/analytics/contact-submissions/export?format=csv&date_range=last_30_days&recipe_name=chocolate&user_email=<EMAIL>&search=general", "host": ["{{base_url}}"], "path": ["v1", "private", "analytics", "contact-submissions", "export"], "query": [{"key": "format", "value": "csv", "description": "Export format: csv, json"}, {"key": "date_range", "value": "last_30_days", "description": "Date range: last_7_days, last_30_days, last_90_days, custom"}, {"key": "start_date", "value": "2024-01-01", "description": "Start date (required when date_range=custom)", "disabled": true}, {"key": "end_date", "value": "2024-12-31", "description": "End date (required when date_range=custom)", "disabled": true}, {"key": "search", "value": "chocolate", "description": "Search term for recipe name or user email"}]}, "description": "Export contact form submissions to CSV or JSON format with filtering options."}}, {"name": "Delete Contact Submission", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/analytics/contact-submissions/123", "host": ["{{base_url}}"], "path": ["v1", "private", "analytics", "contact-submissions", "123"]}, "description": "Delete a specific contact form submission by ID. Only submissions from user's organization can be deleted."}}, {"name": "Get Recipe View Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/analytics/recipe-view-statistics/123", "host": ["{{base_url}}"], "path": ["v1", "private", "analytics", "recipe-view-statistics", "123"], "query": [{"key": "organization_id", "value": "org123", "description": "Organization ID (optional for admin users)", "disabled": true}]}, "description": "Get detailed view statistics for private recipes with assigned users. Shows unique viewers, total views, and individual user view counts."}}, {"name": "Reset Recipe View Statistics", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_ids\": [123, 456, 789]\n}"}, "url": {"raw": "{{base_url}}/v1/private/analytics/recipe-view-statistics/123/reset", "host": ["{{base_url}}"], "path": ["v1", "private", "analytics", "recipe-view-statistics", "123", "reset"], "query": [{"key": "organization_id", "value": "org123", "description": "Organization ID (optional for admin users)", "disabled": true}]}, "description": "Reset view statistics for private recipes. Can reset for specific users or all users if user_ids array is not provided."}}]}, {"name": "Private Recipe APIs", "item": [{"name": "Create Recipe", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Chocolate Cake Recipe", "type": "text"}, {"key": "public_title", "value": "Delicious Chocolate Cake", "type": "text"}, {"key": "description", "value": "A rich and moist chocolate cake recipe", "type": "text"}, {"key": "categories", "value": "[1,2,3]", "type": "text", "description": "Array of category IDs"}, {"key": "ingredients", "value": "[{\"ingredient_id\":1,\"quantity\":\"2\",\"unit\":\"cups\"}]", "type": "text", "description": "Array of ingredient objects"}, {"key": "instructions", "value": "[{\"step\":1,\"instruction\":\"Mix ingredients\"}]", "type": "text", "description": "Array of instruction objects"}, {"key": "recipeFiles", "type": "file", "description": "Recipe image files"}]}, "url": {"raw": "{{base_url}}/v1/private/recipes/create", "host": ["{{base_url}}"], "path": ["v1", "private", "recipes", "create"]}, "description": "Create a new recipe with ingredients, instructions, categories, and file uploads."}}, {"name": "Get Private Recipes List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/recipes/list?page=1&limit=20&search=chocolate&categories=1,2&visibility=private&sort_by=created_at&sort_order=desc", "host": ["{{base_url}}"], "path": ["v1", "private", "recipes", "list"], "query": [{"key": "page", "value": "1", "description": "Page number (min: 1)"}, {"key": "limit", "value": "20", "description": "Items per page (min: 1, max: 100)"}, {"key": "search", "value": "chocolate", "description": "Search in recipe title and description"}, {"key": "categories", "value": "1,2", "description": "Filter by category IDs (comma-separated)"}, {"key": "visibility", "value": "private", "description": "Recipe visibility: public, private, all"}, {"key": "sort_by", "value": "created_at", "description": "Sort by field: created_at, title, updated_at"}, {"key": "sort_order", "value": "desc", "description": "Sort order: asc, desc"}]}, "description": "Get paginated list of private recipes with filtering and search. Organization-scoped for authenticated users."}}, {"name": "Get Recipe by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/recipes/get-by-id/123", "host": ["{{base_url}}"], "path": ["v1", "private", "recipes", "get-by-id", "123"]}, "description": "Get detailed information about a specific recipe by ID. Organization-scoped access."}}, {"name": "Update Recipe", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Updated Chocolate Cake Recipe", "type": "text"}, {"key": "description", "value": "Updated description", "type": "text"}, {"key": "categories", "value": "[1,2,3]", "type": "text"}, {"key": "recipeFiles", "type": "file", "description": "Updated recipe image files"}]}, "url": {"raw": "{{base_url}}/v1/private/recipes/update/123", "host": ["{{base_url}}"], "path": ["v1", "private", "recipes", "update", "123"]}, "description": "Update an existing recipe with new data and files."}}, {"name": "Delete Recipe", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/recipes/delete/123", "host": ["{{base_url}}"], "path": ["v1", "private", "recipes", "delete", "123"]}, "description": "Permanently delete a recipe. Only accessible by recipe owner or admin."}}, {"name": "Publish Recipe", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"visibility\": \"public\",\n  \"publish_date\": \"2024-12-31T00:00:00.000Z\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/recipes/publish/123", "host": ["{{base_url}}"], "path": ["v1", "private", "recipes", "publish", "123"]}, "description": "Publish a recipe to make it publicly visible with optional scheduled publishing."}}]}], "variable": [{"key": "base_url", "value": "http://localhost:3000/api", "type": "string"}, {"key": "auth_token", "value": "your_jwt_token_here", "type": "string"}]}