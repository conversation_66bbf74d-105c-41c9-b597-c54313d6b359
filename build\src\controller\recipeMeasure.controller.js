"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_codes_1 = require("http-status-codes");
const sequelize_1 = require("sequelize");
const models_1 = require("../models");
const RecipeMeasure_1 = require("../models/RecipeMeasure");
const slugGenerator_1 = require("../helper/slugGenerator");
const common_1 = require("../helper/common");
// Get models from db object to ensure associations are set up
const RecipeMeasure = models_1.db.RecipeMeasure;
const Item = models_1.db.Item;
const RecipeAttributes = models_1.db.RecipeAttributes;
const RecipeIngredients = models_1.db.RecipeIngredients;
const IngredientConversion = models_1.db.IngredientConversion;
const Ingredient = models_1.db.Ingredient;
// Helper function to get the proper base URL
const getBaseUrl = () => {
    var _a;
    const baseUrl = (_a = global.config) === null || _a === void 0 ? void 0 : _a.API_BASE_URL;
    if (baseUrl &&
        baseUrl.includes("/backend-api/v1/public/user/get-file?location=")) {
        // API_BASE_URL already contains the full endpoint, return base part
        return baseUrl.replace("/backend-api/v1/public/user/get-file?location=", "");
    }
    else {
        // For development or when API_BASE_URL is just the base domain
        return (process.env.BASE_URL ||
            process.env.FRONTEND_URL ||
            "https://staging.namastevillage.theeasyaccess.com");
    }
};
/**
 * Create a new recipe measure unit
 * @route POST /api/v1/private/recipe-measure
 * @access Private (Authenticated users)
 */
const createRecipeMeasure = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d;
    try {
        const { unit_title, status } = req.body;
        // Check if user has default access (can create system defaults)
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        // Determine organization ID for creation
        const organizationIdForCreation = hasDefaultAccess
            ? null
            : (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id;
        // Check for duplicate unit title (case-insensitive)
        // Check both in same organization AND in default/system records
        const existingUnitByTitle = yield RecipeMeasure.findOne({
            where: {
                [sequelize_1.Op.and]: [
                    models_1.db.sequelize.where(models_1.db.sequelize.fn("LOWER", models_1.db.sequelize.col("unit_title")), models_1.db.sequelize.fn("LOWER", unit_title.trim())),
                    {
                        [sequelize_1.Op.or]: [
                            { organization_id: organizationIdForCreation }, // Same organization
                            { organization_id: null }, // Default/system records
                        ],
                    },
                ],
            },
        });
        if (existingUnitByTitle) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("UNIT_TITLE_ALREADY_EXISTS"),
            });
        }
        // Generate unique slug from unit title
        const checkSlugExists = (slug) => __awaiter(void 0, void 0, void 0, function* () {
            const existing = yield RecipeMeasure.findOne({
                where: {
                    unit_slug: slug,
                    organization_id: organizationIdForCreation,
                },
            });
            return !!existing;
        });
        const unitSlug = yield (0, slugGenerator_1.generateUniqueSlug)(unit_title, checkSlugExists, {
            maxLength: 25,
            separator: "-",
            lowercase: true,
        });
        // Set system unit flag based on default access
        const isSystemUnit = hasDefaultAccess;
        // Create unit first
        const unit = yield RecipeMeasure.create({
            unit_title: unit_title,
            unit_slug: unitSlug,
            status: status || RecipeMeasure_1.MeasureStatus.active,
            organization_id: organizationIdForCreation,
            is_system_unit: isSystemUnit,
            created_by: ((_c = req.user) === null || _c === void 0 ? void 0 : _c.id) || null,
            updated_by: ((_d = req.user) === null || _d === void 0 ? void 0 : _d.id) || null,
        });
        let iconItemId = null;
        // Handle file upload from S3 middleware (req.files format)
        if (req.files &&
            typeof req.files === "object" &&
            !Array.isArray(req.files)) {
            const files = req.files;
            if (files.unitIcon && files.unitIcon.length > 0) {
                const uploadedFile = files.unitIcon[0];
                iconItemId = uploadedFile.item_id;
            }
        }
        // Update unit with icon item_id if uploaded
        if (iconItemId) {
            yield unit.update({ unit_icon: iconItemId });
        }
        return res.status(http_status_codes_1.StatusCodes.CREATED).json({
            status: true,
            message: res.__("UNIT_CREATED_SUCCESSFULLY"),
        });
    }
    catch (error) {
        console.log("Error in recipeMeasure.controller.ts - createRecipeMeasure:", error);
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            error: process.env.NODE_ENV === "development" ? error : undefined,
        });
    }
});
/**
 * Get all recipe measure units with filtering and search
 * @route GET /api/v1/private/recipe-measures/list
 * @access Private (Authenticated users)
 *
 * Query Parameters:
 * - page: Page number (default: 1)
 * - limit: Items per page (default: show all if not provided)
 * - search: Search in unit title and slug
 * - status: Filter by unit status (active, inactive)
 * - isSystemUnit: Filter by system units (true/false) - Admin only
 * - sort: Sort field (default: unit_title)
 * - order: Sort order (ASC/DESC, default: ASC)
 */
const getAllRecipeMeasures = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { page, limit, search = "", status, isSystemUnit, sort = "unit_title", order = "ASC", } = req.query;
        // Check if user has default access
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        // Build where clause with organization isolation
        let whereClause = {};
        if (hasDefaultAccess) {
            // Users with default access see all records (no filter)
            whereClause = {};
        }
        else {
            // Regular users see their org records + system defaults
            whereClause = {
                [sequelize_1.Op.or]: [
                    { organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id },
                    { organization_id: null }, // System defaults
                    { is_system_unit: true }, // System units
                ],
            };
        }
        // Add search functionality
        if (search) {
            whereClause[sequelize_1.Op.or] = [
                { unit_title: { [sequelize_1.Op.like]: `%${search}%` } },
                { unit_slug: { [sequelize_1.Op.like]: `%${search}%` } },
            ];
        }
        // Filter by status
        if (status) {
            whereClause.status = status;
        }
        // Filter by system unit flag - accessible by all users
        if (isSystemUnit !== undefined) {
            if (isSystemUnit === "true") {
                whereClause.is_system_unit = true;
            }
            else if (isSystemUnit === "false") {
                whereClause.is_system_unit = false;
            }
            // If isSystemUnit is neither "true" nor "false", ignore the filter
        }
        // Handle pagination - if limit is not provided, show all records
        const pageNumber = page ? Number(page) : 1;
        const limitNumber = limit ? Number(limit) : null; // null means no limit
        const offset = limitNumber ? (pageNumber - 1) * limitNumber : 0;
        // Build query options - conditionally add limit and offset
        const queryOptions = {
            where: whereClause,
            include: [
                {
                    model: Item,
                    as: "iconItem",
                    attributes: [
                        "id",
                        "item_name",
                        "item_location",
                        "item_mime_type",
                        // Add computed iconUrl field at database level
                        [
                            models_1.sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN CONCAT('${getBaseUrl()}/backend-api/v1/public/user/get-file?location=', iconItem.item_location)
                ELSE NULL
              END`),
                            "iconUrl",
                        ],
                        // Add computed hasIcon field at database level
                        [
                            models_1.sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN true
                ELSE false
              END`),
                            "hasIcon",
                        ],
                    ],
                    required: false, // LEFT JOIN to include units without icons
                },
            ],
            order: [[sort, order]],
            raw: false,
            nest: true,
        };
        // Only add limit and offset if limit is provided
        if (limitNumber) {
            queryOptions.limit = limitNumber;
            queryOptions.offset = offset;
        }
        // Fetch units with conditional pagination
        const { rows: units, count } = yield RecipeMeasure.findAndCountAll(queryOptions);
        // Add user names to the units
        const unitsWithUserNames = yield Promise.all(units.map((unit) => __awaiter(void 0, void 0, void 0, function* () {
            const unitData = unit.toJSON ? unit.toJSON() : unit;
            // Add created_by and updated_by user names
            if (unitData.created_by) {
                unitData.created_by_name = yield (0, common_1.getUserFullName)(unitData.created_by);
            }
            if (unitData.updated_by) {
                unitData.updated_by_name = yield (0, common_1.getUserFullName)(unitData.updated_by);
            }
            return unitData;
        })));
        // Calculate pagination info only if limit is provided
        let paginationInfo = {
            count: count,
            data: unitsWithUserNames,
        };
        if (limitNumber) {
            const { total_pages } = (0, common_1.getPaginatedItems)(limitNumber, pageNumber, count || 0);
            paginationInfo = Object.assign(Object.assign({}, paginationInfo), { page: pageNumber, limit: limitNumber, total_pages: total_pages });
        }
        else {
            // When no limit is provided, show all records info
            paginationInfo = Object.assign(Object.assign({}, paginationInfo), { page: 1, limit: "all", total_pages: 1, showing_all_records: true });
        }
        return res.status(http_status_codes_1.StatusCodes.OK).json(Object.assign({ status: true, message: res.__("SUCCESS_DATA_FETCHED") }, paginationInfo));
    }
    catch (error) {
        console.log("Error in recipeMeasure.controller.ts - getAllRecipeMeasures:", error);
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            error: process.env.NODE_ENV === "development" ? error : undefined,
        });
    }
});
/**
 * Get single recipe measure unit by ID
 * @route GET /api/v1/private/recipe-measure/:id
 * @access Private (Authenticated users)
 */
const getRecipeMeasureById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { id } = req.params;
        // Check if user has default access
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        let whereClause = { id };
        if (!hasDefaultAccess) {
            // Regular users can only see their org records + system defaults
            whereClause = {
                id,
                [sequelize_1.Op.or]: [
                    { organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id },
                    { organization_id: null }, // System defaults
                    { is_system_unit: true }, // System units
                ],
            };
        }
        const unit = yield RecipeMeasure.findOne({
            where: whereClause,
            include: [
                {
                    model: Item,
                    as: "iconItem",
                    attributes: [
                        "id",
                        "item_name",
                        "item_location",
                        "item_mime_type",
                        // Add computed iconUrl field at database level
                        [
                            models_1.sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN CONCAT('${getBaseUrl()}/backend-api/v1/public/user/get-file?location=', iconItem.item_location)
                ELSE NULL
              END`),
                            "iconUrl",
                        ],
                        // Add computed hasIcon field at database level
                        [
                            models_1.sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN true
                ELSE false
              END`),
                            "hasIcon",
                        ],
                    ],
                    required: false, // LEFT JOIN to include unit even without icon
                },
            ],
            raw: false,
            nest: true,
        });
        if (!unit) {
            return res.status(http_status_codes_1.StatusCodes.OK).json({
                status: true,
                message: res.__("UNIT_NOT_FOUND"),
                data: {},
            });
        }
        // Add user names to the unit
        const unitData = unit.toJSON ? unit.toJSON() : unit;
        // Add created_by and updated_by user names
        if (unitData.created_by) {
            unitData.created_by_name = yield (0, common_1.getUserFullName)(unitData.created_by);
        }
        if (unitData.updated_by) {
            unitData.updated_by_name = yield (0, common_1.getUserFullName)(unitData.updated_by);
        }
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_DATA_FETCHED"),
            data: unitData,
        });
    }
    catch (error) {
        console.log("Error in recipeMeasure.controller.ts - getRecipeMeasureById:", error);
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            error: process.env.NODE_ENV === "development" ? error : undefined,
        });
    }
});
/**
 * Update recipe measure unit by ID
 * @route PUT /api/v1/private/recipe-measure/:id
 * @access Private (Authenticated users)
 */
const updateRecipeMeasure = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d, _e;
    try {
        const { id } = req.params;
        const { unit_title, status } = req.body;
        // Check if user has default access
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        // Build where clause based on user access
        let whereClause = { id };
        if (!hasDefaultAccess) {
            // Regular users can only update their org records
            whereClause = {
                id,
                organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id,
            };
        }
        else {
            // Super admin can update any record
            whereClause = { id };
        }
        // Find the unit
        const unit = yield RecipeMeasure.findOne({
            where: whereClause,
        });
        if (!unit) {
            return res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("UNIT_NOT_FOUND"),
            });
        }
        // Prevent regular users from updating system default records
        if (unit.organization_id === null && !hasDefaultAccess) {
            return res.status(http_status_codes_1.StatusCodes.FORBIDDEN).json({
                status: false,
                message: res.__("CANNOT_UPDATE_SYSTEM_DEFAULT"),
            });
        }
        // 🔍 BUSINESS VALIDATION: Check for duplicate unit title (excluding current unit)
        if (unit_title && unit_title.trim() !== unit.unit_title) {
            // For system defaults, check globally; for org records, check within org
            const duplicateCheckOrgId = hasDefaultAccess && unit.organization_id === null
                ? null
                : (_c = req.user) === null || _c === void 0 ? void 0 : _c.organization_id;
            // Check both in same organization AND in default/system records
            const existingUnitByTitle = yield RecipeMeasure.findOne({
                where: {
                    [sequelize_1.Op.and]: [
                        models_1.db.sequelize.where(models_1.db.sequelize.fn("LOWER", models_1.db.sequelize.col("unit_title")), models_1.db.sequelize.fn("LOWER", unit_title.trim())),
                        {
                            [sequelize_1.Op.or]: [
                                { organization_id: (_d = req.user) === null || _d === void 0 ? void 0 : _d.organization_id }, // Same organization
                                { organization_id: null }, // Default/system records
                            ],
                        },
                        { id: { [sequelize_1.Op.ne]: id } }, // Exclude current unit
                    ],
                },
            });
            if (existingUnitByTitle) {
                return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                    status: false,
                    message: res.__("UNIT_TITLE_ALREADY_EXISTS"),
                });
            }
        }
        // Generate new slug if unit title is being updated
        let newSlug = unit.unit_slug;
        if (unit_title && unit_title !== unit.unit_title) {
            const checkSlugExists = (slug) => __awaiter(void 0, void 0, void 0, function* () {
                var _a;
                // For system defaults, check globally; for org records, check within org
                const slugCheckOrgId = hasDefaultAccess && unit.organization_id === null
                    ? null
                    : (_a = req.user) === null || _a === void 0 ? void 0 : _a.organization_id;
                const existing = yield RecipeMeasure.findOne({
                    where: {
                        unit_slug: slug,
                        organization_id: slugCheckOrgId,
                        id: { [sequelize_1.Op.ne]: id },
                    },
                });
                return !!existing;
            });
            newSlug = yield (0, slugGenerator_1.generateUniqueSlug)(unit_title, checkSlugExists, {
                maxLength: 25,
                separator: "-",
                lowercase: true,
            });
        }
        let newIconItemId = unit.unit_icon;
        let iconWasUpdated = false;
        // Handle file upload from S3 middleware (req.files format)
        if (req.files &&
            typeof req.files === "object" &&
            !Array.isArray(req.files)) {
            const files = req.files;
            if (files.unitIcon && files.unitIcon.length > 0) {
                const uploadedFile = files.unitIcon[0];
                newIconItemId = uploadedFile.item_id;
                iconWasUpdated = true;
            }
            else if (files.unitIcon && files.unitIcon.length === 0) {
                // Empty file array means user wants to remove the icon
                newIconItemId = null;
                iconWasUpdated = true;
            }
        }
        else if (req.body.unitIcon === "" || req.body.unitIcon === null) {
            // Handle explicit removal of icon via form data
            newIconItemId = null;
            iconWasUpdated = true;
        }
        // Update the unit
        yield RecipeMeasure.update({
            unit_title: unit_title || unit.unit_title,
            unit_slug: newSlug,
            unit_icon: newIconItemId,
            status: status || unit.status,
            updated_by: ((_e = req.user) === null || _e === void 0 ? void 0 : _e.id) || null,
        }, {
            where: whereClause,
        });
        // Fetch updated unit using the same where clause
        const updatedUnit = yield RecipeMeasure.findOne({
            where: whereClause,
        });
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("UNIT_UPDATED_SUCCESSFULLY"),
        });
    }
    catch (error) {
        console.log("Error in recipeMeasure.controller.ts - updateRecipeMeasure:", error);
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            error: process.env.NODE_ENV === "development" ? error : undefined,
        });
    }
});
/**
 * Delete recipe measure unit by ID (hard delete if not in use, otherwise show usage message)
 * @route DELETE /api/v1/private/recipe-measure/:id
 * @access Private (Authenticated users)
 */
const deleteRecipeMeasure = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    const transaction = yield models_1.sequelize.transaction();
    try {
        const { id } = req.params;
        // Check if user has default access
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        // Build where clause based on user access
        let whereClause = { id };
        if (!hasDefaultAccess) {
            // Regular users can only delete their org records
            whereClause = {
                id,
                organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id,
            };
        }
        else {
            // Super admin can delete any record
            whereClause = { id };
        }
        const unit = yield RecipeMeasure.findOne({
            where: whereClause,
            transaction,
        });
        if (!unit) {
            yield transaction.rollback();
            return res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("UNIT_NOT_FOUND"),
            });
        }
        // Prevent deletion of system default records (organization_id: null) by non-default users
        if (unit.organization_id === null && !hasDefaultAccess) {
            yield transaction.rollback();
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("CANNOT_DELETE_SYSTEM_DEFAULT"),
            });
        }
        // Prevent deletion of system units by non-default users
        if (unit.is_system_unit && !hasDefaultAccess) {
            yield transaction.rollback();
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("CANNOT_DELETE_SYSTEM_UNIT"),
            });
        }
        // Check if measure is being used in recipe attributes (any status)
        const recipeAttributeUsage = yield RecipeAttributes.count({
            where: {
                unit_of_measure: id,
            },
            transaction,
        });
        // Check if measure is being used in recipe ingredients (any status)
        const recipeIngredientUsage = yield RecipeIngredients.count({
            where: {
                ingredient_measure: id,
            },
            transaction,
        });
        // Check if measure is being used in ingredient conversions (any status)
        const ingredientConversionUsage = yield IngredientConversion.count({
            where: {
                [sequelize_1.Op.or]: [{ from_measure: id }, { to_measure: id }],
            },
            transaction,
        });
        // Check if measure is being used as unit_of_measure in ingredients (any status)
        const ingredientUsage = yield Ingredient.count({
            where: {
                unit_of_measure: id,
            },
            transaction,
        });
        // Calculate total usage
        const totalUsage = recipeAttributeUsage +
            recipeIngredientUsage +
            ingredientConversionUsage +
            ingredientUsage;
        // If measure is in use, prevent deletion
        if (totalUsage > 0) {
            yield transaction.rollback();
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("UNIT_IN_USE_CANNOT_DELETE"),
            });
        }
        // Hard delete the measure and all its inactive relations
        yield RecipeAttributes.destroy({
            where: { unit_of_measure: id },
            transaction,
        });
        yield RecipeIngredients.update({ ingredient_measure: null }, {
            where: { ingredient_measure: id },
            transaction,
        });
        yield IngredientConversion.destroy({
            where: {
                [sequelize_1.Op.or]: [{ from_measure: id }, { to_measure: id }],
            },
            transaction,
        });
        yield Ingredient.update({ unit_of_measure: null }, {
            where: { unit_of_measure: id },
            transaction,
        });
        yield RecipeMeasure.destroy({
            where: whereClause,
            transaction,
        });
        yield transaction.commit();
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("UNIT_DELETED_SUCCESSFULLY"),
        });
    }
    catch (error) {
        console.log("Error in recipeMeasure.controller.ts - deleteRecipeMeasure:", error);
        yield transaction.rollback();
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            error: process.env.NODE_ENV === "development" ? error : undefined,
        });
    }
});
// Default export object
exports.default = {
    createRecipeMeasure,
    getAllRecipeMeasures,
    getRecipeMeasureById,
    updateRecipeMeasure,
    deleteRecipeMeasure,
};
