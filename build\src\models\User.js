"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
class User extends sequelize_1.Model {
}
exports.User = User;
User.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    keycloak_userId: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    user_email: {
        type: sequelize_1.DataTypes.STRING,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
    },
    updatedAt: {
        type: sequelize_1.DataTypes.DATE,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "users",
    modelName: "User",
});
