"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRecipesUsingIngredient = exports.areRecipeNutritionValuesOutdated = exports.areRecipeCostsOutdated = exports.updateRecipeNutritionTimestamp = exports.updateRecipeCostTimestamp = exports.updateIngredientNutritionTimestamp = exports.updateIngredientCostTimestamp = void 0;
const models_1 = require("../models");
const Recipe_1 = require("../models/Recipe");
const Ingreditant_1 = require("../models/Ingreditant");
/**
 * Update ingredient cost timestamp when cost_per_unit changes
 */
const updateIngredientCostTimestamp = (ingredientId, transaction) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield Ingreditant_1.Ingredient.update({ cost_last_updated_at: new Date() }, {
            where: { id: ingredientId },
            transaction,
        });
    }
    catch (error) {
        console.error(`Error updating ingredient cost timestamp for ID ${ingredientId}:`, error);
        throw error;
    }
});
exports.updateIngredientCostTimestamp = updateIngredientCostTimestamp;
/**
 * Update ingredient nutrition timestamp when nutrition attributes change
 */
const updateIngredientNutritionTimestamp = (ingredientId, transaction) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield Ingreditant_1.Ingredient.update({ nutrition_last_updated_at: new Date() }, {
            where: { id: ingredientId },
            transaction,
        });
    }
    catch (error) {
        console.error(`Error updating ingredient nutrition timestamp for ID ${ingredientId}:`, error);
        throw error;
    }
});
exports.updateIngredientNutritionTimestamp = updateIngredientNutritionTimestamp;
/**
 * Update recipe cost calculation timestamp
 */
const updateRecipeCostTimestamp = (recipeId, transaction) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield Recipe_1.Recipe.update({ ingredient_costs_updated_at: new Date() }, {
            where: { id: recipeId },
            transaction,
        });
    }
    catch (error) {
        console.error(`Error updating recipe cost timestamp for ID ${recipeId}:`, error);
        throw error;
    }
});
exports.updateRecipeCostTimestamp = updateRecipeCostTimestamp;
/**
 * Update recipe nutrition calculation timestamp
 */
const updateRecipeNutritionTimestamp = (recipeId, transaction) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield Recipe_1.Recipe.update({ nutrition_values_updated_at: new Date() }, {
            where: { id: recipeId },
            transaction,
        });
    }
    catch (error) {
        console.error(`Error updating recipe nutrition timestamp for ID ${recipeId}:`, error);
        throw error;
    }
});
exports.updateRecipeNutritionTimestamp = updateRecipeNutritionTimestamp;
/**
 * Check if recipe costs are outdated compared to ingredient costs
 */
const areRecipeCostsOutdated = (recipeId, organizationId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const query = `
      SELECT 
        r.ingredient_costs_updated_at as recipe_cost_timestamp,
        i.id as ingredient_id,
        i.ingredient_name,
        i.cost_last_updated_at as ingredient_cost_timestamp
      FROM mo_recipe r
      JOIN mo_recipe_ingredients ri ON r.id = ri.recipe_id
      JOIN mo_ingredients i ON ri.ingredient_id = i.id
      WHERE r.id = :recipeId 
        AND r.organization_id = :organizationId
        AND ri.recipe_ingredient_status = 'active'
        AND i.ingredient_status = 'active'
    `;
        const results = yield models_1.sequelize.query(query, {
            replacements: { recipeId, organizationId },
            type: models_1.sequelize.QueryTypes.SELECT,
        });
        if (results.length === 0) {
            return { isOutdated: false, outdatedIngredients: [] };
        }
        const recipeCostTimestamp = results[0].recipe_cost_timestamp;
        const outdatedIngredients = [];
        let isOutdated = false;
        for (const result of results) {
            const ingredientCostTimestamp = result.ingredient_cost_timestamp;
            // If ingredient cost was updated after recipe cost calculation
            if (ingredientCostTimestamp && recipeCostTimestamp &&
                new Date(ingredientCostTimestamp) > new Date(recipeCostTimestamp)) {
                isOutdated = true;
                outdatedIngredients.push({
                    id: result.ingredient_id,
                    name: result.ingredient_name,
                    lastUpdated: new Date(ingredientCostTimestamp),
                });
            }
        }
        return { isOutdated, outdatedIngredients };
    }
    catch (error) {
        console.error(`Error checking recipe cost freshness for recipe ID ${recipeId}:`, error);
        throw error;
    }
});
exports.areRecipeCostsOutdated = areRecipeCostsOutdated;
/**
 * Check if recipe nutrition values are outdated compared to ingredient nutrition
 */
const areRecipeNutritionValuesOutdated = (recipeId, organizationId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const query = `
      SELECT 
        r.nutrition_values_updated_at as recipe_nutrition_timestamp,
        i.id as ingredient_id,
        i.ingredient_name,
        i.nutrition_last_updated_at as ingredient_nutrition_timestamp
      FROM mo_recipe r
      JOIN mo_recipe_ingredients ri ON r.id = ri.recipe_id
      JOIN mo_ingredients i ON ri.ingredient_id = i.id
      WHERE r.id = :recipeId 
        AND r.organization_id = :organizationId
        AND ri.recipe_ingredient_status = 'active'
        AND i.ingredient_status = 'active'
    `;
        const results = yield models_1.sequelize.query(query, {
            replacements: { recipeId, organizationId },
            type: models_1.sequelize.QueryTypes.SELECT,
        });
        if (results.length === 0) {
            return { isOutdated: false, outdatedIngredients: [] };
        }
        const recipeNutritionTimestamp = results[0].recipe_nutrition_timestamp;
        const outdatedIngredients = [];
        let isOutdated = false;
        for (const result of results) {
            const ingredientNutritionTimestamp = result.ingredient_nutrition_timestamp;
            // If ingredient nutrition was updated after recipe nutrition calculation
            if (ingredientNutritionTimestamp && recipeNutritionTimestamp &&
                new Date(ingredientNutritionTimestamp) > new Date(recipeNutritionTimestamp)) {
                isOutdated = true;
                outdatedIngredients.push({
                    id: result.ingredient_id,
                    name: result.ingredient_name,
                    lastUpdated: new Date(ingredientNutritionTimestamp),
                });
            }
        }
        return { isOutdated, outdatedIngredients };
    }
    catch (error) {
        console.error(`Error checking recipe nutrition freshness for recipe ID ${recipeId}:`, error);
        throw error;
    }
});
exports.areRecipeNutritionValuesOutdated = areRecipeNutritionValuesOutdated;
/**
 * Get all recipes that use a specific ingredient (for bulk timestamp updates)
 */
const getRecipesUsingIngredient = (ingredientId, organizationId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const query = `
      SELECT DISTINCT r.id
      FROM mo_recipe r
      JOIN mo_recipe_ingredients ri ON r.id = ri.recipe_id
      WHERE ri.ingredient_id = :ingredientId
        AND r.organization_id = :organizationId
        AND ri.recipe_ingredient_status = 'active'
    `;
        const results = yield models_1.sequelize.query(query, {
            replacements: { ingredientId, organizationId },
            type: models_1.sequelize.QueryTypes.SELECT,
        });
        return results.map((result) => result.id);
    }
    catch (error) {
        console.error(`Error getting recipes using ingredient ID ${ingredientId}:`, error);
        throw error;
    }
});
exports.getRecipesUsingIngredient = getRecipesUsingIngredient;
