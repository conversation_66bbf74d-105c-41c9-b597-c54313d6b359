"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RABBITMQ_QUEUE = exports.NORMAL_USER = exports.ADMIN_SIDE_USER = exports.ROLE_CONSTANT = void 0;
exports.ROLE_CONSTANT = Object.freeze({
    SUPER_ADMIN: "Super Admin",
    ADMIN: "Admin",
    DIRECTOR: "Director",
    HR: "HR",
    AREA_MANAGER: "Area Manager",
    ACCOUNTANT: "Accountant",
    BRANCH_MANAGER: "Branch Manager",
    ASSIGN_BRANCH_MANAGER: "Assist. Branch Manager",
    HEAD_CHEF: "Head Chef",
    BAR_MANAGER: "Bar Manager",
    FOH: "FOH",
    BAR: "Bar",
    <PERSON><PERSON><PERSON><PERSON>: "Kitchen",
    HOTEL_MANAGER: "Hotel Manager",
    ASSIGN_HOTEL_MANAGER: "Assist. Hotel Manager",
    RECEPTIONIST: "Receptionist",
    HEAD_HOUSEKEEPER: "Head Housekeeper",
    HOUSE_KEEPER: "House Keeper",
    SIGNATURE: "Signature",
});
exports.ADMIN_SIDE_USER = [
    exports.ROLE_CONSTANT.SUPER_ADMIN,
    exports.ROLE_CONSTANT.ADMIN,
    exports.ROLE_CONSTANT.DIRECTOR,
    exports.ROLE_CONSTANT.ACCOUNTANT,
    exports.ROLE_CONSTANT.HR,
    exports.ROLE_CONSTANT.AREA_MANAGER,
    exports.ROLE_CONSTANT.BRANCH_MANAGER,
    exports.ROLE_CONSTANT.HOTEL_MANAGER,
    exports.ROLE_CONSTANT.SIGNATURE,
];
exports.NORMAL_USER = [
    exports.ROLE_CONSTANT.SUPER_ADMIN,
    exports.ROLE_CONSTANT.ADMIN,
    exports.ROLE_CONSTANT.BRANCH_MANAGER,
    exports.ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
    exports.ROLE_CONSTANT.HEAD_CHEF,
    exports.ROLE_CONSTANT.BAR_MANAGER,
    exports.ROLE_CONSTANT.FOH,
    exports.ROLE_CONSTANT.BAR,
    exports.ROLE_CONSTANT.KITCHEN,
    exports.ROLE_CONSTANT.HOTEL_MANAGER,
    exports.ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER,
    exports.ROLE_CONSTANT.RECEPTIONIST,
    exports.ROLE_CONSTANT.HEAD_HOUSEKEEPER,
    exports.ROLE_CONSTANT.HOUSE_KEEPER,
];
exports.RABBITMQ_QUEUE = Object.freeze({
    STAFF_CREATION_DETAILS: 'staff_creation_details',
    STAFF_CREATION_SUCCESS: 'staff_creation_success',
    ORG_MASTER_USER: 'org_master_user',
    ORG_MASTER_USER_VERIFICATION_SUCCESS: 'org_master_user_verification_success',
    STAFF_CREATION: 'staff_creation',
    STAFF_PASSWORD_PIN_GENERATE: 'staff_password_pin_generate',
    SESSION_STORE: 'session_store',
    USER_ACTIVITY_LOG: 'user_activity_log',
    BANNER_NOTIFICATION: 'banner_notification',
    EMAIL_NOTIFICATION: 'email_notification',
    PUSH_NOTIFICATION_SUCCESS: 'push_notification_success'
});
