"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getOrgName = exports.getOrganizationLogo = exports.getEnhancedUserLiterals = exports.getUserLiterals = exports.isDefaultAccess = exports.getImageUrl = exports.getMimeTypeFromExtension = exports.ReadingFile = exports.getHash = exports.getUserWeekDays = exports.getDepartmentDetails = exports.getUsers = exports.getBranchDetails = exports.createNotification = exports.getUserSession = exports.getUserAllRoles = exports.getUserFullName = exports.getUser = exports.getRoles = exports.checkUserRole = exports.readHTMLFile = exports.getPagination = exports.getPaginatedItems = exports.getUserIdFromUserTable = exports.RECIPE_FILE_UPLOAD_CONSTANT = void 0;
const common_1 = require("../keycloak/common");
const fs_1 = __importDefault(require("fs"));
const User_1 = require("../models/User");
const models_1 = require("../models");
const crypto_1 = require("crypto");
const path = __importStar(require("path"));
/** read html file */
const readHTMLFile = function (path, cb) {
    /** Read the HTML file at the specified path using UTF-8 encoding */
    fs_1.default.readFile(path, "utf-8", function (err, data) {
        /** If an error occurs during reading, log the error and throw it */
        if (err) {
            console.log(err);
            throw err; // Stop the execution and throw the error
        }
        else {
            /** If no error, pass the file content to the callback function */
            cb(null, data); // call the callback function with the file data
        }
    });
};
exports.readHTMLFile = readHTMLFile;
/** Get UserId from User table */
const getUserIdFromUserTable = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const getUser = yield User_1.User.findOne({
            where: {
                keycloak_userId: userId,
            },
            raw: true,
        });
        if (!getUser) {
            return { status: false, message: "ERROR_USER_NOT_FOUND_IN_KEYCLOAK" };
        }
        return { status: true, data: getUser };
    }
    catch (e) {
        console.log("Exception: ", e);
        return null;
    }
});
exports.getUserIdFromUserTable = getUserIdFromUserTable;
const getPagination = (page, size) => {
    const limit = size;
    const Page = page || 1;
    const offset = (Page - 1) * limit;
    return { limit, offset };
};
exports.getPagination = getPagination;
const getPaginatedItems = (pageSize, pageNumber, total) => {
    return {
        pageNumber: pageNumber,
        per_page: pageSize,
        total: total,
        total_pages: Math.ceil(total / pageSize),
    };
};
exports.getPaginatedItems = getPaginatedItems;
/** Check user role status and return response accordingly. */
const checkUserRole = (userId, token) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const getRoles = yield (0, common_1.getUserRoles)(userId, token);
        const masterRole = getRoles.data.realmMappings.find((role) => role.name === global.config.KEYCLOAK_SUPER_ADMIN_ROLE &&
            role.description ===
                global.config.KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION);
        if (!masterRole) {
            return false;
        }
        return true;
    }
    catch (e) {
        console.log("user role status Exception: ", e);
        return { status: false, message: e };
    }
});
exports.checkUserRole = checkUserRole;
const getUser = (id_1, ...args_1) => __awaiter(void 0, [id_1, ...args_1], void 0, function* (id, isAuth = false) {
    const findUser = yield models_1.sequelize.query(`SELECT id, user_first_name, user_middle_name, user_last_name, user_email, branch_id, department_id, IF((user_avatar IS NOT NULL AND user_avatar != ''), CONCAT('${global.config.API_UPLOAD_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar)), '') AS user_avatar_link, user_avatar, user_status, user_active_role_id, web_user_active_role_id, webAppToken, appToken, concat(user_first_name, " ", user_last_name) as user_full_name, rota_group_by, list_order, organization_id , keycloak_auth_id FROM nv_users WHERE ${isAuth ? `keycloak_auth_id='${id}'` : `id = ${id}`} AND user_status NOT IN ('cancelled', 'deleted')`, {
        type: models_1.QueryTypes.SELECT,
        raw: true,
    });
    return findUser && findUser.length > 0 ? findUser[0] : null;
});
exports.getUser = getUser;
/**
 * Get user full name by user ID
 * @param user_id - User ID
 * @returns Promise<string> - User full name or fallback
 */
const getUserFullName = (user_id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!user_id)
            return "Unknown User";
        const findUser = yield models_1.sequelize.query(`SELECT user_first_name, user_middle_name, user_last_name, user_email
       FROM nv_users
       WHERE id = ${user_id}
       AND user_status NOT IN ('cancelled', 'deleted')`, {
            type: models_1.QueryTypes.SELECT,
            raw: true,
        });
        if (!findUser || findUser.length === 0)
            return "Unknown User";
        const user = findUser[0];
        const employeeName = [];
        if (user === null || user === void 0 ? void 0 : user.user_first_name) {
            employeeName.push(user.user_first_name);
        }
        if (user === null || user === void 0 ? void 0 : user.user_middle_name) {
            employeeName.push(user.user_middle_name);
        }
        if (user === null || user === void 0 ? void 0 : user.user_last_name) {
            employeeName.push(user.user_last_name);
        }
        // If no name parts found, use email as fallback
        if (employeeName.length === 0 && (user === null || user === void 0 ? void 0 : user.user_email)) {
            return user.user_email.split("@")[0]; // Use part before @ as display name
        }
        return employeeName.length > 0 ? employeeName.join(" ") : "Unknown User";
    }
    catch (error) {
        console.log("Error getting user full name:", error);
        return "Unknown User";
    }
});
exports.getUserFullName = getUserFullName;
const getUsers = (ids) => __awaiter(void 0, void 0, void 0, function* () {
    const findUsers = yield models_1.sequelize.query(`SELECT id, user_first_name, user_middle_name, user_last_name, user_email, branch_id, department_id, IF((user_avatar IS NOT NULL AND user_avatar != ''), CONCAT('${global.config.API_UPLOAD_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar)), '') AS user_avatar_link, user_avatar, user_status, user_active_role_id, web_user_active_role_id, webAppToken, appToken, concat(user_first_name, " ", user_last_name) as user_full_name, rota_group_by, list_order, organization_id FROM nv_users WHERE id IN (${ids.join(",")})`, {
        type: models_1.QueryTypes.SELECT,
        raw: true,
    });
    return findUsers;
});
exports.getUsers = getUsers;
const getRoles = (id) => __awaiter(void 0, void 0, void 0, function* () {
    const findRoles = yield models_1.sequelize.query(`SELECT * FROM nv_roles WHERE id IN (${id === null || id === void 0 ? void 0 : id.join(",")})`, {
        type: models_1.QueryTypes.SELECT,
        raw: true,
    });
    return findRoles;
});
exports.getRoles = getRoles;
const getBranchDetails = (id) => __awaiter(void 0, void 0, void 0, function* () {
    const findBranches = yield models_1.sequelize.query(`SELECT * FROM nv_branches WHERE id IN (${id === null || id === void 0 ? void 0 : id.join(",")})`, {
        type: models_1.QueryTypes.SELECT,
        raw: true,
    });
    return findBranches;
});
exports.getBranchDetails = getBranchDetails;
const getDepartmentDetails = (id) => __awaiter(void 0, void 0, void 0, function* () {
    const findBranches = yield models_1.sequelize.query(`SELECT * FROM nv_departments WHERE id IN (${id === null || id === void 0 ? void 0 : id.join(",")})`, {
        type: models_1.QueryTypes.SELECT,
        raw: true,
    });
    return findBranches;
});
exports.getDepartmentDetails = getDepartmentDetails;
const getUserAllRoles = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    const userRoles = yield models_1.sequelize.query(`SELECT r.id, r.role_name FROM nv_user_roles ur INNER JOIN nv_roles r ON ur.role_id = r.id WHERE ur.user_id = ${userId}`, {
        type: models_1.QueryTypes.SELECT,
        raw: true,
    });
    return userRoles;
});
exports.getUserAllRoles = getUserAllRoles;
const getUserSession = (token, deviceType) => __awaiter(void 0, void 0, void 0, function* () {
    // const userSession = await UserSession.findOne({ where: { token, device_type: deviceType } });
    const userSession = yield models_1.sequelize.query(`SELECT * FROM nv_user_session WHERE token = '${token}' AND device_type = '${deviceType}'`, {
        type: models_1.QueryTypes.SELECT,
        raw: true,
    });
    return userSession && userSession.length > 0 ? userSession[0] : null;
});
exports.getUserSession = getUserSession;
/**
 * Common Sequelize literal for fetching user information (ID and full name)
 * @param userIdField - The field name containing the user ID (e.g., 'created_by', 'updated_by')
 * @param aliasPrefix - Prefix for the alias (e.g., 'creator', 'updater')
 * @returns Object with user_id and user_full_name literals
 */
const getUserLiterals = (userIdField, aliasPrefix) => {
    return {
        [`${aliasPrefix}_user_id`]: models_1.sequelize.literal(`${userIdField}`),
        [`${aliasPrefix}_user_full_name`]: models_1.sequelize.literal(`(SELECT CONCAT(COALESCE(user_first_name, ''), ' ', COALESCE(user_last_name, ''))
        FROM nv_users
        WHERE id = ${userIdField}
        LIMIT 1)`),
    };
};
exports.getUserLiterals = getUserLiterals;
/**
 * Enhanced user literals with additional user information
 * @param userIdField - The field name containing the user ID
 * @param aliasPrefix - Prefix for the alias
 * @returns Object with comprehensive user information literals
 */
const getEnhancedUserLiterals = (userIdField, aliasPrefix) => {
    return {
        [`${aliasPrefix}_user_id`]: models_1.sequelize.literal(`${userIdField}`),
        [`${aliasPrefix}_user_full_name`]: models_1.sequelize.literal(`(SELECT CONCAT(COALESCE(user_first_name, ''), ' ', COALESCE(user_last_name, ''))
        FROM nv_users
        WHERE id = ${userIdField}
        LIMIT 1)`),
        [`${aliasPrefix}_user_email`]: models_1.sequelize.literal(`(SELECT user_email FROM nv_users WHERE id = ${userIdField} LIMIT 1)`),
        [`${aliasPrefix}_user_avatar_link`]: models_1.sequelize.literal(`(SELECT
         CASE
           WHEN user_avatar IS NOT NULL AND user_avatar != ''
           THEN CONCAT('${global.config.API_UPLOAD_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar))
           ELSE ''
         END
       FROM nv_users
       WHERE id = ${userIdField}
       LIMIT 1)`),
    };
};
exports.getEnhancedUserLiterals = getEnhancedUserLiterals;
const createNotification = (data) => __awaiter(void 0, void 0, void 0, function* () {
    yield models_1.sequelize.query(`INSERT INTO nv_notifications (${Object.keys(data).join(",")}) VALUES (${Object.values(data)
        .map((value) => `'${value}'`)
        .join(",")}) `, {
        type: models_1.QueryTypes.INSERT,
        returning: true,
    });
});
exports.createNotification = createNotification;
const getUserWeekDays = (userId, day) => __awaiter(void 0, void 0, void 0, function* () {
    return yield models_1.sequelize.query(`(SELECT * FROM nv_user_week_day WHERE user_id = ${userId} AND ${day} !='working' AND user_weekday_status='active')`, {
        type: models_1.QueryTypes.SELECT,
        raw: true,
    });
});
exports.getUserWeekDays = getUserWeekDays;
const getHash = function (file, mimeType) {
    try {
        // Create a simple hash using file content, size, and mime type
        const fileContent = fs_1.default.readFileSync(file.path);
        const hash = (0, crypto_1.createHash)("sha256");
        // Add file content, size, and mime type to hash
        hash.update(fileContent);
        hash.update(file.size.toString());
        if (mimeType) {
            hash.update(mimeType);
        }
        else {
            // Simple mime type detection based on file extension
            const ext = path
                .extname(file.originalname || file.filename || "")
                .toLowerCase();
            const detectedMimeType = getMimeTypeFromExtension(ext);
            hash.update(detectedMimeType);
            mimeType = detectedMimeType;
        }
        const finalHash = hash.digest("hex");
        return {
            status: true,
            hash: finalHash,
            actualMimeType: mimeType,
        };
    }
    catch (error) {
        return {
            status: false,
            message: error,
        };
    }
};
exports.getHash = getHash;
const getMimeTypeFromExtension = (ext) => {
    const mimeTypes = {
        ".jpg": "image/jpeg",
        ".jpeg": "image/jpeg",
        ".png": "image/png",
        ".gif": "image/gif",
        ".webp": "image/webp",
        ".svg": "image/svg+xml",
        ".mp4": "video/mp4",
        ".avi": "video/avi",
        ".mov": "video/quicktime",
        ".wmv": "video/x-ms-wmv",
        ".mp3": "audio/mpeg",
        ".wav": "audio/wav",
        ".pdf": "application/pdf",
        ".doc": "application/msword",
        ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ".txt": "text/plain",
        ".json": "application/json",
        ".xml": "application/xml",
    };
    return mimeTypes[ext] || "application/octet-stream";
};
exports.getMimeTypeFromExtension = getMimeTypeFromExtension;
const ReadingFile = (path) => {
    return new Promise(function (resolve, reject) {
        let FileObject;
        const readStream = fs_1.default.createReadStream(path, "utf-8");
        readStream.on("error", (error) => {
            console.log(error);
            FileObject = { status: false, data: error };
            reject(FileObject);
        });
        const chunk = [];
        readStream.on("data", (data) => chunk.push(data));
        readStream.on("end", () => {
            console.log("Reading complete");
            FileObject = { status: true, data: chunk };
            resolve(FileObject);
        });
    });
};
exports.ReadingFile = ReadingFile;
// Recipe File Upload Constants
exports.RECIPE_FILE_UPLOAD_CONSTANT = Object.freeze({
    RECIPE_IMAGE: {
        folder: "recipe_images",
        destinationPath: (orgName, recipeId, fileName) => orgName
            ? `${orgName}/recipe_images/${recipeId ? `${recipeId}/` : ""}${fileName}`
            : `recipe_defaults/recipe_images/${recipeId ? `${recipeId}/` : ""}${fileName}`,
    },
    RECIPE_VIDEO: {
        folder: "recipe_videos",
        destinationPath: (orgName, recipeId, fileName) => orgName
            ? `${orgName}/recipe_videos/${recipeId ? `${recipeId}/` : ""}${fileName}`
            : `recipe_defaults/recipe_videos/${recipeId ? `${recipeId}/` : ""}${fileName}`,
    },
    RECIPE_DOCUMENT: {
        folder: "recipe_documents",
        destinationPath: (orgName, recipeId, fileName) => orgName
            ? `${orgName}/recipe_documents/${recipeId ? `${recipeId}/` : ""}${fileName}`
            : `recipe_defaults/recipe_documents/${recipeId ? `${recipeId}/` : ""}${fileName}`,
    },
    RECIPE_AUDIO: {
        folder: "recipe_audio",
        destinationPath: (orgName, recipeId, fileName) => orgName
            ? `${orgName}/recipe_audio/${recipeId ? `${recipeId}/` : ""}${fileName}`
            : `recipe_defaults/recipe_audio/${recipeId ? `${recipeId}/` : ""}${fileName}`,
    },
    INGREDIENT_IMAGE: {
        folder: "recipe_ingredient_images",
        destinationPath: (orgName, ingredientId, fileName) => orgName
            ? `${orgName}/recipe_ingredients/${ingredientId ? `${ingredientId}/` : ""}${fileName}`
            : `recipe_defaults/recipe_ingredients/${ingredientId ? `${ingredientId}/` : ""}${fileName}`,
    },
    CATEGORY_ICON: {
        folder: "recipe_category_icons",
        destinationPath: (orgName, categoryId, fileName) => orgName
            ? `${orgName}/recipe_categories/${fileName}`
            : `recipe_defaults/recipe_categories/${fileName}`,
    },
    INGREDIENT_CATEGORY_ICON: {
        folder: "ingredient_category_icons",
        destinationPath: (orgName, categoryId, fileName) => orgName
            ? `${orgName}/ingredient_categories/${fileName}`
            : `recipe_defaults/ingredient_categories/${fileName}`,
    },
    ATTRIBUTE_ICON: {
        folder: "recipe_attribute_icons",
        destinationPath: (orgName, attributeId, fileName) => orgName
            ? `${orgName}/recipe_food_attributes/${fileName}`
            : `recipe_defaults/recipe_food_attributes/${fileName}`,
    },
    UNIT_ICON: {
        folder: "recipe_unit_icons",
        destinationPath: (orgName, _unitId, fileName) => orgName
            ? `${orgName}/recipe_measures/${fileName}`
            : `recipe_defaults/recipe_measures/${fileName}`,
    },
    RECIPE_THUMBNAIL: {
        folder: "recipe_thumbnails",
        destinationPath: (orgName, recipeId, fileName) => orgName
            ? `${orgName}/recipe_thumbnails/${recipeId ? `${recipeId}/` : ""}${fileName}`
            : `recipe_defaults/recipe_thumbnails/${recipeId ? `${recipeId}/` : ""}${fileName}`,
    },
    RECIPE_INSTRUCTION_MEDIA: {
        folder: "recipe_instruction_media",
        destinationPath: (orgName, recipeId, stepNumber, fileName) => orgName
            ? `${orgName}/recipe_instruction_media/${recipeId ? `${recipeId}/` : ""}step_${stepNumber}/${fileName}`
            : `recipe_defaults/recipe_instruction_media/${recipeId ? `${recipeId}/` : ""}step_${stepNumber}/${fileName}`,
    },
    RECIPE_NUTRITION_LABELS: {
        folder: "recipe_nutrition_labels",
        destinationPath: (orgName, recipeId, fileName) => orgName
            ? `${orgName}/recipe_nutrition_labels/${recipeId ? `${recipeId}/` : ""}${fileName}`
            : `recipe_defaults/recipe_nutrition_labels/${recipeId ? `${recipeId}/` : ""}${fileName}`,
    },
    SAMPLE_FILES: {
        folder: "sample_files",
        destinationPath: (orgName, fileType, fileName) => orgName
            ? `${orgName}/sample_files/${fileType}/${fileName}`
            : `recipe_defaults/sample_files/${fileType}/${fileName}`,
    },
});
/**
 * Generate image URL from item_location
 * @param itemLocation - The S3 path stored in database
 * @returns Full URL to access the image via file serving endpoint
 */
const getImageUrl = (itemLocation) => {
    var _a;
    if (!itemLocation)
        return null;
    const baseUrl = (_a = global.config) === null || _a === void 0 ? void 0 : _a.API_BASE_URL;
    if (baseUrl &&
        baseUrl.includes("/backend-api/v1/public/user/get-file?location=")) {
        // API_BASE_URL already contains the full endpoint, just append the file path
        // Don't include bucket name as the endpoint handles it automatically
        return `${baseUrl}${itemLocation}`;
    }
    else {
        // For development or when API_BASE_URL is just the base domain
        const fallbackUrl = "https://staging.namastevillage.theeasyaccess.com";
        // Don't include bucket name as the endpoint handles it automatically
        return `${fallbackUrl}/backend-api/v1/public/user/get-file?location=${itemLocation}`;
    }
};
exports.getImageUrl = getImageUrl;
/**
 * Check if user has default access role
 * @param userId - User ID to check
 * @returns boolean - true if user has default access, false otherwise
 */
const isDefaultAccess = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Input validation
        if (!userId) {
            console.log("isDefaultAccess: userId is required");
            return false;
        }
        // Get user from database
        const user = yield getUser(userId, false);
        // If user not found, return false
        if (!user || !user.keycloak_auth_id) {
            console.log(`isDefaultAccess: User not found for ID: ${userId}`);
            return false;
        }
        // Get user roles from Keycloak using parameterized query to prevent SQL injection
        const userRoles = yield models_1.sequelize.query(`SELECT ROLE_ID FROM USER_ROLE_MAPPING WHERE USER_ID = :userId`, {
            type: models_1.QueryTypes.SELECT,
            raw: true,
            replacements: { userId: user.keycloak_auth_id },
        });
        // If no roles found, return false
        if (!userRoles || userRoles.length === 0) {
            console.log(`isDefaultAccess: No roles found for user: ${user.keycloak_auth_id}`);
            return false;
        }
        // Extract role IDs
        const roleIds = userRoles.map((role) => role.ROLE_ID);
        // Get role details using parameterized query
        const roles = yield models_1.sequelize.query(`SELECT NAME, DESCRIPTION FROM KEYCLOAK_ROLE WHERE ID IN (:roleIds)`, {
            type: models_1.QueryTypes.SELECT,
            raw: true,
            replacements: { roleIds },
        });
        // Check if user has default creation role
        const hasDefaultRole = roles.some((role) => role.NAME === global.config.KEYCLOAK_SUPER_ADMIN_ROLE &&
            role.DESCRIPTION ===
                global.config.KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION);
        return hasDefaultRole;
    }
    catch (error) {
        // Log error in development mode only
        if (process.env.NODE_ENV === "development") {
            console.error("isDefaultAccess Exception:", error);
        }
        return false; // Return false instead of null for consistency
    }
});
exports.isDefaultAccess = isDefaultAccess;
/** Get organization logo */
const getOrganizationLogo = (organization_id) => __awaiter(void 0, void 0, void 0, function* () {
    // Get item_location directly in a single query using subquery
    const result = yield models_1.sequelize.query(`
    SELECT i.item_location
    FROM nv_settings s
    LEFT JOIN nv_items i ON i.id = s.value
    WHERE s.setting_status = 'active'
    AND s.organization_id = '${organization_id}'
    AND s.key = 'brand_logo'
    LIMIT 1
  `, { type: models_1.QueryTypes.SELECT, raw: true });
    // Check if we got a result with item_location
    if (result && result.length > 0 && result[0].item_location) {
        return `${global.config.API_BASE_URL}${result[0].item_location}`;
    }
    return "";
});
exports.getOrganizationLogo = getOrganizationLogo;
const getOrgName = (orgId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const selectQuery = `SELECT NAME FROM ORG WHERE ID = '${orgId}'`;
        const orgName = yield models_1.sequelize.query(selectQuery, { type: models_1.sequelize.QueryTypes.SELECT });
        return orgName[0].NAME;
    }
    catch (error) {
        console.error("Error in getOrgName:", error);
        return false;
    }
});
exports.getOrgName = getOrgName;
