"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecipeMeasure = exports.MeasureStatus = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var MeasureStatus;
(function (MeasureStatus) {
    MeasureStatus["active"] = "active";
    MeasureStatus["inactive"] = "inactive";
})(MeasureStatus || (exports.MeasureStatus = MeasureStatus = {}));
class RecipeMeasure extends sequelize_1.Model {
}
exports.RecipeMeasure = RecipeMeasure;
RecipeMeasure.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    unit_title: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
    },
    unit_slug: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
    },
    unit_icon: {
        type: sequelize_1.DataTypes.INTEGER, // Store item_id reference
        allowNull: true,
        comment: "Foreign key reference to nv_items table for unit icon",
        references: {
            model: "nv_items",
            key: "id",
        },
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
    },
    status: {
        type: sequelize_1.DataTypes.ENUM(Object.values(MeasureStatus)),
        allowNull: false,
        defaultValue: MeasureStatus.active,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    is_system_unit: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Whether this is a system default unit (created by Super Admin)",
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_recipe_measure",
    modelName: "RecipeMeasure",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
});
// Define associations
RecipeMeasure.associate = (models) => {
    // RecipeMeasure belongs to User (created_by)
    RecipeMeasure.belongsTo(models.User, {
        foreignKey: "created_by",
        as: "creator",
    });
    // RecipeMeasure belongs to User (updated_by)
    RecipeMeasure.belongsTo(models.User, {
        foreignKey: "updated_by",
        as: "updater",
    });
    RecipeMeasure.belongsTo(models.Item, {
        foreignKey: "unit_icon",
        as: "iconItem",
        constraints: true, //
        onDelete: "SET NULL", //
        onUpdate: "CASCADE", //
    });
    // Note: Only nutrition-related unit_of_measure fields in RecipeAttributes and IngredientAttributes are text fields
    // Other measure fields like ingredient_measure in RecipeIngredients still reference this table
    // RecipeMeasure has many RecipeIngredients (ingredient_measure)
    RecipeMeasure.hasMany(models.RecipeIngredients, {
        foreignKey: "ingredient_measure",
        as: "recipeIngredients",
    });
};
exports.default = RecipeMeasure;
