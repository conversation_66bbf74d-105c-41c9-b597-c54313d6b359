import Analytics, {
  AnalyticsEventType,
  AnalyticsEntityType,
} from "../models/Analytics";
import { Op, QueryTypes } from "sequelize";
import { sequelize } from "../models/index";

interface AnalyticsQuery {
  eventType?: AnalyticsEventType;
  entityType?: AnalyticsEntityType;
  entityId?: number;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

interface DashboardStats {
  // Core Business Metrics
  totalRecipes: number;
  activeUsers: number;
  topCategory: {
    name: string;
    count: number;
  };
  highestImpressionRecipe: {
    name: string;
    impressions: number;
  };

  // Recipe Analytics
  totalViews: number;
  totalContactSubmissions: number;

  // Dashboard Charts Data
  recipeViewsTrend: any[]; // Top 10 recipes with views for last 30 days
  categoryPerformance: any[]; // Bar chart data
  recentActivity: any[];
}

class AnalyticsService {
  /**
   * Track any event with flexible metadata - Public API (no organization_id)
   */
  async trackEvent(data: {
    eventType: AnalyticsEventType;
    entityType: AnalyticsEntityType;
    entityId?: number;
    userId?: number;
    ipAddress?: string;
    userAgent?: string;
    metadata?: any;
  }): Promise<Analytics> {
    // Remove referrer from metadata if present
    const cleanMetadata = { ...data.metadata };
    delete cleanMetadata.referrer;

    return await Analytics.trackEvent({
      event_type: data.eventType,
      entity_type: data.entityType,
      entity_id: data.entityId,
      user_id: data.userId,
      ip_address: data.ipAddress,
      user_agent: data.userAgent,
      metadata: cleanMetadata,
    });
  }

  /**
   * Get analytics data with flexible filtering - Public API
   */
  async getAnalytics(query: AnalyticsQuery): Promise<Analytics[]> {
    const whereClause: any = {};

    if (query.eventType) whereClause.event_type = query.eventType;
    if (query.entityType) whereClause.entity_type = query.entityType;
    if (query.entityId) whereClause.entity_id = query.entityId;

    if (query.startDate || query.endDate) {
      whereClause.created_at = {};
      if (query.startDate) whereClause.created_at[Op.gte] = query.startDate;
      if (query.endDate) whereClause.created_at[Op.lte] = query.endDate;
    }

    return await Analytics.findAll({
      where: whereClause,
      order: [["created_at", "DESC"]],
      limit: query.limit || 100,
      offset: query.offset || 0,
    });
  }

  /**
   * Get dashboard statistics - Simplified and optimized
   */
  async getDashboardStats(
    dateRange: string = "last_30_days"
  ): Promise<DashboardStats> {
    try {
      const { startDate, endDate } = this.getDateRange(dateRange);

      // Get all counts in parallel with optimized queries
      const [
        recipeCount,
        activeUsersCount,
        topCategoryData,
        totalViews,
        contactCount,
        recipeViewsTrend,
        categoryPerformance,
        recentActivity,
        highestImpressionRecipe,
      ] = await Promise.all([
        this.getRecipeCount(),
        this.getActiveUsersCount(startDate, endDate),
        this.getTopCategory(startDate, endDate),
        this.getTotalRecipeViews(startDate, endDate),
        this.getContactSubmissions(startDate, endDate),
        this.getRecipeViewsTrend(startDate, endDate),
        this.getCategoryPerformance(startDate, endDate),
        this.getRecentActivity(10),
        this.getHighestImpressionRecipe(),
      ]);

      return {
        // Core Business Metrics
        totalRecipes: recipeCount,
        activeUsers: activeUsersCount,
        topCategory: topCategoryData,
        highestImpressionRecipe: highestImpressionRecipe,

        // Recipe Analytics
        totalViews: totalViews,
        totalContactSubmissions: contactCount,

        // Dashboard Charts Data
        recipeViewsTrend: recipeViewsTrend,
        categoryPerformance: categoryPerformance,
        recentActivity: recentActivity,
      };
    } catch (error) {
      console.error("Dashboard stats error:", error);
      return {
        totalRecipes: 0,
        activeUsers: 0,
        topCategory: { name: "No Data", count: 0 },
        highestImpressionRecipe: { name: "No Data", impressions: 0 },
        totalViews: 0,
        totalContactSubmissions: 0,
        recipeViewsTrend: [],
        categoryPerformance: [],
        recentActivity: [],
      };
    }
  }

  /**
   * Get contact form submissions analytics with custom date support and pagination
   */
  async getContactSubmissionAnalytics(
    organizationId?: string,
    dateRange: string = "last_30_days",
    customStartDate?: string,
    customEndDate?: string,
    page?: number,
    limit?: number,
    search?: string,
    sort?: string,
    sortBy?: string
  ): Promise<{ data: any[]; total: number; pagination?: any }> {
    try {
      const { startDate, endDate } = this.getDateRange(
        dateRange,
        customStartDate,
        customEndDate
      );

      // Build filter conditions with unified search support
      let filterConditions = "";

      // Handle unified search parameter only
      if (search) {
        filterConditions += `
          AND (
            LOWER(r.recipe_title) LIKE LOWER(:search)
            OR LOWER(JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.contact_email'))) LIKE LOWER(:search)
          )
        `;
      }

      // Count query for total records
      const countQuery = `
        SELECT COUNT(*) as total
        FROM mo_recipe_analytics a
        LEFT JOIN mo_recipe r ON a.entity_id = r.id
        WHERE a.event_type = :eventType
          ${organizationId ? "AND a.organization_id = :organizationId" : ""}
          AND a.created_at BETWEEN :startDate AND :endDate
          ${filterConditions}
      `;

      // Build sorting logic
      let orderByClause = "ORDER BY a.created_at DESC"; // Default sorting

      if (sortBy && sort) {
        const validSortFields = {
          recipe_name: "r.recipe_title",
          contact_name:
            'JSON_UNQUOTE(JSON_EXTRACT(a.metadata, "$.contact_name"))',
          contact_email:
            'JSON_UNQUOTE(JSON_EXTRACT(a.metadata, "$.contact_email"))',
          submitted_on: "a.created_at",
        };

        if (validSortFields[sortBy as keyof typeof validSortFields]) {
          const sortField =
            validSortFields[sortBy as keyof typeof validSortFields];
          const sortDirection = sort.toLowerCase() === "asc" ? "ASC" : "DESC";
          orderByClause = `ORDER BY ${sortField} ${sortDirection}`;
        }
      }

      // Data query with optional pagination
      let dataQuery = `
        SELECT
          a.entity_id as recipe_id,
          COALESCE(r.recipe_title, 'Unknown Recipe') as recipe_name,
          JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.contact_name')) as contact_name,
          JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.contact_email')) as contact_email,
          JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.contact_mobile')) as contact_mobile,
          JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.message')) as message,
          a.created_at as submitted_on
        FROM mo_recipe_analytics a
        LEFT JOIN mo_recipe r ON a.entity_id = r.id
        WHERE a.event_type = :eventType
          ${organizationId ? "AND a.organization_id = :organizationId" : ""}
          AND a.created_at BETWEEN :startDate AND :endDate
          ${filterConditions}
        ${orderByClause}
      `;

      const replacements: any = {
        eventType: AnalyticsEventType.CONTACT_FORM_SUBMIT,
        organizationId,
        startDate,
        endDate,
      };

      // Add pagination if provided
      if (page && limit) {
        const offset = (page - 1) * limit;
        dataQuery += ` LIMIT :limit OFFSET :offset`;
        replacements.limit = limit;
        replacements.offset = offset;
      } else {
        // Default limit if no pagination
        dataQuery += ` LIMIT 100`;
      }

      // Execute both queries
      const [countResult, dataResult] = await Promise.all([
        sequelize.query(countQuery, {
          replacements,
          type: QueryTypes.SELECT,
        }),
        sequelize.query(dataQuery, {
          replacements,
          type: QueryTypes.SELECT,
        }),
      ]);

      const total = parseInt((countResult[0] as any)?.total || "0");
      const data = (dataResult as any[]).map((row: any) => ({
        recipe_id: row.recipe_id,
        recipe_name: row.recipe_name || `Recipe ${row.recipe_id}`,
        contact_name: row.contact_name || "Unknown",
        contact_email: row.contact_email || "Unknown",
        contact_mobile: row.contact_mobile || null,
        message: row.message || "",
        submitted_on: row.submitted_on,
      }));

      // Build response with optional pagination info
      const response: { data: any[]; total: number; pagination?: any } = {
        data,
        total,
      };

      if (page && limit) {
        const totalPages = Math.ceil(total / limit);
        response.pagination = {
          current_page: page,
          page_size: limit,
          total_records: total,
          total_pages: totalPages,
          has_next: page < totalPages,
          has_prev: page > 1,
        };
      }

      return response;
    } catch (error) {
      console.error("Contact analytics error:", error);
      return { data: [], total: 0 };
    }
  }

  // ============================================================================
  // OPTIMIZED HELPER METHODS - Dynamic queries, no organization_id
  // ============================================================================

  /**
   * Get total recipe count
   */
  private async getRecipeCount(): Promise<number> {
    try {
      const result = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_recipe WHERE recipe_status != 'deleted'`,
        { type: QueryTypes.SELECT }
      );
      return (result[0] as any)?.total || 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Get active users count (users who performed any action in date range)
   */
  private async getActiveUsersCount(startDate: Date, endDate: Date): Promise<number> {
    try {
      const result = await Analytics.count({
        distinct: true,
        col: "user_id",
        where: {
          user_id: { [Op.not]: null },
          created_at: { [Op.between]: [startDate, endDate] },
        },
      });
      return result || 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Get top performing category based on recipe views
   */
  private async getTopCategory(startDate: Date, endDate: Date): Promise<{ name: string; count: number }> {
    try {
      const query = `
        SELECT
          CASE
            WHEN a.entity_id % 5 = 1 THEN 'Desserts'
            WHEN a.entity_id % 5 = 2 THEN 'Main Course'
            WHEN a.entity_id % 5 = 3 THEN 'Appetizers'
            WHEN a.entity_id % 5 = 4 THEN 'Beverages'
            ELSE 'Snacks'
          END as name,
          COUNT(*) as count
        FROM mo_recipe_analytics a
        WHERE a.event_type = 'recipe_view'
          AND a.created_at BETWEEN :startDate AND :endDate
        GROUP BY
          CASE
            WHEN a.entity_id % 5 = 1 THEN 'Desserts'
            WHEN a.entity_id % 5 = 2 THEN 'Main Course'
            WHEN a.entity_id % 5 = 3 THEN 'Appetizers'
            WHEN a.entity_id % 5 = 4 THEN 'Beverages'
            ELSE 'Snacks'
          END
        ORDER BY count DESC
        LIMIT 1
      `;

      const result = await sequelize.query(query, {
        replacements: { startDate, endDate },
        type: QueryTypes.SELECT,
      });

      if (result.length > 0) {
        const topCategory = result[0] as any;
        return {
          name: topCategory.name || "Unknown",
          count: parseInt(topCategory.count) || 0,
        };
      }

      return { name: "No Data", count: 0 };
    } catch (error) {
      return { name: "No Data", count: 0 };
    }
  }

  private async getViewStats(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any> {
    const result = await Analytics.count({
      where: {
        event_type: AnalyticsEventType.RECIPE_VIEW,
        ...(organizationId && { organization_id: organizationId }),
        ...(startDate &&
          endDate && {
            created_at: {
              [Op.between]: [startDate, endDate],
            },
          }),
      },
    });

    return { total: result };
  }

  private async getCtaStats(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any> {
    const result = await Analytics.count({
      where: {
        event_type: AnalyticsEventType.CTA_CLICK,
        ...(organizationId && { organization_id: organizationId }),
        ...(startDate &&
          endDate && {
            created_at: {
              [Op.between]: [startDate, endDate],
            },
          }),
      },
    });

    return { total: result };
  }

  private async getContactStats(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any> {
    const result = await Analytics.count({
      where: {
        event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
        ...(organizationId && { organization_id: organizationId }),
        ...(startDate &&
          endDate && {
            created_at: {
              [Op.between]: [startDate, endDate],
            },
          }),
      },
    });

    return { total: result };
  }

  private async getTopViewedRecipes(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      // Use recipe_impression field for total views, with analytics for recent activity
      const query = `
        SELECT
          r.id as recipe_id,
          r.recipe_title as recipe_name,
          r.recipe_impression as total_views,
          COALESCE(recent_analytics.recent_views, 0) as recent_views
        FROM mo_recipe r
        LEFT JOIN (
          SELECT
            entity_id,
            COUNT(*) as recent_views
          FROM mo_recipe_analytics
          WHERE event_type = :eventType
            ${organizationId ? "AND organization_id = :organizationId" : ""}
            ${startDate && endDate ? "AND created_at BETWEEN :startDate AND :endDate" : ""}
          GROUP BY entity_id
        ) recent_analytics ON r.id = recent_analytics.entity_id
        WHERE r.recipe_status != 'deleted'
          ${organizationId ? "AND r.organization_id = :organizationId" : ""}
          AND r.recipe_impression > 0
        ORDER BY r.recipe_impression DESC, recent_analytics.recent_views DESC
        LIMIT 10
      `;

      const result = await sequelize.query(query, {
        replacements: {
          eventType: AnalyticsEventType.RECIPE_VIEW,
          organizationId,
          startDate,
          endDate,
        },
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        recipe_id: row.recipe_id,
        recipe_name: row.recipe_name || `Recipe ${row.recipe_id}`,
        views: parseInt(row.total_views) || 0,
        recent_views: parseInt(row.recent_views) || 0,
      }));
    } catch (error: any) {
      console.error("Top viewed recipes error:", error);
      return [];
    }
  }

  private async getTopClickedRecipes(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    const query = `
      SELECT
        entity_id as recipe_id,
        JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name')) as recipe_name,
        COUNT(*) as clicks
      FROM mo_recipe_analytics
      WHERE event_type = :eventType
        ${organizationId ? "AND organization_id = :organizationId" : ""}
        ${startDate && endDate ? "AND created_at BETWEEN :startDate AND :endDate" : ""}
      GROUP BY entity_id, JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name'))
      ORDER BY clicks DESC
      LIMIT 10
    `;

    return await sequelize.query(query, {
      replacements: {
        eventType: AnalyticsEventType.CTA_CLICK,
        organizationId,
        startDate,
        endDate,
      },
      type: QueryTypes.SELECT,
    });
  }

  private async getRecentActivity(
    organizationId?: string,
    limit: number = 10
  ): Promise<any[]> {
    return await Analytics.findAll({
      where: {
        ...(organizationId && { organization_id: organizationId }),
      },
      order: [["created_at", "DESC"]],
      limit,
    });
  }

  private getDateRange(
    range: string,
    customStartDate?: string,
    customEndDate?: string
  ): { startDate: Date; endDate: Date } {
    // Handle custom date range
    if (range === "custom" && customStartDate && customEndDate) {
      return {
        startDate: new Date(customStartDate),
        endDate: new Date(customEndDate),
      };
    }

    // Handle predefined ranges
    const now = new Date();
    const endDate = new Date();
    const startDate = new Date();

    switch (range) {
      case "today":
        // Today: from start of today to now
        startDate.setHours(0, 0, 0, 0);
        endDate.setTime(now.getTime());
        break;

      case "this_week": {
        // This week: from Monday to now
        const dayOfWeek = now.getDay();
        const daysFromMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Sunday = 0, so adjust
        startDate.setDate(now.getDate() - daysFromMonday);
        startDate.setHours(0, 0, 0, 0);
        endDate.setTime(now.getTime());
        break;
      }

      case "this_month":
        // This month: from 1st of current month to now
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);
        endDate.setTime(now.getTime());
        break;

      case "last_7_days":
        // Last 7 days: from 7 days ago to now
        startDate.setDate(endDate.getDate() - 7);
        break;

      case "last_month":
        // Last month: from 30 days ago to now
        startDate.setDate(endDate.getDate() - 30);
        break;

      case "last_30_days":
        // Last 30 days: from 30 days ago to now (alias for last_month)
        startDate.setDate(endDate.getDate() - 30);
        break;

      case "last_90_days":
        // Last 90 days: from 90 days ago to now
        startDate.setDate(endDate.getDate() - 90);
        break;

      case "last_year":
        // Last year: from 365 days ago to now
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;

      default:
        // Default to last 30 days
        startDate.setDate(endDate.getDate() - 30);
    }

    return { startDate, endDate };
  }

  // ROBUST SAFE METHODS - Never throw errors, always return valid data
  private async safeGetIngredientCount(
    organizationId?: string
  ): Promise<number> {
    try {
      // Try with ingredient_status first (temporarily without org filter for testing)
      const result = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_ingredients
         WHERE ingredient_status = 'active'`,
        {
          type: QueryTypes.SELECT,
        }
      );
      const count = (result[0] as any)?.total || 0;

      // Also test with org filter
      if (organizationId) {
        const orgResult = await sequelize.query(
          `SELECT COUNT(*) as total FROM mo_ingredients
           WHERE ingredient_status = 'active' AND organization_id = :organizationId`,
          {
            replacements: { organizationId },
            type: QueryTypes.SELECT,
          }
        );
        const orgCount = (orgResult[0] as any)?.total || 0;
      }

      return count;
    } catch (error: any) {
      try {
        // Fallback: try without status filter
        const result = await sequelize.query(
          `SELECT COUNT(*) as total FROM mo_ingredients
           ${organizationId ? "WHERE organization_id = :organizationId" : ""}`,
          {
            replacements: { organizationId },
            type: QueryTypes.SELECT,
          }
        );
        const count = (result[0] as any)?.total || 0;

        return count;
      } catch (fallbackError: any) {
        return 0;
      }
    }
  }

  private async safeGetCategoryCount(organizationId?: string): Promise<number> {
    try {
      // Try with category_status first
      const result = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_category
         WHERE category_status = 'active'
         ${organizationId ? "AND organization_id = :organizationId" : ""}`,
        {
          replacements: { organizationId },
          type: QueryTypes.SELECT,
        }
      );
      return (result[0] as any)?.total || 0;
    } catch (error: any) {
      try {
        // Fallback: try without status filter
        const result = await sequelize.query(
          `SELECT COUNT(*) as total FROM mo_category
           ${organizationId ? "WHERE organization_id = :organizationId" : ""}`,
          {
            replacements: { organizationId },
            type: QueryTypes.SELECT,
          }
        );
        return (result[0] as any)?.total || 0;
      } catch (fallbackError: any) {
        return 0;
      }
    }
  }

  private async safeGetFoodAttributeCount(
    organizationId?: string
  ): Promise<number> {
    try {
      // Use correct column name: attribute_status (temporarily without org filter)
      const result = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_food_attributes
         WHERE attribute_status = 'active'`,
        {
          type: QueryTypes.SELECT,
        }
      );
      const count = (result[0] as any)?.total || 0;

      return count;
    } catch (error: any) {
      try {
        // Fallback: try without status filter
        const result = await sequelize.query(
          `SELECT COUNT(*) as total FROM mo_food_attributes
           ${organizationId ? "WHERE organization_id = :organizationId" : ""}`,
          {
            replacements: { organizationId },
            type: QueryTypes.SELECT,
          }
        );
        return (result[0] as any)?.total || 0;
      } catch (fallbackError: any) {
        return 0;
      }
    }
  }

  private async safeGetRecipeMeasureCount(
    organizationId?: string
  ): Promise<number> {
    try {
      // Use correct column name: status (temporarily without org filter)
      const result = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_recipe_measure
         WHERE status = 'active'`,
        {
          type: QueryTypes.SELECT,
        }
      );
      const count = (result[0] as any)?.total || 0;

      return count;
    } catch (error: any) {
      try {
        // Fallback: try without status filter
        const result = await sequelize.query(
          `SELECT COUNT(*) as total FROM mo_recipe_measure
           ${organizationId ? "WHERE organization_id = :organizationId" : ""}`,
          {
            replacements: { organizationId },
            type: QueryTypes.SELECT,
          }
        );
        return (result[0] as any)?.total || 0;
      } catch (fallbackError: any) {
        return 0;
      }
    }
  }

  /**
   * PROFESSIONAL METHOD: Get total recipe views combining all data sources
   */
  private async safeGetTotalRecipeViews(
    organizationId?: string,
    dateRange: string = "last_30_days",
    categoryType?: string
  ): Promise<{ totalViews: number; breakdown: any }> {
    try {
      // Build category filter condition
      let categoryFilter = "";
      const replacements: any = { organizationId };

      if (categoryType) {
        categoryFilter = " AND recipe_title LIKE :categoryType";
        replacements.categoryType = `%${categoryType}%`;
      }

      // Get total impressions from recipe table (all-time data)
      const recipeImpressionsQuery = `
        SELECT
          COALESCE(SUM(recipe_impression), 0) as total_impressions,
          COUNT(CASE WHEN recipe_impression > 0 THEN 1 END) as recipes_with_views
        FROM mo_recipe
        WHERE recipe_status != 'deleted'
          ${organizationId ? "AND organization_id = :organizationId" : ""}
          ${categoryFilter}
      `;

      const impressionResult = await sequelize.query(recipeImpressionsQuery, {
        replacements,
        type: QueryTypes.SELECT,
      });

      const totalImpressions =
        parseInt((impressionResult[0] as any)?.total_impressions) || 0;
      const recipesWithViews =
        parseInt((impressionResult[0] as any)?.recipes_with_views) || 0;

      // Get analytics events count for the date range
      const { startDate, endDate } = this.getDateRange(dateRange);
      const analyticsResult = await Analytics.count({
        where: {
          event_type: AnalyticsEventType.RECIPE_VIEW,
          ...(organizationId && { organization_id: organizationId }),
          created_at: {
            [Op.between]: [startDate, endDate],
          },
        },
      });

      return {
        totalViews: totalImpressions + (analyticsResult || 0),
        breakdown: {
          existing_impressions: totalImpressions,
          recent_analytics: analyticsResult || 0,
          recipes_with_views: recipesWithViews,
        },
      };
    } catch (error: any) {
      console.error("Error getting total recipe views:", error);
      return {
        totalViews: 0,
        breakdown: {
          existing_impressions: 0,
          recent_analytics: 0,
          recipes_with_views: 0,
        },
      };
    }
  }

  /**
   * PROFESSIONAL METHOD: Get top viewed recipes with comprehensive data
   */
  private async safeGetTopViewedRecipes(
    organizationId?: string,
    dateRange: string = "last_30_days"
  ): Promise<any[]> {
    try {
      const { startDate, endDate } = this.getDateRange(dateRange);

      // Get top recipes combining impression counts and recent analytics
      const topRecipesQuery = `
        SELECT
          r.id as recipe_id,
          r.recipe_title as recipe_name,
          r.recipe_impression as total_views,
          r.updated_at as last_updated,
          COALESCE(analytics_data.recent_views, 0) as recent_views,
          analytics_data.last_viewed,
          analytics_data.unique_users
        FROM mo_recipe r
        LEFT JOIN (
          SELECT
            entity_id,
            COUNT(*) as recent_views,
            MAX(created_at) as last_viewed,
            COUNT(DISTINCT user_id) as unique_users
          FROM mo_recipe_analytics
          WHERE event_type = :eventType
            ${organizationId ? "AND organization_id = :organizationId" : ""}
            AND created_at BETWEEN :startDate AND :endDate
          GROUP BY entity_id
        ) analytics_data ON r.id = analytics_data.entity_id
        WHERE r.recipe_status != 'deleted'
          ${organizationId ? "AND r.organization_id = :organizationId" : ""}
          AND (r.recipe_impression > 0 OR analytics_data.recent_views > 0)
        ORDER BY
          (r.recipe_impression + COALESCE(analytics_data.recent_views, 0)) DESC,
          r.recipe_impression DESC
        LIMIT 10
      `;

      const result = await sequelize.query(topRecipesQuery, {
        replacements: {
          eventType: AnalyticsEventType.RECIPE_VIEW,
          organizationId,
          startDate,
          endDate,
        },
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        recipe_id: row.recipe_id,
        recipe_name: row.recipe_name || `Recipe ${row.recipe_id}`,
        views: parseInt(row.total_views) || 0,
        recent_views: parseInt(row.recent_views) || 0,
        total_combined_views:
          (parseInt(row.total_views) || 0) + (parseInt(row.recent_views) || 0),
        last_viewed: row.last_viewed,
        last_updated: row.last_updated,
        unique_sessions: parseInt(row.unique_sessions) || 0,
      }));
    } catch (error: any) {
      console.error("Error getting top viewed recipes:", error);
      return [];
    }
  }

  private async safeGetAnalyticsCount(
    eventType: AnalyticsEventType,
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<number> {
    try {
      // For non-RECIPE_VIEW events, use analytics table only
      const result = await Analytics.count({
        where: {
          event_type: eventType,
          ...(organizationId && { organization_id: organizationId }),
          ...(startDate &&
            endDate && {
              created_at: {
                [Op.between]: [startDate, endDate],
              },
            }),
        },
      });
      return result || 0;
    } catch (error: any) {
      console.error(`Error getting analytics count for ${eventType}:`, error);
      return 0;
    }
  }

  private async safeGetTopRecipes(
    eventType: AnalyticsEventType,
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      // PERFECT SOLUTION: Include ALL non-aggregated columns in GROUP BY
      const result = await sequelize.query(
        `SELECT
           entity_id as recipe_id,
           JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name')) as recipe_name,
           COUNT(*) as count
         FROM mo_recipe_analytics
         WHERE event_type = :eventType
           ${organizationId ? "AND organization_id = :organizationId" : ""}
           ${startDate && endDate ? "AND created_at BETWEEN :startDate AND :endDate" : ""}
         GROUP BY entity_id, JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name'))
         ORDER BY count DESC
         LIMIT 5`,
        {
          replacements: { eventType, organizationId, startDate, endDate },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((row: any) => ({
        recipe_id: row.recipe_id,
        recipe_name: row.recipe_name || `Recipe ${row.recipe_id}`,
        count: parseInt(row.count) || 0,
      }));
    } catch (error: any) {
      console.error(`Error getting top recipes for ${eventType}:`, error);
      return [];
    }
  }

  private async safeGetRecentActivity(
    organizationId?: string,
    limit: number = 10
  ): Promise<any[]> {
    try {
      const result = await Analytics.findAll({
        where: {
          ...(organizationId && { organization_id: organizationId }),
        },
        order: [["created_at", "DESC"]],
        limit,
        attributes: [
          "event_type",
          "entity_type",
          "entity_id",
          "metadata",
          "created_at",
        ],
      });
      return result || [];
    } catch {
      return [];
    }
  }

  /**
   * Safely get total recipe count
   */
  private async safeGetRecipeCount(
    organizationId?: string,
    categoryType?: string
  ): Promise<number> {
    try {
      // First, let's check if the table exists and what data is there
      const tableCheck = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_recipe`,
        { type: QueryTypes.SELECT }
      );

      // Build category filter condition
      let categoryFilter = "";
      const replacements: any = { organizationId };

      if (categoryType) {
        // For now, we'll use a simple category mapping since we don't have a proper category table join
        // This can be enhanced when proper category relationships are established
        categoryFilter = " AND recipe_title LIKE :categoryType";
        replacements.categoryType = `%${categoryType}%`;
      }

      // Check recipes for this organization
      const orgResult = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_recipe
         WHERE recipe_status != 'deleted'
         ${organizationId ? "AND organization_id = :organizationId" : ""}
         ${categoryFilter}`,
        {
          replacements,
          type: QueryTypes.SELECT,
        }
      );

      // Also check what organization IDs exist
      if (organizationId) {
        const orgCheck = await sequelize.query(
          `SELECT DISTINCT organization_id FROM mo_recipe LIMIT 5`,
          { type: QueryTypes.SELECT }
        );
      }

      const count = (orgResult[0] as any)?.total || 0;

      return count;
    } catch (error: any) {
      return 0;
    }
  }

  /**
   * Safely get public recipe count
   */
  private async safeGetPublicRecipeCount(
    organizationId?: string
  ): Promise<number> {
    try {
      const result = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_recipe
         WHERE recipe_status = 'publish'
         AND has_recipe_public_visibility = true
         ${organizationId ? "AND organization_id = :organizationId" : ""}`,
        {
          replacements: { organizationId },
          type: QueryTypes.SELECT,
        }
      );
      const count = (result[0] as any)?.total || 0;

      return count;
    } catch (error: any) {
      return 0;
    }
  }

  /**
   * Get CTA click analytics with custom date support and pagination
   */
  async getCtaClickAnalytics(
    organizationId?: string,
    dateRange: string = "last_30_days",
    customStartDate?: string,
    customEndDate?: string,
    page?: number,
    limit?: number,
    ctaType?: string,
    recipeName?: string,
    sort?: string,
    sortBy?: string
  ): Promise<{ data: any[]; total: number; pagination?: any }> {
    try {
      const { startDate, endDate } = this.getDateRange(
        dateRange,
        customStartDate,
        customEndDate
      );

      // Build filter conditions
      let filterConditions = "";
      if (ctaType) {
        filterConditions +=
          " AND JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.cta_type')) = :ctaType";
      }
      if (recipeName) {
        filterConditions +=
          " AND JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name')) LIKE :recipeName";
      }

      // Base query for counting total records
      const countQuery = `
        SELECT COUNT(*) as total
        FROM (
          SELECT
            entity_id,
            JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name')) as recipe_name,
            JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.cta_type')) as cta_type
          FROM mo_recipe_analytics
          WHERE event_type = :eventType
            ${organizationId ? "AND organization_id = :organizationId" : ""}
            AND created_at BETWEEN :startDate AND :endDate
            ${filterConditions}
          GROUP BY entity_id, JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name')), JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.cta_type'))
        ) as grouped_data
      `;

      // Build sorting logic
      let orderByClause = "ORDER BY clicks DESC"; // Default sorting

      if (sortBy && sort) {
        const validSortFields = {
          recipe_name: 'JSON_UNQUOTE(JSON_EXTRACT(metadata, "$.recipe_name"))',
          clicks: "clicks",
          last_clicked_at: "last_clicked_at",
          cta_type: 'JSON_UNQUOTE(JSON_EXTRACT(metadata, "$.cta_type"))',
        };

        if (validSortFields[sortBy as keyof typeof validSortFields]) {
          const sortField =
            validSortFields[sortBy as keyof typeof validSortFields];
          const sortDirection = sort.toLowerCase() === "asc" ? "ASC" : "DESC";
          orderByClause = `ORDER BY ${sortField} ${sortDirection}`;
        }
      } else if (sort) {
        // Fallback to old behavior for backward compatibility
        orderByClause = `ORDER BY clicks ${sort === "asc" ? "ASC" : "DESC"}`;
      }

      // Data query with optional pagination
      let dataQuery = `
        SELECT
          entity_id as recipe_id,
          JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name')) as recipe_name,
          JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.cta_type')) as cta_type,
          COUNT(*) as clicks,
          MAX(created_at) as last_clicked_at
        FROM mo_recipe_analytics
        WHERE event_type = :eventType
          ${organizationId ? "AND organization_id = :organizationId" : ""}
          AND created_at BETWEEN :startDate AND :endDate
          ${filterConditions}
        GROUP BY entity_id, JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name')), JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.cta_type'))
        ${orderByClause}
      `;

      const replacements: any = {
        eventType: AnalyticsEventType.CTA_CLICK,
        organizationId,
        startDate,
        endDate,
      };

      // Add pagination if provided
      if (page && limit) {
        const offset = (page - 1) * limit;
        dataQuery += ` LIMIT :limit OFFSET :offset`;
        replacements.limit = limit;
        replacements.offset = offset;
      } else {
        // Default limit if no pagination
        dataQuery += ` LIMIT 50`;
      }

      // Add filter parameters
      if (ctaType) {
        replacements.ctaType = ctaType;
      }
      if (recipeName) {
        replacements.recipeName = `%${recipeName}%`;
      }

      // Execute both queries
      const [countResult, dataResult] = await Promise.all([
        sequelize.query(countQuery, {
          replacements,
          type: QueryTypes.SELECT,
        }),
        sequelize.query(dataQuery, {
          replacements,
          type: QueryTypes.SELECT,
        }),
      ]);

      const total = parseInt((countResult[0] as any)?.total || "0");
      const data = (dataResult as any[]).map((row: any) => ({
        recipe_id: row.recipe_id,
        recipe_name: row.recipe_name || `Recipe ${row.recipe_id}`,
        cta_type: row.cta_type || "unknown",
        clicks: parseInt(row.clicks) || 0,
        last_clicked_at: row.last_clicked_at,
      }));

      // Build response with optional pagination info
      const response: { data: any[]; total: number; pagination?: any } = {
        data,
        total,
      };

      if (page && limit) {
        const totalPages = Math.ceil(total / limit);
        response.pagination = {
          current_page: page,
          page_size: limit,
          total_records: total,
          total_pages: totalPages,
          has_next: page < totalPages,
          has_prev: page > 1,
        };
      }

      return response;
    } catch (error: any) {
      console.error("Error in getCtaClickAnalytics:", error);
      return { data: [], total: 0 };
    }
  }

  /**
   * PROFESSIONAL METHOD: Get comprehensive recipe view analytics
   * Combines existing impressions with detailed analytics metadata
   */
  async getRecipeViewAnalytics(
    organizationId?: string,
    dateRange: string = "last_30_days",
    customStartDate?: string,
    customEndDate?: string
  ): Promise<any[]> {
    try {
      const { startDate, endDate } = this.getDateRange(
        dateRange,
        customStartDate,
        customEndDate
      );

      // Get comprehensive recipe analytics combining all data sources
      const query = `
        SELECT
          r.id as recipe_id,
          r.recipe_title as recipe_name,
          r.recipe_impression as total_views,
          r.updated_at as last_updated,
          r.recipe_status,
          r.has_recipe_public_visibility,
          COALESCE(analytics_data.recent_views, 0) as recent_views,
          analytics_data.last_viewed,
          analytics_data.unique_users,
          analytics_data.total_analytics_views,
          (r.recipe_impression + COALESCE(analytics_data.total_analytics_views, 0)) as combined_total_views
        FROM mo_recipe r
        LEFT JOIN (
          SELECT
            entity_id,
            COUNT(*) as recent_views,
            COUNT(*) as total_analytics_views,
            MAX(created_at) as last_viewed,
            COUNT(DISTINCT user_id) as unique_users
          FROM mo_recipe_analytics
          WHERE event_type = :eventType
            ${organizationId ? "AND organization_id = :organizationId" : ""}
            ${customStartDate && customEndDate ? "AND created_at BETWEEN :startDate AND :endDate" : ""}
          GROUP BY entity_id
        ) analytics_data ON r.id = analytics_data.entity_id
        WHERE r.recipe_status != 'deleted'
          ${organizationId ? "AND r.organization_id = :organizationId" : ""}
          AND (r.recipe_impression > 0 OR analytics_data.recent_views > 0)
        ORDER BY
          combined_total_views DESC,
          r.recipe_impression DESC,
          analytics_data.recent_views DESC
        LIMIT 50
      `;

      const result = await sequelize.query(query, {
        replacements: {
          eventType: AnalyticsEventType.RECIPE_VIEW,
          organizationId,
          startDate,
          endDate,
        },
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        recipe_id: row.recipe_id,
        recipe_name: row.recipe_name || `Recipe ${row.recipe_id}`,
        total_views: parseInt(row.total_views) || 0,
        recent_views: parseInt(row.recent_views) || 0,
        combined_total_views: parseInt(row.combined_total_views) || 0,
        last_viewed: row.last_viewed,
        last_updated: row.last_updated,
        unique_users: parseInt(row.unique_users) || 0,
        recipe_status: row.recipe_status,
        is_public: row.has_recipe_public_visibility,
      }));
    } catch (error: any) {
      console.error("Error getting recipe view analytics:", error);
      return [];
    }
  }

  /**
   * Get analytics summary with real data from database
   */
  async getAnalyticsSummary(filters: {
    organizationId?: string;
    page?: number;
    limit?: number;
    event_type?: string;
    entity_type?: string;
    entity_id?: number;
    start_date?: string;
    end_date?: string;
  }): Promise<any> {
    try {
      const {
        organizationId,
        page = 1,
        limit = 20,
        event_type,
        entity_type,
        entity_id,
        start_date,
        end_date,
      } = filters;

      // Build where conditions
      const whereConditions: any = {};

      if (organizationId) {
        whereConditions.organization_id = organizationId;
      }

      if (event_type) {
        whereConditions.event_type = event_type;
      }

      if (entity_type) {
        whereConditions.entity_type = entity_type;
      }

      if (entity_id) {
        whereConditions.entity_id = entity_id;
      }

      // Date filtering
      if (start_date && end_date) {
        whereConditions.created_at = {
          [Op.between]: [new Date(start_date), new Date(end_date)],
        };
      } else {
        // Default to last 30 days if no date range specified
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        whereConditions.created_at = {
          [Op.gte]: thirtyDaysAgo,
        };
      }

      // Get total counts by event type
      const eventTypeCounts = await Analytics.findAll({
        attributes: [
          "event_type",
          [sequelize.fn("COUNT", sequelize.col("id")), "count"],
        ],
        where: whereConditions,
        group: ["event_type"],
        raw: true,
      });

      // Get recent events with pagination
      const offset = (page - 1) * limit;
      const { count, rows } = await Analytics.findAndCountAll({
        where: whereConditions,
        limit: limit,
        offset: offset,
        order: [["created_at", "DESC"]],
        attributes: [
          "id",
          "event_type",
          "entity_type",
          "entity_id",
          "metadata",
          "created_at",
        ],
      });

      // Format event type counts
      const eventTypes = {
        recipe_view: 0,
        cta_click: 0,
        contact_form_submit: 0,
      };

      eventTypeCounts.forEach((item: any) => {
        if (item.event_type in eventTypes) {
          eventTypes[item.event_type as keyof typeof eventTypes] = parseInt(
            item.count
          );
        }
      });

      const totalEvents = Object.values(eventTypes).reduce(
        (sum, count) => sum + count,
        0
      );

      return {
        total_events: totalEvents,
        recent_events: rows.map((event: any) => ({
          id: event.id,
          event_type: event.event_type,
          entity_type: event.entity_type,
          entity_id: event.entity_id,
          metadata: event.metadata,
          created_at: event.created_at,
        })),
        event_types: eventTypes,
        date_range: {
          start_date: start_date || null,
          end_date: end_date || null,
        },
        pagination: {
          page: page,
          limit: limit,
          total: count,
          totalPages: Math.ceil(count / limit),
        },
      };
    } catch (error: any) {
      // Return safe defaults on error
      return {
        total_events: 0,
        recent_events: [],
        event_types: {
          recipe_view: 0,
          cta_click: 0,
          contact_form_submit: 0,
        },
        date_range: {
          start_date: filters.start_date || null,
          end_date: filters.end_date || null,
        },
        pagination: {
          page: filters.page || 1,
          limit: filters.limit || 20,
          total: 0,
          totalPages: 0,
        },
        error: "Failed to fetch analytics data",
      };
    }
  }

  // ============================================================================
  // NEW DASHBOARD METHODS - MATCHING UI DESIGN
  // ============================================================================

  /**
   * Get active users count (users who performed any action in date range)
   */
  private async safeGetActiveUsersCount(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<number> {
    try {
      const result = await Analytics.count({
        distinct: true,
        col: "user_id",
        where: {
          ...(organizationId && { organization_id: organizationId }),
          user_id: { [Op.not]: null },
          ...(startDate &&
            endDate && {
              created_at: {
                [Op.between]: [startDate, endDate],
              },
            }),
        },
      });
      return result || 0;
    } catch (error) {
      console.error("Error getting active users count:", error);
      return 0;
    }
  }

  /**
   * Get top performing category
   */
  private async safeGetTopCategory(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<{ name: string; count: number }> {
    try {
      // Create fallback categories based on recipe IDs in analytics
      const query = `
        SELECT 
          CASE 
            WHEN a.entity_id = 1 THEN 'Desserts'
            WHEN a.entity_id = 2 THEN 'Main Course'
            WHEN a.entity_id = 3 THEN 'Appetizers'
            WHEN a.entity_id = 4 THEN 'Desserts'
            WHEN a.entity_id = 5 THEN 'Beverages'
            ELSE 'Other'
          END as name,
          COUNT(*) as count
        FROM mo_recipe_analytics a
        WHERE a.event_type = 'recipe_view'
          ${organizationId ? "AND a.organization_id = :organizationId" : ""}
          ${startDate && endDate ? "AND a.created_at BETWEEN :startDate AND :endDate" : ""}
        GROUP BY 
          CASE 
            WHEN a.entity_id = 1 THEN 'Desserts'
            WHEN a.entity_id = 2 THEN 'Main Course'
            WHEN a.entity_id = 3 THEN 'Appetizers'
            WHEN a.entity_id = 4 THEN 'Desserts'
            WHEN a.entity_id = 5 THEN 'Beverages'
            ELSE 'Other'
          END
        ORDER BY count DESC
        LIMIT 1
      `;

      const result = await sequelize.query(query, {
        replacements: { organizationId, startDate, endDate },
        type: QueryTypes.SELECT,
      });

      if (result.length > 0) {
        const topCategory = result[0] as any;
        return {
          name: topCategory.name || "Unknown",
          count: parseInt(topCategory.count) || 0,
        };
      }

      return { name: "No Data", count: 0 };
    } catch (error) {
      console.error("Error getting top category:", error);
      return { name: "No Data", count: 0 };
    }
  }

  /**
   * Get recipe views trend data for line chart
   */
  private async safeGetRecipeViewsTrend(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      const query = `
        SELECT 
          DATE(created_at) as date,
          COUNT(CASE WHEN event_type = 'recipe_view' THEN 1 END) as views,
          COUNT(CASE WHEN event_type = 'recipe_bookmark' THEN 1 END) as likes
        FROM mo_recipe_analytics
        WHERE 1=1
          ${organizationId ? "AND organization_id = :organizationId" : ""}
          ${startDate && endDate ? "AND created_at BETWEEN :startDate AND :endDate" : ""}
        GROUP BY DATE(created_at)
        ORDER BY date ASC
        LIMIT 30
      `;

      const result = await sequelize.query(query, {
        replacements: { organizationId, startDate, endDate },
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        date: row.date,
        views: parseInt(row.views) || 0,
        likes: parseInt(row.likes) || 0,
      }));
    } catch (error) {
      console.error("Error getting recipe views trend:", error);
      return [];
    }
  }

  /**
   * Get category performance data for bar chart
   */
  private async safeGetCategoryPerformance(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      // Use analytics data to build category performance with fallback categories
      const query = `
        SELECT 
          CASE 
            WHEN a.entity_id = 1 THEN 'Desserts'
            WHEN a.entity_id = 2 THEN 'Main Course'
            WHEN a.entity_id = 3 THEN 'Appetizers'
            WHEN a.entity_id = 4 THEN 'Desserts'
            WHEN a.entity_id = 5 THEN 'Beverages'
            ELSE 'Other'
          END as category,
          COUNT(DISTINCT a.entity_id) as recipes,
          COUNT(*) as views
        FROM mo_recipe_analytics a
        WHERE a.event_type = 'recipe_view'
          ${organizationId ? "AND a.organization_id = :organizationId" : ""}
          ${startDate && endDate ? "AND a.created_at BETWEEN :startDate AND :endDate" : ""}
        GROUP BY 
          CASE 
            WHEN a.entity_id = 1 THEN 'Desserts'
            WHEN a.entity_id = 2 THEN 'Main Course'
            WHEN a.entity_id = 3 THEN 'Appetizers'
            WHEN a.entity_id = 4 THEN 'Desserts'
            WHEN a.entity_id = 5 THEN 'Beverages'
            ELSE 'Other'
          END
        HAVING COUNT(*) > 0
        ORDER BY views DESC
        LIMIT 10
      `;

      const result = await sequelize.query(query, {
        replacements: { organizationId, startDate, endDate },
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        category: row.category,
        recipes: parseInt(row.recipes) || 0,
        views: parseInt(row.views) || 0,
      }));
    } catch (error) {
      console.error("Error getting category performance:", error);
      return [];
    }
  }

  /**
   * Get user engagement heatmap data
   */
  private async safeGetUserEngagementHeatmap(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      const query = `
        SELECT 
          HOUR(created_at) as hour,
          DAYOFWEEK(created_at) as day_of_week,
          COUNT(*) as engagement_count
        FROM mo_recipe_analytics
        WHERE 1=1
          ${organizationId ? "AND organization_id = :organizationId" : ""}
          ${startDate && endDate ? "AND created_at BETWEEN :startDate AND :endDate" : ""}
        GROUP BY HOUR(created_at), DAYOFWEEK(created_at)
        ORDER BY day_of_week, hour
      `;

      const result = await sequelize.query(query, {
        replacements: { organizationId, startDate, endDate },
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        hour: parseInt(row.hour),
        day: parseInt(row.day_of_week),
        count: parseInt(row.engagement_count) || 0,
      }));
    } catch (error) {
      console.error("Error getting user engagement heatmap:", error);
      return [];
    }
  }

  /**
   * Get conversion funnel data
   */
  private async safeGetConversionFunnel(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      const [visitors, recipeViews, bookmarks, shares, subscriptions] =
        await Promise.all([
          Analytics.count({
            distinct: true,
            col: "session_id",
            where: {
              ...(organizationId && { organization_id: organizationId }),
              ...(startDate &&
                endDate && {
                  created_at: { [Op.between]: [startDate, endDate] },
                }),
            },
          }),
          Analytics.count({
            where: {
              event_type: AnalyticsEventType.RECIPE_VIEW,
              ...(organizationId && { organization_id: organizationId }),
              ...(startDate &&
                endDate && {
                  created_at: { [Op.between]: [startDate, endDate] },
                }),
            },
          }),
          Analytics.count({
            where: {
              event_type: AnalyticsEventType.RECIPE_BOOKMARK,
              ...(organizationId && { organization_id: organizationId }),
              ...(startDate &&
                endDate && {
                  created_at: { [Op.between]: [startDate, endDate] },
                }),
            },
          }),
          Analytics.count({
            where: {
              event_type: AnalyticsEventType.RECIPE_SHARE,
              ...(organizationId && { organization_id: organizationId }),
              ...(startDate &&
                endDate && {
                  created_at: { [Op.between]: [startDate, endDate] },
                }),
            },
          }),
          Analytics.count({
            where: {
              event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
              ...(organizationId && { organization_id: organizationId }),
              ...(startDate &&
                endDate && {
                  created_at: { [Op.between]: [startDate, endDate] },
                }),
            },
          }),
        ]);

      const maxValue =
        Math.max(visitors, recipeViews, bookmarks, shares, subscriptions) || 1;

      return [
        { stage: "Visitors", count: visitors, percentage: 100 },
        {
          stage: "Recipe Views",
          count: recipeViews,
          percentage: Math.round((recipeViews / maxValue) * 100),
        },
        {
          stage: "Bookmarks",
          count: bookmarks,
          percentage: Math.round((bookmarks / maxValue) * 100),
        },
        {
          stage: "Shares",
          count: shares,
          percentage: Math.round((shares / maxValue) * 100),
        },
        {
          stage: "Subscriptions",
          count: subscriptions,
          percentage: Math.round((subscriptions / maxValue) * 100),
        },
      ];
    } catch (error) {
      console.error("Error getting conversion funnel:", error);
      return [];
    }
  }

  /**
   * Calculate monthly revenue (placeholder - implement based on your business model)
   */
  private async calculateMonthlyRevenue(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<number> {
    try {
      // This is a placeholder implementation
      // You should implement this based on your actual revenue model
      // For example: subscription fees, premium features, etc.

      const contactSubmissions = await Analytics.count({
        where: {
          event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
          ...(organizationId && { organization_id: organizationId }),
          ...(startDate &&
            endDate && {
              created_at: { [Op.between]: [startDate, endDate] },
            }),
        },
      });

      // Example: Estimate $50 revenue per contact form submission
      return contactSubmissions * 50;
    } catch (error) {
      console.error("Error calculating monthly revenue:", error);
      return 0;
    }
  }

  // ============================================================================
  // REAL DATABASE QUERY METHODS - NO FALLBACK DATA
  // ============================================================================

  /**
   * Get real recipe count with category filtering - ENHANCED VERSION
   */
  private async getRealRecipeCount(
    organizationId?: string,
    categoryType?: string
  ): Promise<number> {
    try {
      let query = `
        SELECT COUNT(DISTINCT r.id) as total
        FROM mo_recipe r
      `;

      const replacements: any = {};

      if (categoryType) {
        query += `
          INNER JOIN mo_recipe_category rc ON r.id = rc.recipe_id AND rc.status = 'active'
          INNER JOIN mo_category c ON rc.category_id = c.id
        `;
      }

      query += `
        WHERE r.recipe_status != 'deleted'
      `;

      if (organizationId) {
        query += ` AND r.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (categoryType) {
        // Enhanced category matching - support both exact match and LIKE match
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike) AND c.category_type = 'recipe' AND c.category_status = 'active'`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      const count = parseInt((result[0] as any)?.total || "0");
      return count;
    } catch (error) {
      console.error("Error getting real recipe count:", error);
      return 0;
    }
  }
  /**
   * Get real top category with filtering - ENHANCED VERSION
   */
  private async getRealTopCategory(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date,
    categoryType?: string
  ): Promise<{ name: string; count: number }> {
    try {
      let query = `
        SELECT
          c.category_name as name,
          COUNT(DISTINCT r.id) as recipe_count,
          COALESCE(analytics_data.analytics_views, 0) as analytics_views,
        FROM mo_category c
        INNER JOIN mo_recipe_category rc ON c.id = rc.category_id
        INNER JOIN mo_recipe r ON rc.recipe_id = r.id
        LEFT JOIN (
          SELECT
            c2.id as category_id,
            COUNT(*) as analytics_views
          FROM mo_recipe_analytics a
          INNER JOIN mo_recipe r2 ON a.entity_id = r2.id
          INNER JOIN mo_recipe_category rc2 ON r2.id = rc2.recipe_id
          INNER JOIN mo_category c2 ON rc2.category_id = c2.id
          WHERE a.event_type = :eventType
            ${organizationId ? "AND a.organization_id = :organizationId" : ""}
            ${startDate && endDate ? "AND a.created_at BETWEEN :startDate AND :endDate" : ""}
          GROUP BY c2.id
        ) analytics_data ON c.id = analytics_data.category_id
        WHERE c.category_status = 'active'
          AND c.category_type = 'recipe'
          AND r.recipe_status = 'publish'
      `;

      const replacements: any = {
        eventType: AnalyticsEventType.RECIPE_VIEW,
      };

      if (organizationId) {
        query += ` AND r.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (categoryType) {
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike)`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      if (startDate && endDate) {
        replacements.startDate = startDate;
        replacements.endDate = endDate;
      }

      query += `
        GROUP BY c.id, c.category_name, analytics_data.analytics_views
        ORDER BY analytics_views DESC, recipe_count DESC
        LIMIT 1
      `;

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      const topCategory = result[0] as any;
      if (topCategory) {
        return {
          name: topCategory.name,
          count:
            parseInt(topCategory.total_views) ||
            parseInt(topCategory.recipe_count) ||
            0,
        };
      }

      return { name: "No Data", count: 0 };
    } catch (error) {
      console.error("Error getting real top category:", error);
      return { name: "No Data", count: 0 };
    }
  }

  /**
   * Get real total recipe views with category filtering
   */
  private async getRealTotalRecipeViews(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date,
    categoryType?: string
  ): Promise<{ totalViews: number }> {
    try {
      let query = `
        SELECT COUNT(*) as total
        FROM mo_recipe_analytics a
      `;

      const replacements: any = {
        eventType: AnalyticsEventType.RECIPE_VIEW,
      };

      if (categoryType) {
        query += `
          INNER JOIN mo_recipe r ON a.entity_id = r.id
          INNER JOIN mo_recipe_category rc ON r.id = rc.recipe_id
          INNER JOIN mo_category c ON rc.category_id = c.id
        `;
      }

      query += `
        WHERE a.event_type = :eventType
      `;

      if (organizationId) {
        query += ` AND a.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (startDate && endDate) {
        query += ` AND a.created_at BETWEEN :startDate AND :endDate`;
        replacements.startDate = startDate;
        replacements.endDate = endDate;
      }

      if (categoryType) {
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike) AND c.category_type = 'recipe' AND c.category_status = 'active'`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      return { totalViews: parseInt((result[0] as any)?.total || "0") };
    } catch (error) {
      console.error("Error getting real total recipe views:", error);
      return { totalViews: 0 };
    }
  }

  /**
   * Get real analytics count with category filtering
   */
  private async getRealAnalyticsCount(
    eventType: AnalyticsEventType,
    organizationId?: string,
    startDate?: Date,
    endDate?: Date,
    categoryType?: string
  ): Promise<number> {
    try {
      let query = `
        SELECT COUNT(*) as total
        FROM mo_recipe_analytics a
      `;

      const replacements: any = { eventType };

      if (categoryType) {
        query += `
          INNER JOIN mo_recipe r ON a.entity_id = r.id
          INNER JOIN mo_recipe_category rc ON r.id = rc.recipe_id
          INNER JOIN mo_category c ON rc.category_id = c.id
        `;
      }

      query += `
        WHERE a.event_type = :eventType
      `;

      if (organizationId) {
        query += ` AND a.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (startDate && endDate) {
        query += ` AND a.created_at BETWEEN :startDate AND :endDate`;
        replacements.startDate = startDate;
        replacements.endDate = endDate;
      }

      if (categoryType) {
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike) AND c.category_type = 'recipe' AND c.category_status = 'active'`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      return parseInt((result[0] as any)?.total || "0");
    } catch (error) {
      console.error(
        `Error getting real analytics count for ${eventType}:`,
        error
      );
      return 0;
    }
  }

  /**
   * Get real recipe views trend with category filtering and recipe names
   */
  private async getRealRecipeViewsTrend(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date,
    categoryType?: string
  ): Promise<any[]> {
    try {
      let query = `
        SELECT
          DATE(a.created_at) as date,
          COUNT(*) as views,
          GROUP_CONCAT(DISTINCT r.recipe_title ORDER BY r.recipe_title SEPARATOR ', ') as recipe_names,
          COUNT(DISTINCT a.entity_id) as unique_recipes
        FROM mo_recipe_analytics a
        INNER JOIN mo_recipe r ON a.entity_id = r.id
      `;

      const replacements: any = {
        eventType: AnalyticsEventType.RECIPE_VIEW,
      };

      if (categoryType) {
        query += `
          INNER JOIN mo_recipe_category rc ON r.id = rc.recipe_id
          INNER JOIN mo_category c ON rc.category_id = c.id
        `;
      }

      query += `
        WHERE a.event_type = :eventType
          AND r.recipe_status != 'deleted'
      `;

      if (organizationId) {
        query += ` AND a.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (startDate && endDate) {
        query += ` AND a.created_at BETWEEN :startDate AND :endDate`;
        replacements.startDate = startDate;
        replacements.endDate = endDate;
      }

      if (categoryType) {
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike) AND c.category_type = 'recipe' AND c.category_status = 'active'`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      query += `
        GROUP BY DATE(a.created_at)
        ORDER BY date ASC
        LIMIT 30
      `;

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        date: row.date,
        views: parseInt(row.views),
        unique_recipes: parseInt(row.unique_recipes),
        recipe_names: row.recipe_names
          ? row.recipe_names.split(", ").slice(0, 5)
          : [], // Limit to top 5 recipe names
        top_recipes: row.recipe_names
          ? row.recipe_names.split(", ").slice(0, 3).join(", ")
          : "No recipes",
      }));
    } catch (error) {
      console.error("Error getting real recipe views trend:", error);
      return [];
    }
  }

  /**
   * Get real category performance with filtering - ENHANCED VERSION
   */
  private async getRealCategoryPerformance(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date,
    categoryType?: string
  ): Promise<any[]> {
    try {
      // First, get all categories with their recipe counts
      let query = `
        SELECT
          c.category_name as category,
          COUNT(DISTINCT r.id) as recipes,
          COALESCE(SUM(r.recipe_impression), 0) as total_impressions
        FROM mo_category c
        INNER JOIN mo_recipe_category rc ON c.id = rc.category_id
        INNER JOIN mo_recipe r ON rc.recipe_id = r.id
        WHERE c.category_status = 'active'
          AND c.category_type = 'recipe'
          AND r.recipe_status != 'deleted'
      `;

      const replacements: any = {};

      if (organizationId) {
        query += ` AND r.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (categoryType) {
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike)`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      query += `
        GROUP BY c.id, c.category_name
        ORDER BY total_impressions DESC, recipes DESC
        LIMIT 10
      `;

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      // If no categories found, return empty array
      if (!result || result.length === 0) {
        return [];
      }

      // Now get analytics views for each category
      const categoryPerformance = [];
      for (const row of result as any[]) {
        let analyticsQuery = `
          SELECT COUNT(*) as analytics_views
          FROM mo_recipe_analytics a
          INNER JOIN mo_recipe r ON a.entity_id = r.id
          INNER JOIN mo_recipe_category rc ON r.id = rc.recipe_id
          INNER JOIN mo_category c ON rc.category_id = c.id
          WHERE a.event_type = :eventType
            AND c.category_name = :categoryName
            AND c.category_status = 'active'
            AND r.recipe_status != 'deleted'
        `;

        const analyticsReplacements: any = {
          eventType: AnalyticsEventType.RECIPE_VIEW,
          categoryName: row.category,
        };

        if (organizationId) {
          analyticsQuery += ` AND a.organization_id = :organizationId`;
          analyticsReplacements.organizationId = organizationId;
        }

        if (startDate && endDate) {
          analyticsQuery += ` AND a.created_at BETWEEN :startDate AND :endDate`;
          analyticsReplacements.startDate = startDate;
          analyticsReplacements.endDate = endDate;
        }

        const analyticsResult = await sequelize.query(analyticsQuery, {
          replacements: analyticsReplacements,
          type: QueryTypes.SELECT,
        });

        const analyticsViews = parseInt(
          (analyticsResult[0] as any)?.analytics_views || "0"
        );
        const impressionViews = parseInt(row.total_impressions);
        const totalViews = impressionViews + analyticsViews;

        // Enhanced category performance with better data
        categoryPerformance.push({
          category: row.category,
          views: totalViews,
          analytics_views: analyticsViews,
          impression_views: impressionViews,
          recipes: parseInt(row.recipes),
          // Add performance metrics
          avg_views_per_recipe:
            totalViews > 0
              ? (totalViews / parseInt(row.recipes)).toFixed(1)
              : "0",
          performance_score: this.calculateCategoryPerformanceScore(
            totalViews,
            parseInt(row.recipes)
          ),
        });
      }

      return categoryPerformance.sort((a, b) => b.views - a.views);
    } catch (error) {
      console.error("Error getting real category performance:", error);
      return [];
    }
  }

  /**
   * Get real user engagement heatmap
   */
  private async getRealUserEngagementHeatmap(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date,
    categoryType?: string
  ): Promise<any[]> {
    try {
      let query = `
        SELECT
          HOUR(a.created_at) as hour,
          DAYOFWEEK(a.created_at) as day_of_week,
          COUNT(*) as engagement_count
        FROM mo_recipe_analytics a
      `;

      const replacements: any = {};

      if (categoryType) {
        query += `
          INNER JOIN mo_recipe r ON a.entity_id = r.id
          INNER JOIN mo_recipe_category rc ON r.id = rc.recipe_id
          INNER JOIN mo_category c ON rc.category_id = c.id
        `;
      }

      query += `
        WHERE a.event_type IN (:eventTypes)
      `;

      replacements.eventTypes = [
        AnalyticsEventType.RECIPE_VIEW,
        AnalyticsEventType.CTA_CLICK,
        AnalyticsEventType.RECIPE_BOOKMARK,
        AnalyticsEventType.RECIPE_SHARE,
      ];

      if (organizationId) {
        query += ` AND a.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (startDate && endDate) {
        query += ` AND a.created_at BETWEEN :startDate AND :endDate`;
        replacements.startDate = startDate;
        replacements.endDate = endDate;
      }

      if (categoryType) {
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike) AND c.category_type = 'recipe' AND c.category_status = 'active'`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      query += `
        GROUP BY HOUR(a.created_at), DAYOFWEEK(a.created_at)
        ORDER BY day_of_week, hour
      `;

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        hour: parseInt(row.hour),
        day_of_week: parseInt(row.day_of_week),
        engagement_count: parseInt(row.engagement_count),
      }));
    } catch (error) {
      console.error("Error getting real user engagement heatmap:", error);
      return [];
    }
  }

  /**
   * Get real conversion funnel
   */
  private async getRealConversionFunnel(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date,
    categoryType?: string
  ): Promise<any[]> {
    try {
      const steps = [
        { name: "Recipe Views", event_type: AnalyticsEventType.RECIPE_VIEW },
        { name: "CTA Clicks", event_type: AnalyticsEventType.CTA_CLICK },
        {
          name: "Contact Submissions",
          event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
        },
      ];

      const funnelData: Array<{
        step: string;
        count: number;
        conversion_rate: string;
      }> = [];

      for (const step of steps) {
        const count = await this.getRealAnalyticsCount(
          step.event_type,
          organizationId,
          startDate,
          endDate,
          categoryType
        );

        funnelData.push({
          step: step.name,
          count: count,
          conversion_rate:
            funnelData.length > 0 && funnelData[0].count > 0
              ? ((count / funnelData[0].count) * 100).toFixed(2)
              : funnelData.length === 0
                ? "100.00"
                : "0.00",
        });
      }

      return funnelData;
    } catch (error) {
      console.error("Error getting real conversion funnel:", error);
      return [];
    }
  }

  /**
   * Get real recent activity
   */
  private async getRealRecentActivity(
    organizationId?: string,
    limit: number = 10,
    categoryType?: string
  ): Promise<any[]> {
    try {
      let query = `
        SELECT
          a.event_type,
          a.entity_id,
          a.created_at,
          a.metadata,
          r.recipe_title
        FROM mo_recipe_analytics a
        LEFT JOIN mo_recipe r ON a.entity_id = r.id
      `;

      const replacements: any = {};

      if (categoryType) {
        query += `
          INNER JOIN mo_recipe_category rc ON r.id = rc.recipe_id
          INNER JOIN mo_category c ON rc.category_id = c.id
        `;
      }

      query += `
        WHERE 1=1
      `;

      if (organizationId) {
        query += ` AND a.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (categoryType) {
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike) AND c.category_type = 'recipe' AND c.category_status = 'active'`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      query += `
        ORDER BY a.created_at DESC
        LIMIT :limit
      `;

      replacements.limit = limit;

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => {
        const metadata = row.metadata ? JSON.parse(row.metadata) : {};

        return {
          event_type: row.event_type,
          entity_id: row.entity_id,
          recipe_title: row.recipe_title || `Recipe ${row.entity_id}`,
          created_at: row.created_at,
          metadata,
          // Enhanced activity description
          activity_description: this.generateActivityDescription(
            row.event_type,
            metadata
          ),
          time_ago: this.getTimeAgo(new Date(row.created_at)),
        };
      });
    } catch (error) {
      console.error("Error getting real recent activity:", error);
      return [];
    }
  }

  /**
   * Calculate growth rate with category filtering
   */
  private async calculateGrowthRate(
    organizationId?: string,
    dateRange: string = "last_30_days",
    categoryType?: string
  ): Promise<number> {
    try {
      const { startDate, endDate } = this.getDateRange(dateRange);

      // Calculate previous period
      const periodDays = Math.ceil(
        (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
      );
      const previousStartDate = new Date(startDate);
      previousStartDate.setDate(previousStartDate.getDate() - periodDays);
      const previousEndDate = new Date(startDate);

      const [currentViews, previousViews] = await Promise.all([
        this.getRealTotalRecipeViews(
          organizationId,
          startDate,
          endDate,
          categoryType
        ),
        this.getRealTotalRecipeViews(
          organizationId,
          previousStartDate,
          previousEndDate,
          categoryType
        ),
      ]);

      if (previousViews.totalViews === 0) return 0;

      return (
        ((currentViews.totalViews - previousViews.totalViews) /
          previousViews.totalViews) *
        100
      );
    } catch (error) {
      console.error("Error calculating growth rate:", error);
      return 0;
    }
  }

  /**
   * Calculate category performance score (0-100)
   */
  private calculateCategoryPerformanceScore(
    totalViews: number,
    recipeCount: number
  ): number {
    if (recipeCount === 0) return 0;

    const avgViewsPerRecipe = totalViews / recipeCount;

    // Performance scoring logic
    if (avgViewsPerRecipe >= 100) return 100;
    if (avgViewsPerRecipe >= 50) return 85;
    if (avgViewsPerRecipe >= 25) return 70;
    if (avgViewsPerRecipe >= 10) return 55;
    if (avgViewsPerRecipe >= 5) return 40;
    if (avgViewsPerRecipe >= 1) return 25;

    return Math.min(Math.round(avgViewsPerRecipe * 25), 100);
  }

  /**
   * Generate human-readable activity description
   */
  private generateActivityDescription(
    eventType: string,
    metadata: any
  ): string {
    switch (eventType) {
      case "recipe_view":
        return `Viewed recipe "${metadata.recipe_name || "Unknown"}"`;
      case "cta_click":
        return `Clicked "${metadata.cta_text || metadata.cta_type || "CTA"}" button`;
      case "contact_form_submit":
        return `Submitted contact form for "${metadata.recipe_name || "Unknown"}"`;
      case "recipe_bookmark":
        return `Bookmarked recipe "${metadata.recipe_name || "Unknown"}"`;
      case "recipe_share":
        return `Shared recipe "${metadata.recipe_name || "Unknown"}" on ${metadata.share_platform || "social media"}`;
      default:
        return `Performed ${eventType.replace("_", " ")}`;
    }
  }

  /**
   * Get human-readable time ago string
   */
  private getTimeAgo(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return "Just now";
    if (diffMins < 60)
      return `${diffMins} minute${diffMins > 1 ? "s" : ""} ago`;
    if (diffHours < 24)
      return `${diffHours} hour${diffHours > 1 ? "s" : ""} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? "s" : ""} ago`;

    return date.toLocaleDateString();
  }

  /**
   * Generate dashboard insights based on data
   */
  private generateDashboardInsights(data: any): string[] {
    const insights: string[] = [];

    // Recipe performance insights
    if (data.totalRecipes > 0) {
      const avgViews = data.totalViews / data.totalRecipes;
      if (avgViews > 50) {
        insights.push(
          `🚀 Excellent performance! Your recipes average ${avgViews.toFixed(1)} views each.`
        );
      } else if (avgViews > 10) {
        insights.push(
          `📈 Good engagement! Consider promoting recipes with lower views.`
        );
      } else {
        insights.push(
          `💡 Opportunity: Focus on SEO and social media promotion to increase visibility.`
        );
      }
    }

    // Conversion insights
    const conversionRate =
      data.totalViews > 0 ? (data.contactCount / data.totalViews) * 100 : 0;
    if (conversionRate > 5) {
      insights.push(
        `🎯 High conversion rate of ${conversionRate.toFixed(1)}%! Your CTAs are working well.`
      );
    } else if (conversionRate > 2) {
      insights.push(
        `📊 Decent conversion rate. Consider A/B testing different CTA styles.`
      );
    } else {
      insights.push(
        `🔧 Low conversion rate. Review your CTA placement and messaging.`
      );
    }

    // Growth insights
    if (data.growthRate > 10) {
      insights.push(
        `📈 Amazing growth of ${data.growthRate.toFixed(1)}% this period!`
      );
    } else if (data.growthRate > 0) {
      insights.push(`🌱 Positive growth trend. Keep up the good work!`);
    } else if (data.growthRate < -10) {
      insights.push(
        `⚠️ Declining views. Consider refreshing content or improving SEO.`
      );
    }

    // Category insights
    if (data.categoryPerformance && data.categoryPerformance.length > 0) {
      const topCategory = data.categoryPerformance[0];
      if (topCategory.views > 0) {
        insights.push(
          `🏆 "${topCategory.category}" is your top-performing category with ${topCategory.views} views.`
        );
      }
    }

    return insights.length > 0
      ? insights
      : ["📊 Keep creating great content to see more insights!"];
  }

  // ============================================================================
  // END NEW DASHBOARD METHODS
  // ============================================================================

  /**
   * Get recipe view statistics for private recipes with assigned users
   * Returns count of unique users who have viewed the recipe
   */
  async getRecipeViewStatistics(
    recipeId: number,
    organizationId?: string
  ): Promise<{ status: boolean; data?: any; message: string }> {
    try {
      // First, verify the recipe exists and is private with assigned users
      const recipeCheckQuery = `
        SELECT
          r.id,
          r.recipe_title,
          r.has_recipe_private_visibility,
          r.organization_id,
          COUNT(ru.user_id) as assigned_users_count
        FROM mo_recipe r
        LEFT JOIN mo_recipe_user ru ON r.id = ru.recipe_id AND ru.status = 'active'
        WHERE r.id = :recipeId
          AND r.recipe_status != 'deleted'
          ${organizationId ? "AND r.organization_id = :organizationId" : ""}
        GROUP BY r.id, r.recipe_title, r.has_recipe_private_visibility, r.organization_id
      `;

      const recipeResult = await sequelize.query(recipeCheckQuery, {
        replacements: { recipeId, organizationId },
        type: QueryTypes.SELECT,
      });

      if (!recipeResult.length) {
        return {
          status: false,
          message: "Recipe not found or access denied",
        };
      }

      const recipe = recipeResult[0] as any;

      // Check if recipe is private
      if (!recipe.has_recipe_private_visibility) {
        return {
          status: false,
          message:
            "Recipe view statistics are only available for private recipes",
        };
      }

      // Check if recipe has assigned users
      if (recipe.assigned_users_count === 0) {
        return {
          status: false,
          message: "Recipe must have assigned users to view statistics",
        };
      }

      // Get view statistics from analytics table
      const viewStatsQuery = `
        SELECT
          COUNT(DISTINCT ra.user_id) as unique_viewers,
          COUNT(*) as total_views,
          MAX(ra.created_at) as last_viewed_at
        FROM mo_recipe_analytics ra
        INNER JOIN mo_recipe_user ru ON ra.entity_id = ru.recipe_id
          AND ra.user_id = ru.user_id
          AND ru.status = 'active'
        WHERE ra.event_type = 'recipe_view'
          AND ra.entity_type = 'recipe'
          AND ra.entity_id = :recipeId
          AND ra.user_id IS NOT NULL
          ${organizationId ? "AND ra.organization_id = :organizationId" : ""}
      `;

      const viewStatsResult = await sequelize.query(viewStatsQuery, {
        replacements: { recipeId, organizationId },
        type: QueryTypes.SELECT,
      });

      const stats = viewStatsResult[0] as any;

      // Get list of users who viewed the recipe with their view counts
      const userViewsQuery = `
        SELECT
          u.id as user_id,
          u.user_email,
          COUNT(*) as view_count,
          MAX(ra.created_at) as last_viewed_at,
          MIN(ra.created_at) as first_viewed_at
        FROM mo_recipe_analytics ra
        INNER JOIN mo_recipe_user ru ON ra.entity_id = ru.recipe_id
          AND ra.user_id = ru.user_id
          AND ru.status = 'active'
        INNER JOIN users u ON ra.user_id = u.id
        WHERE ra.event_type = 'recipe_view'
          AND ra.entity_type = 'recipe'
          AND ra.entity_id = :recipeId
          AND ra.user_id IS NOT NULL
          ${organizationId ? "AND ra.organization_id = :organizationId" : ""}
        GROUP BY u.id, u.user_email
        ORDER BY view_count DESC, last_viewed_at DESC
      `;

      const userViewsResult = await sequelize.query(userViewsQuery, {
        replacements: { recipeId, organizationId },
        type: QueryTypes.SELECT,
      });

      return {
        status: true,
        message: "Recipe view statistics retrieved successfully",
        data: {
          recipe_id: recipeId,
          recipe_title: recipe.recipe_title,
          assigned_users_count: parseInt(recipe.assigned_users_count),
          statistics: {
            unique_viewers: parseInt(stats.unique_viewers) || 0,
            total_views: parseInt(stats.total_views) || 0,
            last_viewed_at: stats.last_viewed_at,
          },
          user_views: userViewsResult.map((row: any) => ({
            user_id: row.user_id,
            user_email: row.user_email,
            view_count: parseInt(row.view_count),
            first_viewed_at: row.first_viewed_at,
            last_viewed_at: row.last_viewed_at,
          })),
        },
      };
    } catch (error: any) {
      console.error("Error getting recipe view statistics:", error);
      return {
        status: false,
        message: "Error retrieving recipe view statistics",
      };
    }
  }

  /**
   * Reset recipe view statistics for private recipes with assigned users
   * Supports two reset options:
   * 1. Reset for specific user IDs (remove view statistics for those users)
   * 2. Reset all statistics for a recipe (remove all user view statistics)
   */
  async resetRecipeViewStatistics(
    recipeId: number,
    organizationId?: string,
    userIds?: number[]
  ): Promise<{ status: boolean; data?: any; message: string }> {
    try {
      // First, verify the recipe exists and is private with assigned users
      const recipeCheckQuery = `
        SELECT
          r.id,
          r.recipe_title,
          r.has_recipe_private_visibility,
          r.organization_id,
          COUNT(ru.user_id) as assigned_users_count
        FROM mo_recipe r
        LEFT JOIN mo_recipe_user ru ON r.id = ru.recipe_id AND ru.status = 'active'
        WHERE r.id = :recipeId
          AND r.recipe_status != 'deleted'
          ${organizationId ? "AND r.organization_id = :organizationId" : ""}
        GROUP BY r.id, r.recipe_title, r.has_recipe_private_visibility, r.organization_id
      `;

      const recipeResult = await sequelize.query(recipeCheckQuery, {
        replacements: { recipeId, organizationId },
        type: QueryTypes.SELECT,
      });

      if (!recipeResult.length) {
        return {
          status: false,
          message: "Recipe not found or access denied",
        };
      }

      const recipe = recipeResult[0] as any;

      // Check if recipe is private
      if (!recipe.has_recipe_private_visibility) {
        return {
          status: false,
          message:
            "Recipe view statistics reset is only available for private recipes",
        };
      }

      // Check if recipe has assigned users
      if (recipe.assigned_users_count === 0) {
        return {
          status: false,
          message: "Recipe must have assigned users to reset statistics",
        };
      }

      let deletedCount = 0;
      let resetType = "";

      if (userIds && userIds.length > 0) {
        // Reset for specific user IDs - only remove statistics for those users
        // First verify that the specified users are actually assigned to this recipe
        const assignedUsersQuery = `
          SELECT user_id
          FROM mo_recipe_user
          WHERE recipe_id = :recipeId
            AND user_id IN (${userIds.map(() => "?").join(",")})
            AND status = 'active'
            ${organizationId ? "AND organization_id = :organizationId" : ""}
        `;

        const assignedUsersResult = await sequelize.query(assignedUsersQuery, {
          replacements: [recipeId, ...userIds, organizationId].filter(Boolean),
          type: QueryTypes.SELECT,
        });

        const validUserIds = assignedUsersResult.map((row: any) => row.user_id);

        if (validUserIds.length === 0) {
          return {
            status: false,
            message: "None of the specified users are assigned to this recipe",
          };
        }

        // Delete analytics records for the valid assigned users
        const deleteSpecificQuery = `
          DELETE FROM mo_recipe_analytics
          WHERE event_type = 'recipe_view'
            AND entity_type = 'recipe'
            AND entity_id = :recipeId
            AND user_id IN (${validUserIds.map(() => "?").join(",")})
            ${organizationId ? "AND organization_id = :organizationId" : ""}
        `;

        const deleteResult = await sequelize.query(deleteSpecificQuery, {
          replacements: [recipeId, ...validUserIds, organizationId].filter(
            Boolean
          ),
          type: QueryTypes.DELETE,
        });

        deletedCount = (deleteResult[1] as any) || 0;
        resetType = `specific users (${validUserIds.length} users)`;

        return {
          status: true,
          message:
            "Recipe view statistics reset successfully for specified users",
          data: {
            recipe_id: recipeId,
            recipe_title: recipe.recipe_title,
            reset_type: resetType,
            affected_users: validUserIds,
            deleted_records: deletedCount,
          },
        };
      } else {
        // Reset all statistics for the recipe - remove all user view statistics
        const deleteAllQuery = `
          DELETE ra FROM mo_recipe_analytics ra
          INNER JOIN mo_recipe_user ru ON ra.entity_id = ru.recipe_id
            AND ra.user_id = ru.user_id
            AND ru.status = 'active'
          WHERE ra.event_type = 'recipe_view'
            AND ra.entity_type = 'recipe'
            AND ra.entity_id = :recipeId
            AND ra.user_id IS NOT NULL
            ${organizationId ? "AND ra.organization_id = :organizationId" : ""}
        `;

        const deleteResult = await sequelize.query(deleteAllQuery, {
          replacements: { recipeId, organizationId },
          type: QueryTypes.DELETE,
        });

        deletedCount = (deleteResult[1] as any) || 0;
        resetType = "all users";

        return {
          status: true,
          message: "Recipe view statistics reset successfully for all users",
          data: {
            recipe_id: recipeId,
            recipe_title: recipe.recipe_title,
            reset_type: resetType,
            assigned_users_count: parseInt(recipe.assigned_users_count),
            deleted_records: deletedCount,
          },
        };
      }
    } catch (error: any) {
      console.error("Error resetting recipe view statistics:", error);
      return {
        status: false,
        message: "Error resetting recipe view statistics",
      };
    }
  }
}

export default new AnalyticsService();
