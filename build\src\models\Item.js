"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Item = exports.item_IEC = exports.item_category = exports.item_external_location = exports.item_type = exports.item_status = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var item_status;
(function (item_status) {
    item_status["ACTIVE"] = "active";
    item_status["DELETED"] = "deleted";
})(item_status || (exports.item_status = item_status = {}));
var item_type;
(function (item_type) {
    item_type["VIDEO"] = "video";
    item_type["AUDIO"] = "audio";
    item_type["IMAGE"] = "image";
    item_type["LINK"] = "link";
    item_type["TEXT"] = "text";
    item_type["BLOB"] = "blob";
    item_type["PDF"] = "pdf";
})(item_type || (exports.item_type = item_type = {}));
var item_external_location;
(function (item_external_location) {
    item_external_location["YES"] = "yes";
    item_external_location["NO"] = "no";
})(item_external_location || (exports.item_external_location = item_external_location = {}));
var item_category;
(function (item_category) {
    item_category["PROFILE"] = "profile";
    item_category["ITEM"] = "item";
    item_category["DOCUMENT"] = "document";
    item_category["SIGNATURE"] = "signature";
    item_category["OTHER"] = "other";
    item_category["RECIPE_IMAGE"] = "recipe_image";
    item_category["RECIPE_VIDEO"] = "recipe_video";
    item_category["RECIPE_DOCUMENT"] = "recipe_document";
    item_category["INGREDIENT_IMAGE"] = "ingredient_image";
    item_category["CATEGORY_ICON"] = "category_icon";
    item_category["ATTRIBUTE_ICON"] = "attribute_icon";
    item_category["UNIT_ICON"] = "unit_icon";
})(item_category || (exports.item_category = item_category = {}));
var item_IEC;
(function (item_IEC) {
    item_IEC["B"] = "B";
    item_IEC["KB"] = "KB";
    item_IEC["MB"] = "MB";
    item_IEC["GB"] = "GB";
    item_IEC["TB"] = "TB";
    item_IEC["PB"] = "PB";
    item_IEC["EB"] = "EB";
    item_IEC["ZB"] = "ZB";
    item_IEC["YB"] = "YB";
    item_IEC["IB"] = "iB";
    item_IEC["KIB"] = "KiB";
    item_IEC["MIB"] = "MiB";
    item_IEC["GIB"] = "GiB";
    item_IEC["TIB"] = "TiB";
    item_IEC["PIB"] = "PiB";
    item_IEC["EIB"] = "EiB";
    item_IEC["ZIB"] = "ZiB";
    item_IEC["YIB"] = "YiB";
})(item_IEC || (exports.item_IEC = item_IEC = {}));
class Item extends sequelize_1.Model {
}
exports.Item = Item;
Item.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    item_type: {
        type: sequelize_1.DataTypes.ENUM,
        values: Object.values(item_type),
        allowNull: false,
    },
    item_name: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    item_hash: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        unique: false,
    },
    item_mime_type: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    item_extension: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    item_size: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    item_IEC: {
        type: sequelize_1.DataTypes.ENUM,
        values: Object.values(item_IEC),
        allowNull: true,
    },
    item_location: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    },
    item_external_location: {
        type: sequelize_1.DataTypes.ENUM,
        values: Object.values(item_external_location),
        defaultValue: item_external_location.NO,
    },
    item_organization_id: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    },
    item_category: {
        type: sequelize_1.DataTypes.ENUM,
        values: Object.values(item_category),
        defaultValue: item_category.ITEM,
    },
    item_status: {
        type: sequelize_1.DataTypes.ENUM,
        values: Object.values(item_status),
        defaultValue: item_status.ACTIVE,
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "nv_items",
    modelName: "Item",
    timestamps: true,
});
// Define associations
Item.associate = (models) => {
    // Item has many Recipes (recipe_placeholder)
    Item.hasMany(models.Recipe, {
        foreignKey: "recipe_placeholder",
        as: "recipePlaceholders",
    });
    // Item has many RecipeSteps (item_id)
    Item.hasMany(models.RecipeSteps, {
        foreignKey: "item_id",
        as: "recipeSteps",
    });
    // Item has many RecipeResources (item_id)
    Item.hasMany(models.RecipeResources, {
        foreignKey: "item_id",
        as: "recipeResources",
    });
    // Item has many Categories (category_icon)
    Item.hasMany(models.Category, {
        foreignKey: "category_icon",
        as: "categoryIcons",
    });
    // Item has many FoodAttributes (attribute_icon)
    Item.hasMany(models.FoodAttributes, {
        foreignKey: "attribute_icon",
        as: "attributeIcons",
    });
    // Item has many RecipeMeasures (unit_icon)
    Item.hasMany(models.RecipeMeasure, {
        foreignKey: "unit_icon",
        as: "unitIcons",
    });
};
exports.default = Item;
