"use strict";
// import fs from "fs";
// import path from "path";
// import { getHash, RECIPE_FILE_UPLOAD_CONSTANT, getMimeTypeFromExtension } from "../helper/common";
// import { db } from "../models/index";
// import { item_type, item_status, item_IEC, item_external_location } from "../models/Item";
// import {
//   S3Client,
//   PutObjectCommand,
//   GetObjectCommand,
// } from "@aws-sdk/client-s3";
// // Setup MinIO client
// const s3 = new S3Client({
//   endpoint: global.config.MINIO_ENDPOINT,
//   region: "us-east-1",
//   forcePathStyle: true,
//   credentials: {
//     accessKeyId: global.config.MINIO_ACCESS_KEY,
//     secretAccessKey: global.config.MINIO_SECRET_KEY,
//   },
// });
// /**
//  * Recipe Category Icon Seeder Service
//  * Maps and uploads icons for recipe categories
//  */
// class RecipeCategoryIconSeederService {
//   private readonly bucketName: string;
//   constructor() {
//     this.bucketName = global.config.MINIO_BUCKET_NAME || process.env.NODE_ENV || "development";
//   }
//   /**
//    * Main method to seed recipe category icons
//    */
//   async seedRecipeCategoryIcons(
//     iconsDirectory: string,
//     iconMapping?: Record<string, string>
//   ): Promise<void> {
//     try {
//       // Get all recipe categories
//       const categories = await db.Category.findAll({
//         where: {
//           category_type: "recipe",
//           is_system_category: true,
//           organization_id: null,
//         },
//         order: [["category_name", "ASC"]],
//       });
//       // Use provided mapping or default mapping
//       const mapping = iconMapping || this.getDefaultIconMapping();
//       for (const category of categories) {
//         const categoryName = category.category_name;
//         const iconFileName = mapping[categoryName];
//         if (iconFileName) {
//           const iconPath = path.join(iconsDirectory, iconFileName);
//           if (fs.existsSync(iconPath)) {
//             const itemId = await this.uploadCategoryIcon(categoryName, iconPath, iconFileName);
//             if (itemId) {
//               // Update category with icon
//               await category.update({ category_icon: itemId });
//             }
//           } else {
//           }
//         } else {
//         }
//       }
//     } catch (error) {
//       console.error("❌ Error in Recipe Category Icon Seeder:", error);
//       throw error;
//     }
//   }
//   /**
//    * Default icon mapping for recipe categories
//    */
//   private getDefaultIconMapping(): Record<string, string> {
//     return {
//       "Appetizer": "Appetizers & Starters Stroke Black.png",
//       "Beverage": "Beverages & Drinks Stroke Black.png",
//       "Breakfast": "breakfast Stroke Black.png",
//       "Dessert": "Dessert cake Stroke Black.png",
//       "Main Course": "Main Courses Stroke Black.png",
//       "Salad": "salad Stroke Black.png",
//       "Snack": "snacks Stroke Black.png",
//       "Soup": "Soups Stroke Black.png",
//       // Additional categories that might be created
//       "Baked Goods": "Baked Goods Stroke Black.png",
//       "Sauces & Dressings": "sauce& Dressings Stroke Black.png", // Note: filename has typo "sauce&" instead of "sauces&"
//       "Side Dishes": "Side Dishes Stroke Black.png",
//     };
//   }
//   /**
//    * Upload category icon to S3 and local storage
//    */
//   private async uploadCategoryIcon(
//     categoryName: string,
//     iconPath: string,
//     fileName: string
//   ): Promise<number | null> {
//     try {
//       // Read file
//       const fileBuffer = fs.readFileSync(iconPath);
//       const fileExtension = path.extname(fileName);
//       const fileStats = fs.statSync(iconPath);
//       // Create file object that matches getHash expectations
//       const fileObject = {
//         path: iconPath,
//         size: fileStats.size,
//         originalname: fileName,
//         filename: fileName
//       };
//       // Generate hash to check for duplicates
//       const fileHashResult = getHash(fileObject);
//       if (!fileHashResult.status) {
//         console.error(`Failed to generate hash for ${categoryName} icon:`, fileHashResult.message);
//         return null;
//       }
//       // Check if item already exists with same hash
//       const existingItem = await db.Item.findOne({
//         where: {
//           item_hash: fileHashResult.hash,
//           item_organization_id: null,
//         },
//       });
//       if (existingItem) {
//         return existingItem.id;
//       }
//       // Generate file name for storage
//       const storageFileName = `${categoryName.toLowerCase().replace(/\s+/g, '-')}-icon${fileExtension}`;
//       // Use existing CATEGORY_ICON constant for path generation
//       const s3FilePath = RECIPE_FILE_UPLOAD_CONSTANT.CATEGORY_ICON.destinationPath(
//         null, // organization name (null for system defaults)
//         null, // category ID (not needed for file path)
//         storageFileName
//       );
//       // Upload to S3
//       await s3.send(
//         new PutObjectCommand({
//           Bucket: this.bucketName,
//           Key: s3FilePath,
//           Body: fileBuffer,
//           ContentType: fileHashResult.actualMimeType || getMimeTypeFromExtension(fileExtension),
//         })
//       );
//       // Also save file locally to uploads directory for local serving
//       const localFilePath = path.join(process.cwd(), "uploads", s3FilePath);
//       const localDir = path.dirname(localFilePath);
//       // Create directory if it doesn't exist
//       if (!fs.existsSync(localDir)) {
//         fs.mkdirSync(localDir, { recursive: true });
//       }
//       // Write file to local uploads directory
//       fs.writeFileSync(localFilePath, fileBuffer);
//       // Create Item record
//       const itemData = {
//         item_type: item_type.IMAGE,
//         item_name: storageFileName,
//         item_hash: fileHashResult.hash,
//         item_mime_type: fileHashResult.actualMimeType || getMimeTypeFromExtension(fileExtension),
//         item_extension: fileExtension,
//         item_size: fileBuffer.length,
//         item_IEC: item_IEC.B,
//         item_status: item_status.ACTIVE,
//         item_external_location: item_external_location.NO,
//         item_location: s3FilePath,
//         item_organization_id: null,
//         item_category: "category_icon",
//         created_by: 1,
//         updated_by: 1,
//       };
//       const item = await db.Item.create(itemData);
//       return item.id;
//     } catch (error) {
//       console.error(`Error uploading icon for ${categoryName}:`, error);
//       return null;
//     }
//   }
//   /**
//    * List all recipe categories and their current icon status
//    */
//   async listCategoriesWithIconStatus(): Promise<void> {
//     try {
//       const categories = await db.Category.findAll({
//         where: {
//           category_type: "recipe",
//           is_system_category: true,
//           organization_id: null,
//         },
//         order: [["category_name", "ASC"]],
//       });
//       for (let i = 0; i < categories.length; i++) {
//         const category = categories[i];
//         let iconInfo = "(No icon)";
//         let hasIcon = "❌";
//         if (category.category_icon) {
//           try {
//             const iconItem = await db.Item.findByPk(category.category_icon);
//             if (iconItem) {
//               hasIcon = "✅";
//               iconInfo = `(${iconItem.item_name})`;
//             }
//           } catch {
//             // Ignore icon lookup errors
//           }
//         }
//       }
//     } catch (error) {
//       console.error("Error listing recipe categories:", error);
//     }
//   }
// }
// export default new RecipeCategoryIconSeederService();
