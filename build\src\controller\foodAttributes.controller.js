"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_codes_1 = require("http-status-codes");
const sequelize_1 = require("sequelize");
const models_1 = require("../models");
const FoodAttributes_1 = require("../models/FoodAttributes");
const slugGenerator_1 = require("../helper/slugGenerator");
const common_1 = require("../helper/common");
// Get models from db object to ensure associations are set up
const FoodAttributes = models_1.db.FoodAttributes;
const Item = models_1.db.Item;
const RecipeAttributes = models_1.db.RecipeAttributes;
const IngredientAttributes = models_1.db.IngredientAttributes;
const RecipeIngredients = models_1.db.RecipeIngredients;
// Helper function to get the proper base URL
const getBaseUrl = () => {
    var _a;
    const baseUrl = (_a = global.config) === null || _a === void 0 ? void 0 : _a.API_BASE_URL;
    if (baseUrl &&
        baseUrl.includes("/backend-api/v1/public/user/get-file?location=")) {
        // API_BASE_URL already contains the full endpoint, return base part
        return baseUrl.replace("/backend-api/v1/public/user/get-file?location=", "");
    }
    else {
        // For development or when API_BASE_URL is just the base domain
        return (process.env.BASE_URL ||
            process.env.FRONTEND_URL ||
            "https://staging.namastevillage.theeasyaccess.com");
    }
};
/**
 * Create a new food attribute (type-wise creation)
 * @route POST /api/v1/private/food-attributes
 * @access Private (Authenticated users)
 */
const createFoodAttribute = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d;
    try {
        const { attribute_title, attribute_description, attribute_status, attribute_type, } = req.body;
        // Check if user has default access (can create system defaults)
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        const organizationIdForCreation = hasDefaultAccess
            ? null
            : (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id;
        // Check for duplicate attribute title (case-insensitive)
        // Check both in same organization AND in default/system records
        const existingAttributeByTitle = yield FoodAttributes.findOne({
            where: {
                [sequelize_1.Op.and]: [
                    models_1.db.sequelize.where(models_1.db.sequelize.fn("LOWER", models_1.db.sequelize.col("attribute_title")), models_1.db.sequelize.fn("LOWER", attribute_title.trim())),
                    { attribute_type: attribute_type },
                    {
                        [sequelize_1.Op.or]: [
                            { organization_id: organizationIdForCreation }, // Same organization
                            { organization_id: null }, // Default/system records
                        ],
                    },
                ],
            },
        });
        if (existingAttributeByTitle) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("ATTRIBUTE_TITLE_ALREADY_EXISTS"),
            });
        }
        // Generate unique slug from attribute title
        const checkSlugExists = (slug) => __awaiter(void 0, void 0, void 0, function* () {
            const existing = yield FoodAttributes.findOne({
                where: {
                    attribute_slug: slug,
                    organization_id: organizationIdForCreation,
                    attribute_type: attribute_type,
                },
            });
            return !!existing;
        });
        const attributeSlug = yield (0, slugGenerator_1.generateUniqueSlug)(attribute_title, checkSlugExists, {
            maxLength: 25,
            separator: "-",
            lowercase: true,
        });
        // Set system attribute flag based on default access
        const isSystemAttributeFlag = hasDefaultAccess;
        // Create attribute first
        const attribute = yield FoodAttributes.create({
            attribute_title: attribute_title,
            attribute_slug: attributeSlug,
            attribute_description: attribute_description,
            attribute_status: attribute_status || FoodAttributes_1.AttributeStatus.active,
            organization_id: organizationIdForCreation,
            attribute_type: attribute_type,
            is_system_attribute: isSystemAttributeFlag,
            created_by: ((_c = req.user) === null || _c === void 0 ? void 0 : _c.id) || null,
            updated_by: ((_d = req.user) === null || _d === void 0 ? void 0 : _d.id) || null,
        });
        let iconItemId = null;
        // Handle file upload from S3 middleware (req.files format)
        if (req.files &&
            typeof req.files === "object" &&
            !Array.isArray(req.files)) {
            const files = req.files;
            if (files.attributeIcon && files.attributeIcon.length > 0) {
                const uploadedFile = files.attributeIcon[0];
                iconItemId = uploadedFile.item_id;
            }
        }
        // Update attribute with icon item_id if uploaded
        if (iconItemId) {
            yield attribute.update({ attribute_icon: iconItemId });
        }
        return res.status(http_status_codes_1.StatusCodes.CREATED).json({
            status: true,
            message: res.__("ATTRIBUTE_CREATED_SUCCESSFULLY"),
        });
    }
    catch (error) {
        console.log("Error in foodAttributes.controller.ts - createFoodAttribute:", error);
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            error: process.env.NODE_ENV === "development" ? error : undefined,
        });
    }
});
/**
 * Get all food attributes by type with advanced filtering and search
 * @route GET /api/v1/private/food-attributes
 * @access Private (Authenticated users)
 *
 * Query Parameters:
 * - page: Page number (default: 1)
 * - limit: Items per page (default: 10)
 * - search: Search in title and description
 * - status: Filter by attribute status (active, inactive)
 * - type: Filter by attribute type (nutrition, allergen, cuisine, dietary)
 * - isSystemAttribute: Filter by system attributes (true/false) - Admin only
 * - organizationId: Filter by organization ID - Admin only
 * - sort: Sort field (default: attribute_title)
 * - order: Sort order (ASC/DESC, default: ASC)
 */
const getAllFoodAttributesByType = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { page, limit, search = "", status, type, isSystemAttribute, organizationId, sort = "attribute_title", order = "ASC", } = req.query;
        // Check if user has default access
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        // Build base where clause with organization isolation
        let whereClause = {};
        const searchConditions = [];
        // Organization-based access control
        if (hasDefaultAccess) {
            // Admin users can see all records, but can filter by organization
            if (organizationId !== undefined) {
                if (organizationId === "null" || organizationId === "") {
                    whereClause.organization_id = null; // System defaults
                }
                else {
                    whereClause.organization_id = organizationId;
                }
            }
        }
        else {
            // Regular users see their org records + system defaults
            whereClause = {
                [sequelize_1.Op.or]: [
                    { organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id },
                    { organization_id: null }, // System defaults
                    { is_system_attribute: true }, // System attributes
                ],
            };
        }
        // Filter by attribute type (nutrition, allergen, cuisine, dietary)
        if (type) {
            whereClause.attribute_type = type;
        }
        // Filter by status (active, inactive)
        if (status) {
            whereClause.attribute_status = status;
        }
        // Filter by system attribute flag - accessible by all users
        if (isSystemAttribute !== undefined) {
            if (isSystemAttribute === "true") {
                whereClause.is_system_attribute = true;
            }
            else if (isSystemAttribute === "false") {
                whereClause.is_system_attribute = false;
            }
            // If isSystemAttribute is neither "true" nor "false", ignore the filter
        }
        // Search functionality (searches in title and description)
        if (search) {
            searchConditions.push({
                [sequelize_1.Op.or]: [
                    { attribute_title: { [sequelize_1.Op.like]: `%${search}%` } },
                    { attribute_description: { [sequelize_1.Op.like]: `%${search}%` } },
                ],
            });
        }
        // Combine search conditions with where clause
        if (searchConditions.length > 0) {
            if (Object.keys(whereClause).length > 0) {
                whereClause = {
                    [sequelize_1.Op.and]: [whereClause, ...searchConditions],
                };
            }
            else {
                whereClause = {
                    [sequelize_1.Op.and]: searchConditions,
                };
            }
        }
        // Handle pagination - if limit is not provided, show all records
        const pageNumber = page ? Number(page) : 1;
        const limitNumber = limit ? Number(limit) : null; // null means no limit
        const offset = limitNumber ? (pageNumber - 1) * limitNumber : 0;
        // Validate sort field to prevent SQL injection
        const allowedSortFields = [
            "attribute_title",
            "attribute_description",
            "attribute_status",
            "attribute_type",
            "is_system_attribute",
            "organization_id",
            "created_at",
            "updated_at",
            "created_by",
            "updated_by",
        ];
        const sortField = allowedSortFields.includes(sort)
            ? sort
            : "attribute_title";
        const sortOrder = order.toUpperCase() === "DESC" ? "DESC" : "ASC";
        // Build query options - conditionally add limit and offset
        const queryOptions = {
            where: whereClause,
            include: [
                {
                    model: Item,
                    as: "iconItem",
                    attributes: [
                        "id",
                        "item_location",
                        // Add computed iconUrl field at database level
                        [
                            models_1.sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN CONCAT('${getBaseUrl()}/backend-api/v1/public/user/get-file?location=', iconItem.item_location)
                ELSE NULL
              END`),
                            "iconUrl",
                        ],
                        // Add computed hasIcon field at database level
                        [
                            models_1.sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN true
                ELSE false
              END`),
                            "hasIcon",
                        ],
                    ],
                    required: false, // LEFT JOIN to include attributes without icons
                },
            ],
            order: [[sortField, sortOrder]],
            raw: false,
            nest: true,
        };
        // Only add limit and offset if limit is provided
        if (limitNumber) {
            queryOptions.limit = limitNumber;
            queryOptions.offset = offset;
        }
        // Fetch attributes with conditional pagination
        const { rows: attributes, count } = yield FoodAttributes.findAndCountAll(queryOptions);
        // Add user names to the attributes
        const attributesWithUserNames = yield Promise.all(attributes.map((attribute) => __awaiter(void 0, void 0, void 0, function* () {
            const attributeData = attribute.toJSON ? attribute.toJSON() : attribute;
            // Add created_by and updated_by user names
            if (attributeData.created_by) {
                attributeData.created_by_name = yield (0, common_1.getUserFullName)(attributeData.created_by);
            }
            if (attributeData.updated_by) {
                attributeData.updated_by_name = yield (0, common_1.getUserFullName)(attributeData.updated_by);
            }
            return attributeData;
        })));
        // Calculate pagination info only if limit is provided
        let paginationInfo = {
            count: count,
            data: attributesWithUserNames,
        };
        if (limitNumber) {
            const { total_pages } = (0, common_1.getPaginatedItems)(limitNumber, pageNumber, count || 0);
            paginationInfo = Object.assign(Object.assign({}, paginationInfo), { page: pageNumber, limit: limitNumber, total_pages: total_pages });
        }
        else {
            // When no limit is provided, show all records info
            paginationInfo = Object.assign(Object.assign({}, paginationInfo), { page: 1, limit: "all", total_pages: 1, showing_all_records: true });
        }
        return res.status(http_status_codes_1.StatusCodes.OK).json(Object.assign({ status: true, message: res.__("SUCCESS_DATA_FETCHED") }, paginationInfo));
    }
    catch (error) {
        console.log("Error in foodAttributes.controller.ts - getAllFoodAttributesByType:", error);
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            error: process.env.NODE_ENV === "development" ? error : undefined,
        });
    }
});
/**
 * Get single food attribute by ID and type
 * @route GET /api/v1/private/food-attributes/:id
 * @access Private (Authenticated users)
 */
const getFoodAttributeById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { id } = req.params;
        const { type } = req.query;
        // Check if user has default access
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        let whereClause = { id };
        if (!hasDefaultAccess) {
            // Regular users can only see their org records + system defaults
            whereClause = {
                id,
                [sequelize_1.Op.or]: [
                    { organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id },
                    { organization_id: null }, // System defaults
                    { is_system_attribute: true }, // System attributes
                ],
            };
        }
        // Add type filter if provided
        if (type) {
            whereClause.attribute_type = type;
        }
        const attribute = yield FoodAttributes.findOne({
            where: whereClause,
            include: [
                {
                    model: Item,
                    as: "iconItem",
                    attributes: [
                        "id",
                        "item_name",
                        "item_location",
                        "item_mime_type",
                        // Add computed iconUrl field at database level
                        [
                            models_1.sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN CONCAT('${getBaseUrl()}/backend-api/v1/public/user/get-file?location=', iconItem.item_location)
                ELSE NULL
              END`),
                            "iconUrl",
                        ],
                        // Add computed hasIcon field at database level
                        [
                            models_1.sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN true
                ELSE false
              END`),
                            "hasIcon",
                        ],
                    ],
                    required: false, // LEFT JOIN to include attribute even without icon
                },
            ],
            raw: false,
            nest: true,
        });
        if (!attribute) {
            return res.status(http_status_codes_1.StatusCodes.OK).json({
                status: true,
                message: res.__("ATTRIBUTE_NOT_FOUND"),
                data: {},
            });
        }
        // Add user names to the attribute
        const attributeData = attribute.toJSON ? attribute.toJSON() : attribute;
        // Add created_by and updated_by user names
        if (attributeData.created_by) {
            attributeData.created_by_name = yield (0, common_1.getUserFullName)(attributeData.created_by);
        }
        if (attributeData.updated_by) {
            attributeData.updated_by_name = yield (0, common_1.getUserFullName)(attributeData.updated_by);
        }
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_DATA_FETCHED"),
            data: attributeData,
        });
    }
    catch (error) {
        console.log("Error in foodAttributes.controller.ts - getFoodAttributeById:", error);
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            error: process.env.NODE_ENV === "development" ? error : undefined,
        });
    }
});
/**
 * Update food attribute by ID
 * @route PUT /api/v1/private/food-attributes/:id
 * @access Private (Authenticated users)
 */
const updateFoodAttribute = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d, _e;
    try {
        const { id } = req.params;
        const { attribute_title, attribute_description, attribute_status, attribute_type, is_system_attribute, } = req.body;
        // Check if user has default access
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        // Build where clause based on user access
        let whereClause = { id };
        if (!hasDefaultAccess) {
            // Regular users can only update their org records
            whereClause = {
                id,
                organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id,
            };
        }
        else {
            // Super admin can update any record
            whereClause = { id };
        }
        // Find the attribute
        const attribute = yield FoodAttributes.findOne({
            where: whereClause,
        });
        if (!attribute) {
            return res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("ATTRIBUTE_NOT_FOUND"),
            });
        }
        // Prevent regular users from updating system default records
        if (attribute.organization_id === null && !hasDefaultAccess) {
            return res.status(http_status_codes_1.StatusCodes.FORBIDDEN).json({
                status: false,
                message: res.__("CANNOT_UPDATE_SYSTEM_DEFAULT"),
            });
        }
        if (attribute_title &&
            attribute_title.trim() !== attribute.attribute_title) {
            // For system defaults, check globally; for org records, check within org
            const duplicateCheckOrgId = hasDefaultAccess && attribute.organization_id === null
                ? null
                : (_c = req.user) === null || _c === void 0 ? void 0 : _c.organization_id;
            // Check both in same organization AND in default/system records
            const existingAttributeByTitle = yield FoodAttributes.findOne({
                where: {
                    [sequelize_1.Op.and]: [
                        models_1.db.sequelize.where(models_1.db.sequelize.fn("LOWER", models_1.db.sequelize.col("attribute_title")), models_1.db.sequelize.fn("LOWER", attribute_title.trim())),
                        { attribute_type: attribute_type || attribute.attribute_type },
                        {
                            [sequelize_1.Op.or]: [
                                { organization_id: (_d = req.user) === null || _d === void 0 ? void 0 : _d.organization_id }, // Same organization
                                { organization_id: null }, // Default/system records
                            ],
                        },
                        { id: { [sequelize_1.Op.ne]: id } }, // Exclude current attribute
                    ],
                },
            });
            if (existingAttributeByTitle) {
                return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                    status: false,
                    message: res.__("ATTRIBUTE_TITLE_ALREADY_EXISTS"),
                });
            }
        }
        // Generate new slug if attribute title is being updated
        let newSlug = attribute.attribute_slug;
        if (attribute_title && attribute_title !== attribute.attribute_title) {
            const checkSlugExists = (slug) => __awaiter(void 0, void 0, void 0, function* () {
                var _a;
                // For system defaults, check globally; for org records, check within org
                const slugCheckOrgId = hasDefaultAccess && attribute.organization_id === null
                    ? null
                    : (_a = req.user) === null || _a === void 0 ? void 0 : _a.organization_id;
                const existing = yield FoodAttributes.findOne({
                    where: {
                        attribute_slug: slug,
                        organization_id: slugCheckOrgId,
                        attribute_type: attribute_type || attribute.attribute_type,
                        id: { [sequelize_1.Op.ne]: id },
                    },
                });
                return !!existing;
            });
            newSlug = yield (0, slugGenerator_1.generateUniqueSlug)(attribute_title, checkSlugExists, {
                maxLength: 25,
                separator: "-",
                lowercase: true,
            });
        }
        let newIconItemId = attribute.attribute_icon;
        let iconWasUpdated = false;
        // Handle file upload from S3 middleware (req.files format)
        if (req.files &&
            typeof req.files === "object" &&
            !Array.isArray(req.files)) {
            const files = req.files;
            if (files.attributeIcon && files.attributeIcon.length > 0) {
                const uploadedFile = files.attributeIcon[0];
                newIconItemId = uploadedFile.item_id;
                iconWasUpdated = true;
            }
            else if (files.attributeIcon && files.attributeIcon.length === 0) {
                // Empty file array means user wants to remove the icon
                newIconItemId = null;
                iconWasUpdated = true;
            }
        }
        else if (req.body.attributeIcon === "" ||
            req.body.attributeIcon === null) {
            // Handle explicit removal of icon via form data
            newIconItemId = null;
            iconWasUpdated = true;
        }
        // Update the attribute
        yield FoodAttributes.update({
            attribute_title: attribute_title || attribute.attribute_title,
            attribute_slug: newSlug,
            attribute_description: attribute_description !== undefined
                ? attribute_description
                : attribute.attribute_description,
            attribute_icon: newIconItemId,
            attribute_status: attribute_status || attribute.attribute_status,
            attribute_type: attribute_type || attribute.attribute_type,
            is_system_attribute: is_system_attribute !== undefined
                ? is_system_attribute
                : attribute.is_system_attribute,
            updated_by: ((_e = req.user) === null || _e === void 0 ? void 0 : _e.id) || null,
        }, {
            where: whereClause,
        });
        // Fetch updated attribute using the same where clause
        const updatedAttribute = yield FoodAttributes.findOne({
            where: whereClause,
        });
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("ATTRIBUTE_UPDATED_SUCCESSFULLY"),
        });
    }
    catch (error) {
        console.log("Error in foodAttributes.controller.ts - updateFoodAttribute:", error);
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            error: process.env.NODE_ENV === "development" ? error : undefined,
        });
    }
});
/**
 * Delete food attribute by ID (hard delete if not in use, otherwise show usage message)
 * @route DELETE /api/v1/private/food-attributes/:id
 * @access Private (Authenticated users)
 */
const deleteFoodAttribute = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    const transaction = yield models_1.sequelize.transaction();
    try {
        const { id } = req.params;
        // Check if user has default access
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        // Build where clause based on user access
        let whereClause = { id };
        if (!hasDefaultAccess) {
            // Regular users can only delete their org records
            whereClause = {
                id,
                organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id,
            };
        }
        else {
            // Super admin can delete any record
            whereClause = { id };
        }
        const attribute = yield FoodAttributes.findOne({
            where: whereClause,
            transaction,
        });
        if (!attribute) {
            yield transaction.rollback();
            return res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("ATTRIBUTE_NOT_FOUND"),
            });
        }
        // Prevent deletion of system default records (organization_id: null) by non-default users
        if (attribute.organization_id === null && !hasDefaultAccess) {
            yield transaction.rollback();
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("CANNOT_DELETE_SYSTEM_DEFAULT"),
            });
        }
        // Prevent deletion of system attributes by non-default users
        if (attribute.is_system_attribute && !hasDefaultAccess) {
            yield transaction.rollback();
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("CANNOT_DELETE_SYSTEM_ATTRIBUTE"),
            });
        }
        // Check if attribute is being used in recipes (any status)
        const recipeUsage = yield RecipeAttributes.count({
            where: {
                attributes_id: id,
            },
            transaction,
        });
        // Check if attribute is being used in ingredients (any status)
        const ingredientUsage = yield IngredientAttributes.count({
            where: {
                attributes_id: id,
            },
            transaction,
        });
        // Check if attribute is being used in recipe ingredients as cooking method
        const recipeIngredientCookingUsage = yield RecipeIngredients.count({
            where: {
                ingredient_cooking_method: id,
            },
            transaction,
        });
        // Check if attribute is being used in recipe ingredients as preparation method
        const recipeIngredientPrepUsage = yield RecipeIngredients.count({
            where: {
                preparation_method: id,
            },
            transaction,
        });
        const totalUsage = recipeUsage +
            ingredientUsage +
            recipeIngredientCookingUsage +
            recipeIngredientPrepUsage;
        // If attribute is in use, prevent deletion
        if (totalUsage > 0) {
            yield transaction.rollback();
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("ATTRIBUTE_IN_USE_CANNOT_DELETE"),
            });
        }
        // Hard delete the attribute and all its inactive relations
        yield IngredientAttributes.destroy({
            where: { attributes_id: id },
            transaction,
        });
        yield RecipeAttributes.destroy({
            where: { attributes_id: id },
            transaction,
        });
        yield FoodAttributes.destroy({
            where: whereClause,
            transaction,
        });
        yield transaction.commit();
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("ATTRIBUTE_DELETED_SUCCESSFULLY"),
        });
    }
    catch (error) {
        console.log("Error in foodAttributes.controller.ts - deleteFoodAttribute:", error);
        yield transaction.rollback();
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            error: process.env.NODE_ENV === "development" ? error : undefined,
        });
    }
});
/**
 * Deactivate food attribute by ID
 * @route PUT /api/v1/private/food-attributes/:id/deactivate
 * @access Private (Authenticated users)
 */
const deactivateFoodAttribute = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c;
    const transaction = yield models_1.sequelize.transaction();
    try {
        const { id } = req.params;
        // Check if user has default access
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        // Build where clause based on user access
        let whereClause = { id };
        if (!hasDefaultAccess) {
            // Regular users can only deactivate their org records
            whereClause = {
                id,
                organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id,
            };
        }
        else {
            // Super admin can deactivate any record
            whereClause = { id };
        }
        const attribute = yield FoodAttributes.findOne({
            where: whereClause,
            transaction,
        });
        if (!attribute) {
            yield transaction.rollback();
            return res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("ATTRIBUTE_NOT_FOUND"),
            });
        }
        // Prevent deactivation of system default records by non-default users
        if (attribute.organization_id === null && !hasDefaultAccess) {
            yield transaction.rollback();
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("CANNOT_UPDATE_SYSTEM_DEFAULT"),
            });
        }
        // Deactivate the attribute
        yield FoodAttributes.update({
            attribute_status: FoodAttributes_1.AttributeStatus.inactive,
            updated_by: ((_c = req.user) === null || _c === void 0 ? void 0 : _c.id) || null,
        }, {
            where: whereClause,
            transaction,
        });
        yield transaction.commit();
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("ATTRIBUTE_DEACTIVATED_SUCCESSFULLY"),
        });
    }
    catch (error) {
        console.log("Error in foodAttributes.controller.ts - deactivateFoodAttribute:", error);
        yield transaction.rollback();
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            error: process.env.NODE_ENV === "development" ? error : undefined,
        });
    }
});
/**
 * Reactivate food attribute by ID
 * @route PUT /api/v1/private/food-attributes/:id/reactivate
 * @access Private (Authenticated users)
 */
const reactivateFoodAttribute = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c;
    const transaction = yield models_1.sequelize.transaction();
    try {
        const { id } = req.params;
        // Check if user has default access
        const hasDefaultAccess = yield (0, common_1.isDefaultAccess)((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        // Build where clause based on user access
        let whereClause = { id };
        if (!hasDefaultAccess) {
            // Regular users can only reactivate their org records
            whereClause = {
                id,
                organization_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id,
            };
        }
        else {
            // Super admin can reactivate any record
            whereClause = { id };
        }
        const attribute = yield FoodAttributes.findOne({
            where: whereClause,
            transaction,
        });
        if (!attribute) {
            yield transaction.rollback();
            return res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("ATTRIBUTE_NOT_FOUND"),
            });
        }
        // Prevent reactivation of system default records by non-default users
        if (attribute.organization_id === null && !hasDefaultAccess) {
            yield transaction.rollback();
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("CANNOT_UPDATE_SYSTEM_DEFAULT"),
            });
        }
        // Reactivate the attribute
        yield FoodAttributes.update({
            attribute_status: FoodAttributes_1.AttributeStatus.active,
            updated_by: ((_c = req.user) === null || _c === void 0 ? void 0 : _c.id) || null,
        }, {
            where: whereClause,
            transaction,
        });
        yield transaction.commit();
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("ATTRIBUTE_REACTIVATED_SUCCESSFULLY"),
        });
    }
    catch (error) {
        console.log("Error in foodAttributes.controller.ts - reactivateFoodAttribute:", error);
        yield transaction.rollback();
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            error: process.env.NODE_ENV === "development" ? error : undefined,
        });
    }
});
// Default export object
exports.default = {
    createFoodAttribute,
    getAllFoodAttributesByType,
    getFoodAttributeById,
    updateFoodAttribute,
    deleteFoodAttribute,
    deactivateFoodAttribute,
    reactivateFoodAttribute,
};
