"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FoodAttributes = exports.AttributeType = exports.AttributeStatus = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var AttributeStatus;
(function (AttributeStatus) {
    AttributeStatus["active"] = "active";
    AttributeStatus["inactive"] = "inactive";
})(AttributeStatus || (exports.AttributeStatus = AttributeStatus = {}));
var AttributeType;
(function (AttributeType) {
    AttributeType["nutrition"] = "nutrition";
    AttributeType["allergen"] = "allergen";
    AttributeType["cuisine"] = "cuisine";
    AttributeType["dietary"] = "dietary";
    AttributeType["ingredient_cooking_method"] = "ingredient_cooking_method";
    AttributeType["haccp_category"] = "haccp_category";
    AttributeType["preparation_method"] = "preparation_method";
})(AttributeType || (exports.AttributeType = AttributeType = {}));
class FoodAttributes extends sequelize_1.Model {
}
exports.FoodAttributes = FoodAttributes;
FoodAttributes.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    attribute_title: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
    },
    attribute_slug: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
    },
    attribute_description: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    attribute_icon: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        comment: "Foreign key reference to nv_items table for attribute icon",
        references: {
            model: "nv_items",
            key: "id",
        },
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
    },
    attribute_status: {
        type: sequelize_1.DataTypes.ENUM(Object.values(AttributeStatus)),
        allowNull: false,
        defaultValue: AttributeStatus.active,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    attribute_type: {
        type: sequelize_1.DataTypes.ENUM(Object.values(AttributeType)),
        allowNull: false,
    },
    is_system_attribute: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_food_attributes",
    modelName: "FoodAttributes",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
        {
            unique: true,
            fields: ["attribute_slug", "organization_id", "attribute_type"],
            name: "unique_attribute_slug_per_org_type",
        },
        {
            fields: ["organization_id"],
            name: "idx_attribute_organization",
        },
        {
            fields: ["attribute_type"],
            name: "idx_attribute_type",
        },
        {
            fields: ["attribute_status"],
            name: "idx_attribute_status",
        },
        {
            fields: ["created_by"],
            name: "idx_attribute_created_by",
        },
        {
            fields: ["updated_by"],
            name: "idx_attribute_updated_by",
        },
    ],
});
// Define associations
FoodAttributes.associate = (models) => {
    // FoodAttributes belongs to User (created_by)
    FoodAttributes.belongsTo(models.User, {
        foreignKey: "created_by",
        as: "creator",
    });
    // FoodAttributes belongs to User (updated_by)
    FoodAttributes.belongsTo(models.User, {
        foreignKey: "updated_by",
        as: "updater",
    });
    FoodAttributes.belongsTo(models.Item, {
        foreignKey: "attribute_icon",
        as: "iconItem",
        constraints: true, //
        onDelete: "SET NULL", //
        onUpdate: "CASCADE", //
    });
    // Many-to-many association with Ingredient through IngredientAttributes
    FoodAttributes.belongsToMany(models.Ingredient, {
        through: models.IngredientAttributes,
        foreignKey: "attributes_id",
        otherKey: "ingredient_id",
        as: "ingredients",
    });
    // Many-to-many association with Recipe through RecipeAttributes
    FoodAttributes.belongsToMany(models.Recipe, {
        through: models.RecipeAttributes,
        foreignKey: "attributes_id",
        otherKey: "recipe_id",
        as: "recipes",
    });
};
exports.default = FoodAttributes;
