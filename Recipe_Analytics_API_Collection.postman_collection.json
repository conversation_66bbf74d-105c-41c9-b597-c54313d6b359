{"info": {"_postman_id": "recipe-analytics-api-collection", "name": "Recipe Analytics API - Complete Collection", "description": "Complete API collection for Recipe Analytics microservice with all endpoints, filters, and parameters", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "recipe-analytics-api"}, "item": [{"name": "Public Analytics APIs", "item": [{"name": "Track Recipe View", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": 123,\n  \"recipe_name\": \"Chocolate Cake\"\n}"}, "url": {"raw": "{{base_url}}/v1/public/analytics/track/recipe-view", "host": ["{{base_url}}"], "path": ["v1", "public", "analytics", "track", "recipe-view"]}, "description": "Track recipe views on public recipes. Only requires recipe_id (required) and optional recipe_name."}}, {"name": "Track CTA Click", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": 123,\n  \"recipe_name\": \"Chocolate Cake\",\n  \"cta_type\": \"contact_form\",\n  \"cta_text\": \"Get Recipe Details\"\n}"}, "url": {"raw": "{{base_url}}/v1/public/analytics/track/cta-click", "host": ["{{base_url}}"], "path": ["v1", "public", "analytics", "track", "cta-click"]}, "description": "Track CTA clicks on public recipes. Requires recipe_id and cta_type. Valid cta_type values: contact_info, contact_form, custom_cta"}}, {"name": "Submit Contact Form", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": 123,\n  \"recipe_name\": \"Chocolate Cake\",\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"mobile\": \"+1234567890\",\n  \"message\": \"I want the complete recipe for this chocolate cake!\"\n}"}, "url": {"raw": "{{base_url}}/v1/public/analytics/contact-form", "host": ["{{base_url}}"], "path": ["v1", "public", "analytics", "contact-form"]}, "description": "Submit contact form from public recipes. Required: recipe_id, name, email, message. Optional: recipe_name, mobile"}}], "description": "Public analytics endpoints that don't require authentication. Used for tracking user interactions on public recipe pages."}, {"name": "Private Analytics APIs", "item": [{"name": "Get Analytics Summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/analytics?page=1&limit=20&event_type=recipe_view&entity_type=recipe&start_date=2024-01-01&end_date=2024-12-31", "host": ["{{base_url}}"], "path": ["v1", "private", "analytics"], "query": [{"key": "page", "value": "1", "description": "Page number (min: 1)"}, {"key": "limit", "value": "20", "description": "Items per page (min: 1, max: 50)"}, {"key": "event_type", "value": "recipe_view", "description": "Filter by event type: recipe_view, cta_click, contact_form_submit"}, {"key": "entity_type", "value": "recipe", "description": "Filter by entity type: recipe"}, {"key": "entity_id", "value": "123", "description": "Filter by specific entity ID", "disabled": true}, {"key": "start_date", "value": "2024-01-01", "description": "Start date (ISO format)"}, {"key": "end_date", "value": "2024-12-31", "description": "End date (ISO format)"}]}, "description": "Get analytics summary with filtering and pagination. All query parameters are optional."}}]}], "variable": [{"key": "base_url", "value": "http://localhost:3000/api", "type": "string"}, {"key": "auth_token", "value": "your_jwt_token_here", "type": "string"}]}