"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Ingredient = exports.IngredientStatus = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var IngredientStatus;
(function (IngredientStatus) {
    IngredientStatus["active"] = "active";
    IngredientStatus["inactive"] = "inactive";
})(IngredientStatus || (exports.IngredientStatus = IngredientStatus = {}));
class Ingredient extends sequelize_1.Model {
}
exports.Ingredient = Ingredient;
Ingredient.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    ingredient_name: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
    },
    ingredient_slug: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
    },
    ingredient_description: {
        type: sequelize_1.DataTypes.TEXT('long'),
        allowNull: true,
    },
    ingredient_status: {
        type: sequelize_1.DataTypes.ENUM,
        values: Object.values(IngredientStatus),
        defaultValue: IngredientStatus.active,
    },
    waste_percentage: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
    },
    unit_of_measure: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
    },
    cost_per_unit: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: false,
    },
    cost_last_updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        comment: "Timestamp when cost_per_unit was last updated",
    },
    nutrition_last_updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        comment: "Timestamp when nutrition attributes were last updated",
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_ingredients",
    modelName: "Ingredient",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
});
// Define associations
Ingredient.associate = (models) => {
    // User associations
    Ingredient.belongsTo(models.User, {
        foreignKey: "created_by",
        as: "creator",
    });
    Ingredient.belongsTo(models.User, {
        foreignKey: "updated_by",
        as: "updater",
    });
    // Unit of measure association
    Ingredient.belongsTo(models.RecipeMeasure, {
        foreignKey: "unit_of_measure",
        as: "unit",
    });
    // Many-to-many association with Category through IngredientCategory
    Ingredient.belongsToMany(models.Category, {
        through: models.IngredientCategory,
        foreignKey: "ingredient_id",
        otherKey: "category_id",
        as: "categories"
    });
    // Many-to-many association with FoodAttributes through IngredientAttributes
    Ingredient.belongsToMany(models.FoodAttributes, {
        through: models.IngredientAttributes,
        foreignKey: "ingredient_id",
        otherKey: "attributes_id",
        as: "attributes"
    });
    // Many-to-many association with Recipe through RecipeIngredients
    Ingredient.belongsToMany(models.Recipe, {
        through: models.RecipeIngredients,
        foreignKey: "ingredient_id",
        otherKey: "recipe_id",
        as: "recipes"
    });
    // One-to-many association with IngredientConversion
    Ingredient.hasMany(models.IngredientConversion, {
        foreignKey: "ingredient_id",
        as: "conversions"
    });
};
exports.default = Ingredient;
