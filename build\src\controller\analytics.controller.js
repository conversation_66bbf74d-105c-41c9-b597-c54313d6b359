"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_codes_1 = require("http-status-codes");
const analytics_service_1 = __importDefault(require("../services/analytics.service"));
const Analytics_1 = __importStar(require("../models/Analytics"));
const validation_helper_1 = require("../helper/validation.helper");
const transaction_helper_1 = require("../helper/transaction.helper");
/**
 * Track CTA clicks on public recipes
 * @route POST /api/v1/public/analytics/track/cta-click
 */
const trackCtaClick = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c;
    try {
        // Input validation and sanitization
        const sanitizedBody = validation_helper_1.ValidationHelper.sanitizeInput(req.body);
        const { recipe_id, organization_id: bodyOrgId, recipe_name, cta_type, cta_text, } = sanitizedBody;
        // Get organization_id from user context or request body
        const organization_id = ((_a = req.user) === null || _a === void 0 ? void 0 : _a.organization_id) || bodyOrgId;
        // Validate required fields
        if (!recipe_id) {
            return res.status(400).json({
                status: false,
                message: "recipe_id is required",
                missing_fields: {
                    recipe_id: !recipe_id,
                },
            });
        }
        // For public endpoints, organization_id can come from body if no user context
        if (!organization_id) {
            return res.status(400).json({
                status: false,
                message: "organization_id is required (provide in request body for public access)",
                received_data: {
                    user_org_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id,
                    body_org_id: bodyOrgId,
                    has_user: !!req.user,
                },
            });
        }
        // Convert recipe_id to number if it's a string
        const recipeIdNumber = typeof recipe_id === "string" ? parseInt(recipe_id) : recipe_id;
        // Validate data types
        if (typeof recipeIdNumber !== "number" ||
            isNaN(recipeIdNumber) ||
            recipeIdNumber <= 0) {
            return res.status(400).json({
                status: false,
                message: "recipe_id must be a positive number",
                received_data: {
                    recipe_id: recipe_id,
                    recipe_id_type: typeof recipe_id,
                    parsed_recipe_id: recipeIdNumber,
                    is_valid_number: !isNaN(recipeIdNumber) && recipeIdNumber > 0,
                },
            });
        }
        // Use existing trackEvent method
        yield analytics_service_1.default.trackEvent({
            eventType: Analytics_1.AnalyticsEventType.CTA_CLICK,
            entityType: Analytics_1.AnalyticsEntityType.RECIPE,
            entityId: recipeIdNumber,
            organizationId: organization_id,
            userId: (_c = req.user) === null || _c === void 0 ? void 0 : _c.id,
            ipAddress: req.ip,
            userAgent: req.get("User-Agent"),
            metadata: {
                recipe_name,
                cta_type,
                cta_text,
                tracking_source: req.user ? "authenticated" : "public",
            },
        });
        return res.status(http_status_codes_1.StatusCodes.CREATED).json({
            status: true,
            message: "CTA click tracked successfully",
        });
    }
    catch (error) {
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error tracking CTA click");
    }
});
/**
 * Submit contact form from public recipes
 * @route POST /api/v1/public/analytics/contact-form
 */
const submitContactForm = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c;
    try {
        // Input validation and sanitization
        const sanitizedBody = validation_helper_1.ValidationHelper.sanitizeInput(req.body);
        const { recipe_id, organization_id: bodyOrgId, recipe_name, name, email, mobile, message, } = sanitizedBody;
        // Get organization_id from user context or request body
        const organization_id = ((_a = req.user) === null || _a === void 0 ? void 0 : _a.organization_id) || bodyOrgId;
        // Validate required fields
        if (!recipe_id || !name || !email) {
            return res.status(400).json({
                status: false,
                message: "recipe_id, name, and email are required",
                missing_fields: {
                    recipe_id: !recipe_id,
                    name: !name,
                    email: !email,
                },
            });
        }
        // For public endpoints, organization_id can come from body if no user context
        if (!organization_id) {
            return res.status(400).json({
                status: false,
                message: "organization_id is required",
                received_data: {
                    user_org_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id,
                    body_org_id: bodyOrgId,
                    has_user: !!req.user,
                },
            });
        }
        // Convert recipe_id to number if it's a string
        const recipeIdNumber = typeof recipe_id === "string" ? parseInt(recipe_id) : recipe_id;
        // Validate recipe_id data type
        if (typeof recipeIdNumber !== "number" ||
            isNaN(recipeIdNumber) ||
            recipeIdNumber <= 0) {
            return res.status(400).json({
                status: false,
                message: "recipe_id must be a positive number",
            });
        }
        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                status: false,
                message: "Invalid email format",
            });
        }
        // Use existing trackEvent method
        yield analytics_service_1.default.trackEvent({
            eventType: Analytics_1.AnalyticsEventType.CONTACT_FORM_SUBMIT,
            entityType: Analytics_1.AnalyticsEntityType.RECIPE,
            entityId: recipeIdNumber,
            organizationId: organization_id,
            userId: (_c = req.user) === null || _c === void 0 ? void 0 : _c.id,
            ipAddress: req.ip,
            userAgent: req.get("User-Agent"),
            metadata: {
                recipe_name,
                contact_name: name,
                contact_email: email,
                contact_mobile: mobile,
                message,
                tracking_source: req.user ? "authenticated" : "public",
            },
        });
        return res.status(http_status_codes_1.StatusCodes.CREATED).json({
            status: true,
            message: "Contact form submitted successfully",
        });
    }
    catch (error) {
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error submitting contact form");
    }
});
/**
 * Track recipe views on public recipes
 * @route POST /v1/public/analytics/track/recipe-view
 */
const trackRecipeView = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c;
    try {
        // Input validation and sanitization
        const sanitizedBody = validation_helper_1.ValidationHelper.sanitizeInput(req.body);
        const { recipe_id, organization_id: bodyOrgId, recipe_name, referrer, event_type = "recipe_view", // Default to recipe_view, but allow bookmark/share
        share_platform, } = sanitizedBody;
        // Get organization_id from user context or request body
        const organization_id = ((_a = req.user) === null || _a === void 0 ? void 0 : _a.organization_id) || bodyOrgId;
        // Validate required fields
        if (!recipe_id) {
            return res.status(400).json({
                status: false,
                message: "recipe_id is required",
                missing_fields: {
                    recipe_id: !recipe_id,
                },
            });
        }
        // For public endpoints, organization_id can come from body if no user context
        if (!organization_id) {
            return res.status(400).json({
                status: false,
                message: "organization_id is required (provide in request body for public access)",
                received_data: {
                    user_org_id: (_b = req.user) === null || _b === void 0 ? void 0 : _b.organization_id,
                    body_org_id: bodyOrgId,
                    has_user: !!req.user,
                },
            });
        }
        // Convert recipe_id to number if it's a string
        const recipeIdNumber = typeof recipe_id === "string" ? parseInt(recipe_id) : recipe_id;
        // Validate data types
        if (typeof recipeIdNumber !== "number" ||
            isNaN(recipeIdNumber) ||
            recipeIdNumber <= 0) {
            return res.status(400).json({
                status: false,
                message: "recipe_id must be a positive number",
                received_data: {
                    recipe_id: recipe_id,
                    recipe_id_type: typeof recipe_id,
                    parsed_recipe_id: recipeIdNumber,
                    is_valid_number: !isNaN(recipeIdNumber) && recipeIdNumber > 0,
                },
            });
        }
        // Determine event type based on the request
        let analyticsEventType = Analytics_1.AnalyticsEventType.RECIPE_VIEW;
        const metadata = {
            recipe_name,
            tracking_source: req.user ? "authenticated" : "public",
        };
        // Handle different event types
        switch (event_type) {
            case "recipe_bookmark":
                analyticsEventType = Analytics_1.AnalyticsEventType.RECIPE_BOOKMARK;
                metadata.bookmark_action = "add";
                break;
            case "recipe_share":
                analyticsEventType = Analytics_1.AnalyticsEventType.RECIPE_SHARE;
                metadata.share_platform = share_platform || "unknown";
                break;
            default:
                analyticsEventType = Analytics_1.AnalyticsEventType.RECIPE_VIEW;
                metadata.referrer = referrer;
                metadata.timestamp = new Date().toISOString();
                break;
        }
        // Use existing trackEvent method
        yield analytics_service_1.default.trackEvent({
            eventType: analyticsEventType,
            entityType: Analytics_1.AnalyticsEntityType.RECIPE,
            entityId: recipeIdNumber,
            organizationId: organization_id,
            userId: (_c = req.user) === null || _c === void 0 ? void 0 : _c.id,
            ipAddress: req.ip,
            userAgent: req.get("User-Agent"),
            metadata: metadata,
        });
        const messages = {
            recipe_view: "Recipe view tracked successfully",
            recipe_bookmark: "Recipe bookmark tracked successfully",
            recipe_share: "Recipe share tracked successfully",
        };
        return res.status(http_status_codes_1.StatusCodes.CREATED).json({
            status: true,
            message: messages[event_type] || messages.recipe_view,
        });
    }
    catch (error) {
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error tracking recipe activity");
    }
});
/**
 * Get CTA click analytics with pagination
 * @route GET /api/v1/private/analytics/cta-clicks
 */
const getCtaClickAnalytics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        // Input validation and sanitization
        const sanitizedQuery = validation_helper_1.ValidationHelper.sanitizeInput(req.query);
        const { date_range, start_date, end_date, sort = "desc", sort_by, page, limit, cta_type, recipe_name, search, } = sanitizedQuery;
        // Get effective organization ID (handles admin users)
        const effectiveOrganizationId = yield validation_helper_1.ValidationHelper.getEffectiveOrganizationId(req.user, sanitizedQuery.organization_id);
        // Admin users can access all data, regular users need organization context
        const isAdmin = yield validation_helper_1.ValidationHelper.isAdminUser(req.user);
        if (!isAdmin && !((_a = req.user) === null || _a === void 0 ? void 0 : _a.organization_id)) {
            return res.status(401).json({
                status: false,
                message: "Organization access required",
            });
        }
        // Validate date_range parameter if provided
        if (date_range) {
            const validDateRanges = [
                "last_7_days",
                "last_30_days",
                "last_90_days",
                "custom",
            ];
            if (!validDateRanges.includes(date_range)) {
                return res.status(400).json({
                    status: false,
                    message: "Invalid date_range. Must be one of: " + validDateRanges.join(", "),
                });
            }
        }
        // Parse pagination parameters
        const pageNumber = page ? parseInt(page) : undefined;
        const limitNumber = limit ? parseInt(limit) : undefined;
        // Validate pagination parameters
        if (pageNumber && pageNumber < 1) {
            return res.status(400).json({
                status: false,
                message: "Page number must be greater than 0",
            });
        }
        if (limitNumber && (limitNumber < 1 || limitNumber > 100)) {
            return res.status(400).json({
                status: false,
                message: "Limit must be between 1 and 100",
            });
        }
        const result = yield analytics_service_1.default.getCtaClickAnalytics(effectiveOrganizationId || undefined, date_range, start_date, end_date, pageNumber, limitNumber, cta_type, recipe_name || search, // Use search as fallback for recipe_name
        sort, sort_by);
        // Sort results if no pagination (pagination already handles sorting)
        if (!pageNumber && !limitNumber) {
            if (sort === "asc") {
                result.data.sort((a, b) => a.clicks - b.clicks);
            }
            else {
                result.data.sort((a, b) => b.clicks - a.clicks);
            }
        }
        const response = {
            status: true,
            message: res.__("CTA_ANALYTICS_FETCHED_SUCCESSFULLY"),
            data: result.data,
            meta: {
                date_range: date_range || null,
                start_date: start_date || null,
                end_date: end_date || null,
                total_records: result.total,
            },
        };
        // Add pagination info if pagination was used
        if (result.pagination) {
            response.pagination = result.pagination;
        }
        return res.status(http_status_codes_1.StatusCodes.OK).json(response);
    }
    catch (error) {
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error fetching CTA analytics");
    }
});
/**
 * Get contact form submission analytics with pagination
 * @route GET /api/v1/private/analytics/contact-submissions
 */
const getContactSubmissionAnalytics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Input validation and sanitization
        const sanitizedQuery = validation_helper_1.ValidationHelper.sanitizeInput(req.query);
        const { date_range, start_date, end_date, recipe_id, page, limit, search, sort, sort_by, } = sanitizedQuery;
        // Get effective organization ID (handles admin users)
        const effectiveOrganizationId = yield validation_helper_1.ValidationHelper.getEffectiveOrganizationId(req.user, sanitizedQuery.organization_id);
        // Validate date_range parameter if provided
        if (date_range) {
            const validDateRanges = [
                "last_7_days",
                "last_30_days",
                "last_90_days",
                "custom",
            ];
            if (!validDateRanges.includes(date_range)) {
                return res.status(400).json({
                    status: false,
                    message: "Invalid date_range. Must be one of: " + validDateRanges.join(", "),
                });
            }
        }
        // Parse pagination parameters
        const pageNumber = page ? parseInt(page) : undefined;
        const limitNumber = limit ? parseInt(limit) : undefined;
        // Validate pagination parameters
        if (pageNumber && pageNumber < 1) {
            return res.status(400).json({
                status: false,
                message: "Page number must be greater than 0",
            });
        }
        if (limitNumber && (limitNumber < 1 || limitNumber > 100)) {
            return res.status(400).json({
                status: false,
                message: "Limit must be between 1 and 100",
            });
        }
        const result = yield analytics_service_1.default.getContactSubmissionAnalytics(effectiveOrganizationId || undefined, date_range, start_date, end_date, pageNumber, limitNumber, search, // Unified search parameter only
        sort, sort_by);
        // Filter by recipe if specified (only when no pagination to avoid incorrect counts)
        let filteredData = result.data;
        let filteredTotal = result.total;
        if (recipe_id && !pageNumber && !limitNumber) {
            filteredData = result.data.filter((item) => item.recipe_id === Number(recipe_id));
            filteredTotal = filteredData.length;
        }
        const response = {
            status: true,
            message: res.__("CONTACT_ANALYTICS_FETCHED_SUCCESSFULLY"),
            data: filteredData,
            meta: {
                date_range: date_range || null,
                start_date: start_date || null,
                end_date: end_date || null,
                recipe_filter: recipe_id || null,
                total_records: filteredTotal,
            },
        };
        // Add pagination info if pagination was used
        if (result.pagination) {
            response.pagination = result.pagination;
        }
        return res.status(http_status_codes_1.StatusCodes.OK).json(response);
    }
    catch (error) {
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error fetching contact analytics");
    }
});
/**
 * Get recipe view analytics
 * @route GET /api/v1/private/analytics/recipe-views
 */
const getRecipeViewAnalytics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Input validation and sanitization
        const sanitizedQuery = validation_helper_1.ValidationHelper.sanitizeInput(req.query);
        const { date_range = "last_30_days", start_date, end_date, sort = "desc", } = sanitizedQuery;
        // Get effective organization ID (handles admin users)
        const effectiveOrganizationId = yield validation_helper_1.ValidationHelper.getEffectiveOrganizationId(req.user, sanitizedQuery.organization_id);
        const analytics = yield analytics_service_1.default.getRecipeViewAnalytics(effectiveOrganizationId || undefined, date_range, start_date, end_date);
        // Sort results by total views or recent views
        if (sort === "asc") {
            analytics.sort((a, b) => a.total_views - b.total_views);
        }
        else {
            analytics.sort((a, b) => b.total_views - a.total_views);
        }
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("RECIPE_VIEW_ANALYTICS_FETCHED_SUCCESSFULLY"),
            data: analytics,
            meta: {
                date_range,
                start_date: start_date || null,
                end_date: end_date || null,
                total_records: analytics.length,
                sort_order: sort,
            },
        });
    }
    catch (error) {
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error fetching recipe view analytics");
    }
});
/**
 * Export contact form submissions to CSV or JSON
 * @route GET /api/v1/private/analytics/contact-submissions/export
 */
const exportContactSubmissions = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Input validation and sanitization
        const sanitizedQuery = validation_helper_1.ValidationHelper.sanitizeInput(req.query);
        const { format = "csv", date_range = "last_30_days", start_date, end_date, recipe_name, user_email, search, } = sanitizedQuery;
        // Get effective organization ID (handles admin users)
        const effectiveOrganizationId = yield validation_helper_1.ValidationHelper.getEffectiveOrganizationId(req.user, sanitizedQuery.organization_id);
        // Validate format
        if (!["csv", "json"].includes(format)) {
            return res.status(400).json({
                status: false,
                message: "Invalid format. Supported formats: csv, json",
            });
        }
        // Get all contact submissions without pagination for export
        const result = yield analytics_service_1.default.getContactSubmissionAnalytics(effectiveOrganizationId || undefined, date_range, start_date, end_date, undefined, // no pagination
        undefined, // no limit
        recipe_name || search, user_email || search);
        const timestamp = new Date().toISOString().split("T")[0];
        if (format === "csv") {
            // Convert to CSV format
            const csv = convertContactSubmissionsToCSV(result.data, date_range);
            res.setHeader("Content-Type", "text/csv");
            res.setHeader("Content-Disposition", `attachment; filename=contact-submissions-export-${timestamp}.csv`);
            return res.send(csv);
        }
        // Default JSON export
        res.setHeader("Content-Type", "application/json");
        res.setHeader("Content-Disposition", `attachment; filename=contact-submissions-export-${timestamp}.json`);
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: "Contact submissions exported successfully",
            data: result.data,
            total: result.total,
            exportedAt: new Date().toISOString(),
            dateRange: date_range,
            organizationId: effectiveOrganizationId,
        });
    }
    catch (error) {
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error exporting contact submissions");
    }
});
/**
 * Delete contact form submission
 * @route DELETE /api/v1/private/analytics/contact-submissions/:id
 */
const deleteContactSubmission = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        // Validate ID parameter
        if (!id || isNaN(Number(id))) {
            return res.status(400).json({
                status: false,
                message: "Valid submission ID is required",
            });
        }
        // Get effective organization ID (handles admin users)
        const effectiveOrganizationId = yield validation_helper_1.ValidationHelper.getEffectiveOrganizationId(req.user);
        // Build where clause
        const whereClause = {
            id: Number(id),
            event_type: Analytics_1.AnalyticsEventType.CONTACT_FORM_SUBMIT,
        };
        // Add organization filter if not admin
        if (effectiveOrganizationId !== undefined) {
            whereClause.organization_id = effectiveOrganizationId;
        }
        const deleted = yield Analytics_1.default.destroy({
            where: whereClause,
        });
        if (!deleted) {
            return res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                status: false,
                message: "Contact submission not found",
            });
        }
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("CONTACT_SUBMISSION_DELETED_SUCCESSFULLY"),
        });
    }
    catch (error) {
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error deleting contact submission");
    }
});
/**
 * Get analytics summary - Real data from database
 * @route GET /api/v1/private/analytics
 */
const getAnalyticsSummary = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Input validation and sanitization
        const sanitizedQuery = validation_helper_1.ValidationHelper.sanitizeInput(req.query);
        const { page, limit, event_type, entity_type, entity_id, start_date, end_date, } = sanitizedQuery;
        // Get effective organization ID (handles admin users)
        const effectiveOrganizationId = yield validation_helper_1.ValidationHelper.getEffectiveOrganizationId(req.user, sanitizedQuery.organization_id);
        // Get real analytics data from service
        const summary = yield analytics_service_1.default.getAnalyticsSummary({
            organizationId: effectiveOrganizationId || undefined,
            page: parseInt(page) || 1,
            limit: Math.min(parseInt(limit) || 10, 50),
            event_type,
            entity_type,
            entity_id,
            start_date,
            end_date,
        });
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: "Analytics summary fetched successfully",
            data: summary,
        });
    }
    catch (error) {
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error fetching analytics summary");
    }
});
/**
 * Get recipe view statistics for private recipes with assigned users
 * @route GET /api/v1/private/analytics/recipe-view-statistics/:recipeId
 */
const getRecipeViewStatistics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Input validation and sanitization
        const sanitizedParams = validation_helper_1.ValidationHelper.sanitizeInput(req.params);
        const { recipeId } = sanitizedParams;
        // Convert recipeId to number
        const recipeIdNumber = parseInt(recipeId);
        if (isNaN(recipeIdNumber) || recipeIdNumber <= 0) {
            return res.status(400).json({
                status: false,
                message: "Invalid recipe ID. Must be a positive number",
            });
        }
        // Get effective organization ID (handles admin users)
        const effectiveOrganizationId = yield validation_helper_1.ValidationHelper.getEffectiveOrganizationId(req.user, req.query.organization_id);
        // Call service method
        const result = yield analytics_service_1.default.getRecipeViewStatistics(recipeIdNumber, effectiveOrganizationId || undefined);
        if (!result.status) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: result.message,
            });
        }
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: result.message,
            data: result.data,
        });
    }
    catch (error) {
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error fetching recipe view statistics");
    }
});
/**
 * Reset recipe view statistics for private recipes with assigned users
 * @route DELETE /api/v1/private/analytics/recipe-view-statistics/:recipeId/reset
 */
const resetRecipeViewStatistics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Input validation and sanitization
        const sanitizedParams = validation_helper_1.ValidationHelper.sanitizeInput(req.params);
        const sanitizedBody = validation_helper_1.ValidationHelper.sanitizeInput(req.body);
        const { recipeId } = sanitizedParams;
        const { user_ids } = sanitizedBody;
        // Convert recipeId to number
        const recipeIdNumber = parseInt(recipeId);
        if (isNaN(recipeIdNumber) || recipeIdNumber <= 0) {
            return res.status(400).json({
                status: false,
                message: "Invalid recipe ID. Must be a positive number",
            });
        }
        // Validate user_ids if provided
        let userIds;
        if (user_ids) {
            if (!Array.isArray(user_ids)) {
                return res.status(400).json({
                    status: false,
                    message: "user_ids must be an array of user IDs",
                });
            }
            userIds = user_ids.map((id) => {
                const userId = parseInt(id);
                if (isNaN(userId) || userId <= 0) {
                    throw new Error(`Invalid user ID: ${id}. Must be a positive number`);
                }
                return userId;
            });
            if (userIds.length === 0) {
                return res.status(400).json({
                    status: false,
                    message: "user_ids array cannot be empty when provided",
                });
            }
        }
        // Get effective organization ID (handles admin users)
        const effectiveOrganizationId = yield validation_helper_1.ValidationHelper.getEffectiveOrganizationId(req.user, req.query.organization_id);
        // Call service method
        const result = yield analytics_service_1.default.resetRecipeViewStatistics(recipeIdNumber, effectiveOrganizationId || undefined, userIds);
        if (!result.status) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: result.message,
            });
        }
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: result.message,
        });
    }
    catch (error) {
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error resetting recipe view statistics");
    }
});
// Helper function to convert contact submissions to CSV
function convertContactSubmissionsToCSV(data, dateRange = "last_30_days") {
    const headers = [
        "Recipe ID",
        "Recipe Name",
        "Contact Name",
        "Email",
        "Mobile",
        "Message",
        "Submitted At",
        "Days Ago",
    ];
    const rows = data.map((item) => [
        item.recipe_id || "N/A",
        item.recipe_name || "Unknown Recipe",
        item.contact_name || "N/A",
        item.contact_email || "N/A",
        item.contact_mobile || "N/A",
        (item.message || "").replace(/"/g, '""').replace(/\n/g, " "), // Escape quotes and newlines
        item.submitted_at || "N/A",
        item.time_ago || "N/A",
    ]);
    const csvContent = [
        `# Contact Form Submissions Export - ${new Date().toISOString()}`,
        `# Date Range: ${dateRange}`,
        `# Generated by Recipe Management System`,
        "",
        headers.join(","),
        ...rows.map((row) => row.map((cell) => `"${cell}"`).join(",")),
    ].join("\n");
    return csvContent;
}
exports.default = {
    // Public analytics (simplified)
    trackCtaClick,
    submitContactForm,
    trackRecipeView,
    // Dashboard analytics (simplified)
    getCtaClickAnalytics,
    getContactSubmissionAnalytics,
    exportContactSubmissions,
    getRecipeViewAnalytics,
    deleteContactSubmission,
    getAnalyticsSummary,
    // Recipe view statistics endpoints
    getRecipeViewStatistics,
    resetRecipeViewStatistics,
};
