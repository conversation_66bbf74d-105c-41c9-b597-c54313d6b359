"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const celebrate_1 = require("celebrate");
const RecipeMeasure_1 = require("../models/RecipeMeasure");
const createRecipeMeasureValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object()
        .keys({
        unit_title: celebrate_1.Joi.string().min(1).max(100).required(),
        unit_slug: celebrate_1.Joi.string().max(100).optional(),
        status: celebrate_1.Joi.string()
            .valid(...Object.values(RecipeMeasure_1.MeasureStatus))
            .optional(),
        is_system_unit: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.boolean(), celebrate_1.Joi.string().valid("true", "false"))
            .optional(),
    })
        .unknown(true), // Allow file uploads and other fields
});
const updateRecipeMeasureValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object()
        .keys({
        unit_title: celebrate_1.Joi.string().min(1).max(100).optional(),
        unit_slug: celebrate_1.Joi.string().max(100).optional(),
        status: celebrate_1.Joi.string()
            .valid(...Object.values(RecipeMeasure_1.MeasureStatus))
            .optional(),
        is_system_unit: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.boolean(), celebrate_1.Joi.string().valid("true", "false"))
            .optional(),
    })
        .unknown(true), // Allow file uploads and other fields
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().integer().min(1).required(),
    }),
});
const deleteRecipeMeasureValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
});
const getRecipeMeasureValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
});
const getRecipeMeasuresListValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: celebrate_1.Joi.object().keys({
        page: celebrate_1.Joi.number().integer().min(1).default(1),
        limit: celebrate_1.Joi.number().integer().min(1).max(100).default(10),
        search: celebrate_1.Joi.string().max(100).optional(),
        status: celebrate_1.Joi.string()
            .valid(...Object.values(RecipeMeasure_1.MeasureStatus))
            .optional(),
        sort_by: celebrate_1.Joi.string()
            .valid("unit_title", "created_at", "updated_at")
            .default("created_at"),
        sort_order: celebrate_1.Joi.string().valid("asc", "desc").default("desc"),
    }),
});
// Default export object
exports.default = {
    createRecipeMeasureValidator,
    updateRecipeMeasureValidator,
    deleteRecipeMeasureValidator,
    getRecipeMeasureValidator,
    getRecipeMeasuresListValidator,
};
