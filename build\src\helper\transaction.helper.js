"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorHandler = exports.FileOperationTracker = exports.TransactionManager = void 0;
const models_1 = require("../models");
/**
 * Enhanced transaction management utility
 * Provides safe transaction handling with proper cleanup
 */
class TransactionManager {
    constructor() {
        this.transaction = null;
        this.isCommitted = false;
        this.isRolledBack = false;
    }
    /**
     * Start a new transaction
     */
    start() {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.transaction) {
                throw new Error("Transaction already started");
            }
            this.transaction = yield models_1.sequelize.transaction();
            this.isCommitted = false;
            this.isRolledBack = false;
            return this.transaction;
        });
    }
    /**
     * Get the current transaction
     */
    get() {
        if (!this.transaction) {
            throw new Error("Transaction not started");
        }
        return this.transaction;
    }
    /**
     * Commit the transaction safely
     */
    commit() {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.transaction) {
                throw new Error("No transaction to commit");
            }
            if (this.isCommitted || this.isRolledBack) {
                return; // Already handled
            }
            try {
                yield this.transaction.commit();
                this.isCommitted = true;
            }
            catch (error) {
                console.error("Error committing transaction:", error);
                yield this.safeRollback();
                throw error;
            }
        });
    }
    /**
     * Rollback the transaction safely
     */
    rollback() {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.safeRollback();
        });
    }
    /**
     * Safe rollback that checks transaction state
     */
    safeRollback() {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.transaction) {
                return; // No transaction to rollback
            }
            if (this.isCommitted || this.isRolledBack) {
                return; // Already handled
            }
            try {
                // Check if transaction is still active
                if (!this.transaction.finished) {
                    yield this.transaction.rollback();
                }
                this.isRolledBack = true;
            }
            catch (error) {
                console.error("Error rolling back transaction:", error);
                // Don't throw here to avoid masking original error
            }
        });
    }
    /**
     * Execute a function within a transaction with automatic cleanup
     */
    static execute(operation) {
        return __awaiter(this, void 0, void 0, function* () {
            const manager = new TransactionManager();
            try {
                const transaction = yield manager.start();
                const result = yield operation(transaction);
                yield manager.commit();
                return result;
            }
            catch (error) {
                yield manager.rollback();
                throw error;
            }
        });
    }
    /**
     * Check if transaction is active
     */
    isActive() {
        return this.transaction !== null && !this.isCommitted && !this.isRolledBack;
    }
    /**
     * Get transaction status
     */
    getStatus() {
        if (!this.transaction)
            return 'not_started';
        if (this.isCommitted)
            return 'committed';
        if (this.isRolledBack)
            return 'rolled_back';
        return 'active';
    }
}
exports.TransactionManager = TransactionManager;
/**
 * File operation rollback utility
 * Tracks file operations for cleanup on transaction failure
 */
class FileOperationTracker {
    constructor() {
        this.operations = [];
    }
    /**
     * Track a file move operation
     */
    trackMove(originalPath, newPath, itemId) {
        this.operations.push({
            type: 'move',
            originalPath,
            newPath,
            itemId
        });
    }
    /**
     * Track a file creation
     */
    trackCreate(filePath, itemId) {
        this.operations.push({
            type: 'create',
            newPath: filePath,
            itemId
        });
    }
    /**
     * Track a file deletion
     */
    trackDelete(filePath) {
        this.operations.push({
            type: 'delete',
            originalPath: filePath
        });
    }
    /**
     * Rollback all tracked file operations
     */
    rollback() {
        return __awaiter(this, void 0, void 0, function* () {
            const uploadService = (yield Promise.resolve().then(() => __importStar(require("./upload.service")))).default;
            const bucketName = process.env.NODE_ENV || "development";
            // Reverse the operations to undo them
            for (const operation of this.operations.reverse()) {
                try {
                    switch (operation.type) {
                        case 'move':
                            if (operation.originalPath && operation.newPath) {
                                // Move file back to original location using correct method
                                yield uploadService.moveFileInBucket(bucketName, operation.newPath, operation.originalPath, operation.itemId || 0);
                            }
                            break;
                        case 'create':
                            if (operation.newPath) {
                                // Delete the created file using correct method
                                yield uploadService.deleteFileFromBucket(bucketName, operation.newPath);
                            }
                            break;
                        case 'delete':
                            // Can't restore deleted files, just log
                            console.warn(`Cannot restore deleted file: ${operation.originalPath}`);
                            break;
                    }
                }
                catch (error) {
                    console.error(`Error rolling back file operation:`, error);
                    // Continue with other operations
                }
            }
            // Clear operations after rollback
            this.operations = [];
        });
    }
    /**
     * Clear all tracked operations (call after successful commit)
     */
    clear() {
        this.operations = [];
    }
}
exports.FileOperationTracker = FileOperationTracker;
/**
 * Enhanced error handling utility
 */
class ErrorHandler {
    /**
     * Handle database errors with specific error types
     */
    static handleDatabaseError(error) {
        var _a, _b, _c, _d;
        // Sequelize validation errors
        if (error.name === 'SequelizeValidationError') {
            return {
                statusCode: 400,
                message: ((_b = (_a = error.errors) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.message) || 'Validation error',
                errorType: 'VALIDATION_ERROR'
            };
        }
        // Unique constraint violations
        if (error.name === 'SequelizeUniqueConstraintError') {
            const field = ((_d = (_c = error.errors) === null || _c === void 0 ? void 0 : _c[0]) === null || _d === void 0 ? void 0 : _d.path) || 'field';
            return {
                statusCode: 409,
                message: `${field} already exists`,
                errorType: 'DUPLICATE_ERROR'
            };
        }
        // Foreign key constraint violations
        if (error.name === 'SequelizeForeignKeyConstraintError') {
            return {
                statusCode: 400,
                message: 'Invalid reference to related data',
                errorType: 'FOREIGN_KEY_ERROR'
            };
        }
        // Database connection errors
        if (error.name === 'SequelizeConnectionError') {
            return {
                statusCode: 503,
                message: 'Database connection error',
                errorType: 'CONNECTION_ERROR'
            };
        }
        // Default error
        return {
            statusCode: 500,
            message: error.message || 'Internal server error',
            errorType: 'UNKNOWN_ERROR'
        };
    }
    /**
     * Create standardized error response
     */
    static createErrorResponse(error, res, defaultMessage = 'Something went wrong') {
        const { statusCode, message, errorType } = this.handleDatabaseError(error);
        return res.status(statusCode).json({
            status: false,
            message: res.__(message) || defaultMessage,
            errorType,
            error: process.env.NODE_ENV === "development" ? error.message : undefined,
        });
    }
}
exports.ErrorHandler = ErrorHandler;
