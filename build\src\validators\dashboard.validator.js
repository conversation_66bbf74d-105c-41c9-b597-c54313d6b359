"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const celebrate_1 = require("celebrate");
// Valid date range options for dashboard
const dateRangeOptions = [
    "today",
    "this_week",
    "this_month",
    "last_7_days",
    "last_month",
    "last_30_days",
    "last_90_days",
    "last_year",
    "custom", // For custom date range
];
/**
 * Validator for dashboard overview endpoint
 * Validates date_range query parameter
 */
const getDashboardOverviewValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: {
        date_range: celebrate_1.Joi.string()
            .valid(...dateRangeOptions)
            .default("last_30_days")
            .description("Date range for dashboard analytics"),
        category_type: celebrate_1.Joi.string()
            .optional()
            .description("Filter by category type/name"),
    },
});
/**
 * Validator for dashboard export endpoint
 * Validates format and date_range query parameters
 */
const exportDashboardDataValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: {
        format: celebrate_1.Joi.string()
            .valid("json", "csv")
            .default("json")
            .description("Export format"),
        date_range: celebrate_1.Joi.string()
            .valid(...dateRangeOptions)
            .default("last_30_days")
            .description("Date range for exported data"),
    },
});
/**
 * Validator for CTA analytics endpoint in dashboard
 * Reuses analytics validation logic for consistency
 */
const getCtaAnalyticsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: {
        page: celebrate_1.Joi.number().min(1).optional(),
        limit: celebrate_1.Joi.number().min(1).max(100).optional(),
        date_range: celebrate_1.Joi.string()
            .valid(...dateRangeOptions)
            .optional(),
        sort: celebrate_1.Joi.string().valid("asc", "desc").optional(),
        sort_by: celebrate_1.Joi.string()
            .valid("recipe_name", "clicks", "last_clicked_at", "cta_type")
            .optional()
            .description("Field to sort by"),
        cta_type: celebrate_1.Joi.string()
            .valid("contact_info", "contact_form", "custom_cta")
            .optional()
            .description("Filter by CTA type"),
        recipe_name: celebrate_1.Joi.string()
            .optional()
            .description("Filter by recipe name (partial match)"),
        search: celebrate_1.Joi.string()
            .optional()
            .description("Search across recipe name and other fields"),
    },
});
/**
 * Validator for contact analytics endpoint in dashboard
 * Reuses analytics validation logic for consistency
 */
const getContactAnalyticsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: {
        page: celebrate_1.Joi.number().min(1).optional(),
        limit: celebrate_1.Joi.number().min(1).max(100).optional(),
        date_range: celebrate_1.Joi.string()
            .valid(...dateRangeOptions)
            .optional(),
        recipe_id: celebrate_1.Joi.number().optional(),
        search: celebrate_1.Joi.string()
            .optional()
            .description("Search across recipe name and contact email"),
        sort: celebrate_1.Joi.string().valid("asc", "desc").optional(),
        sort_by: celebrate_1.Joi.string()
            .valid("recipe_name", "contact_name", "contact_email", "submitted_on")
            .optional()
            .description("Field to sort by"),
    },
});
exports.default = {
    getDashboardOverviewValidator,
    exportDashboardDataValidator,
    getCtaAnalyticsValidator,
    getContactAnalyticsValidator,
};
