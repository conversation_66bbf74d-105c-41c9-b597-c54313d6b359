"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IngredientConversion = exports.IngredientConversionStatus = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var IngredientConversionStatus;
(function (IngredientConversionStatus) {
    IngredientConversionStatus["active"] = "active";
    IngredientConversionStatus["inactive"] = "inactive";
})(IngredientConversionStatus || (exports.IngredientConversionStatus = IngredientConversionStatus = {}));
class IngredientConversion extends sequelize_1.Model {
}
exports.IngredientConversion = IngredientConversion;
IngredientConversion.init({
    ingredient_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: "mo_ingredients",
            key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
    },
    from_measure: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: "mo_recipe_measure",
            key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
    },
    from_measure_value: {
        type: sequelize_1.DataTypes.DECIMAL(10, 2),
        allowNull: false,
    },
    to_measure: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: "mo_recipe_measure",
            key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
    },
    to_measure_value: {
        type: sequelize_1.DataTypes.DECIMAL(10, 2),
        allowNull: false,
    },
    ingredient_conversion_status: {
        type: sequelize_1.DataTypes.ENUM,
        values: Object.values(IngredientConversionStatus),
        defaultValue: IngredientConversionStatus.active,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_ingredients_conversion",
    modelName: "IngredientConversion",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    // Removed unique constraint to allow multiple conversions with same from/to measures
    // indexes: [
    //     {
    //         unique: true,
    //         fields: ['ingredient_id', 'from_measure', 'to_measure']
    //     }
    // ]
});
// Remove the automatic 'id' attribute for junction table
IngredientConversion.removeAttribute("id");
// Define associations
IngredientConversion.associate = (models) => {
    // IngredientConversion belongs to Ingredient
    IngredientConversion.belongsTo(models.Ingredient, {
        foreignKey: "ingredient_id",
        as: "ingredient",
    });
    // IngredientConversion belongs to RecipeMeasure (from_measure)
    IngredientConversion.belongsTo(models.RecipeMeasure, {
        foreignKey: "from_measure",
        as: "fromUnit",
    });
    // IngredientConversion belongs to RecipeMeasure (to_measure)
    IngredientConversion.belongsTo(models.RecipeMeasure, {
        foreignKey: "to_measure",
        as: "toUnit",
    });
};
