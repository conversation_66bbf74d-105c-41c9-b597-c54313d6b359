"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Category = exports.CategoryType = exports.CategoryStatus = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var CategoryStatus;
(function (CategoryStatus) {
    CategoryStatus["active"] = "active";
    CategoryStatus["inactive"] = "inactive";
})(CategoryStatus || (exports.CategoryStatus = CategoryStatus = {}));
var CategoryType;
(function (CategoryType) {
    CategoryType["recipe"] = "recipe";
    CategoryType["ingredient"] = "ingredient";
})(CategoryType || (exports.CategoryType = CategoryType = {}));
class Category extends sequelize_1.Model {
}
exports.Category = Category;
Category.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    category_name: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
    },
    category_slug: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
    },
    category_description: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    category_icon: {
        type: sequelize_1.DataTypes.INTEGER, // Store item_id reference
        allowNull: true,
        comment: "Foreign key reference to nv_items table for category icon",
        references: {
            model: "nv_items",
            key: "id",
        },
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
    },
    category_status: {
        type: sequelize_1.DataTypes.ENUM(Object.values(CategoryStatus)),
        allowNull: false,
        defaultValue: CategoryStatus.active,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    category_type: {
        type: sequelize_1.DataTypes.ENUM(Object.values(CategoryType)),
        allowNull: false,
    },
    is_system_category: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_category",
    modelName: "Category",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
});
// Define associations
Category.associate = (models) => {
    // Category belongs to User (created_by)
    Category.belongsTo(models.User, {
        foreignKey: "created_by",
        as: "creator",
    });
    // Category belongs to User (updated_by)
    Category.belongsTo(models.User, {
        foreignKey: "updated_by",
        as: "updater",
    });
    // Many-to-many association with Ingredient through IngredientCategory
    Category.belongsToMany(models.Ingredient, {
        through: models.IngredientCategory,
        foreignKey: "category_id",
        otherKey: "ingredient_id",
        as: "ingredients",
    });
    // Many-to-many association with Recipe through RecipeCategory
    Category.belongsToMany(models.Recipe, {
        through: models.RecipeCategory,
        foreignKey: "category_id",
        otherKey: "recipe_id",
        as: "recipes",
    });
    // Category belongs to Item (category_icon) - Proper foreign key constraint
    Category.belongsTo(models.Item, {
        foreignKey: "category_icon",
        as: "iconItem",
        constraints: true,
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
    });
};
exports.default = Category;
