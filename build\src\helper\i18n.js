"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const i18n_1 = __importDefault(require("i18n"));
const path_1 = __importDefault(require("path"));
i18n_1.default.configure({
    locales: ["en", "de", "fr"],
    directory: path_1.default.join(__dirname, "../locales"),
    defaultLocale: "en",
    header: "accept-language",
    register: global,
    updateFiles: false,
    syncFiles: false,
    objectNotation: false,
    mustacheConfig: {
        tags: ["{{", "}}"],
        disable: false,
    },
});
exports.default = i18n_1.default;
