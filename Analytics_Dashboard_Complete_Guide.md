# 📊 Analytics & Dashboard System - Complete Technical Documentation

## 📖 Table of Contents
1. [System Overview](#system-overview)
2. [Architecture & Data Flow](#architecture--data-flow)
3. [Database Structure](#database-structure)
4. [Event Types & Tracking](#event-types--tracking)
5. [API Endpoints](#api-endpoints)
6. [Dashboard Components](#dashboard-components)
7. [Technical Implementation](#technical-implementation)
8. [Real-World Examples](#real-world-examples)
9. [Integration Guide](#integration-guide)
10. [Troubleshooting](#troubleshooting)

---

## 🎯 System Overview

The **Recipe Analytics & Dashboard System** is a comprehensive tracking and reporting solution that monitors user interactions on **public recipe pages** and provides **administrative insights** through a powerful dashboard.

### 🏆 Key Objectives
- **Track Public User Behavior** - Monitor how visitors interact with recipes
- **Measure Engagement** - Track views, clicks, shares, and contact submissions
- **Generate Business Intelligence** - Provide actionable insights for decision-making
- **Multi-Tenant Support** - Separate analytics per organization
- **Real-Time Reporting** - Live dashboard updates without page refresh

### 🎭 User Personas

#### **Public Users (Anonymous)**
- View recipes without authentication
- Click CTA buttons (Contact Info, Contact Form, Custom Links)
- Submit contact forms
- Share/bookmark recipes
- **Goal**: Find and interact with recipes

#### **Admin Users (Authenticated)**
- Access private dashboard
- View comprehensive analytics
- Export data (CSV/JSON)
- Configure tracking settings
- **Goal**: Understand user behavior and optimize recipes

---

## 🏗️ Architecture & Data Flow

```mermaid
graph TB
    subgraph "Public Layer"
        PU[Public Users]
        RP[Recipe Pages]
        CTA[CTA Buttons]
        CF[Contact Forms]
    end
    
    subgraph "Analytics Engine"
        API[Public Analytics API]
        VAL[Validation Layer]
        SVC[Analytics Service]
        DB[(Database)]
    end
    
    subgraph "Admin Layer"
        AU[Admin Users]
        DASH[Dashboard]
        PRIV[Private API]
        EXP[Export System]
    end
    
    PU --> RP
    RP --> CTA
    CTA --> CF
    CF --> API
    RP --> API
    CTA --> API
    
    API --> VAL
    VAL --> SVC
    SVC --> DB
    
    AU --> DASH
    DASH --> PRIV
    PRIV --> SVC
    SVC --> EXP
```

### 🔄 Event Flow Lifecycle

```
1. USER VISITS RECIPE PAGE
   ↓ (Auto-triggered)
2. TRACK: recipe_view event
   ↓ (User clicks CTA)
3. TRACK: cta_click event  
   ↓ (User submits form)
4. TRACK: contact_form_submit event
   ↓ (Admin views dashboard)
5. AGGREGATE: All events into analytics
   ↓ (Display insights)
6. DASHBOARD: Shows comprehensive metrics
```

---

## 🗄️ Database Structure

### Primary Table: `mo_recipe_analytics`

```sql
CREATE TABLE mo_recipe_analytics (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INTEGER,
    organization_id VARCHAR(100),
    user_id INTEGER,
    ip_address VARCHAR(45),
    user_agent TEXT,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_analytics_event_type ON mo_recipe_analytics(event_type);
CREATE INDEX idx_analytics_organization ON mo_recipe_analytics(organization_id);
CREATE INDEX idx_analytics_created_at ON mo_recipe_analytics(created_at);
CREATE INDEX idx_analytics_entity ON mo_recipe_analytics(entity_type, entity_id);
```

### 📊 Field Descriptions

| Field | Type | Purpose | Example |
|-------|------|---------|---------|
| `id` | Serial | Primary key | 12345 |
| `event_type` | Enum | Type of event | "recipe_view", "cta_click" |
| `entity_type` | Enum | What was interacted with | "recipe", "category" |
| `entity_id` | Integer | ID of the entity | 123 (recipe ID) |
| `organization_id` | String | Which organization | "bakery_abc" |
| `user_id` | Integer | If user is logged in | 456 (or null) |
| `ip_address` | String | User's IP address | "***********" |
| `user_agent` | Text | Browser information | "Mozilla/5.0..." |
| `metadata` | JSONB | Flexible event data | {"recipe_name": "Cake"} |
| `created_at` | Timestamp | When event occurred | "2024-01-15 10:30:00" |

---

## 📝 Event Types & Tracking

### 🎯 Event Types Enum

```typescript
export enum AnalyticsEventType {
  RECIPE_VIEW = "recipe_view",           // User views a recipe
  CTA_CLICK = "cta_click",               // User clicks CTA button
  CONTACT_FORM_SUBMIT = "contact_form_submit", // User submits contact form
  RECIPE_BOOKMARK = "recipe_bookmark",    // User bookmarks recipe
  RECIPE_SHARE = "recipe_share",         // User shares recipe
}
```

### 🎭 Entity Types Enum

```typescript
export enum AnalyticsEntityType {
  RECIPE = "recipe",                     // Recipe-related events
  CATEGORY = "category",                 // Category-related events
  INGREDIENT = "ingredient",             // Ingredient-related events
  DASHBOARD = "dashboard",               // Dashboard interactions
  SETTINGS = "settings",                 // Settings modifications
}
```

### 📋 Tracking Specifications

#### 1. **Recipe View Event**
```json
{
  "event_type": "recipe_view",
  "entity_type": "recipe",
  "entity_id": 123,
  "organization_id": "bakery_abc",
  "session_id": "sess_xyz789",
  "metadata": {
    "recipe_name": "Chocolate Cake",
    "referrer": "https://google.com",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

#### 2. **CTA Click Event**
```json
{
  "event_type": "cta_click",
  "entity_type": "recipe", 
  "entity_id": 123,
  "organization_id": "bakery_abc",
  "session_id": "sess_xyz789",
  "metadata": {
    "recipe_name": "Chocolate Cake",
    "cta_type": "contact_form",  // contact_info, custom_cta
    "cta_text": "Get Recipe Details",
    "button_position": "top",
    "timestamp": "2024-01-15T10:32:00Z"
  }
}
```

#### 3. **Contact Form Submit Event**
```json
{
  "event_type": "contact_form_submit",
  "entity_type": "recipe",
  "entity_id": 123,
  "organization_id": "bakery_abc",
  "session_id": "sess_xyz789", 
  "metadata": {
    "recipe_name": "Chocolate Cake",
    "contact_name": "John Doe",
    "contact_email": "<EMAIL>",
    "contact_mobile": "+1234567890",
    "message": "I love this recipe!",
    "form_completion_time": 180,
    "timestamp": "2024-01-15T10:35:00Z"
  }
}
```

---

## 🌐 API Endpoints

### **Public Endpoints** (No Authentication)

#### 1. Track Recipe View
```http
POST /api/v1/public/analytics/track/recipe-view
Content-Type: application/json

{
  "recipe_id": 123,
  "organization_id": "bakery_abc",
  "recipe_name": "Chocolate Cake",
  "event_type": "recipe_view",
  "referrer": "https://google.com"
}
```

**Response:**
```json
{
  "status": true,
  "message": "Recipe view tracked successfully"
}
```

#### 2. Track CTA Click
```http
POST /api/v1/public/analytics/track/cta-click
Content-Type: application/json

{
  "recipe_id": 123,
  "organization_id": "bakery_abc", 
  "session_id": "sess_xyz789",
  "recipe_name": "Chocolate Cake",
  "cta_type": "contact_form",
  "cta_text": "Get Recipe Details"
}
```

#### 3. Submit Contact Form
```http
POST /api/v1/public/analytics/contact-form
Content-Type: application/json

{
  "recipe_id": 123,
  "organization_id": "bakery_abc",
  "recipe_name": "Chocolate Cake",
  "name": "John Doe",
  "email": "<EMAIL>",
  "mobile": "+1234567890",
  "message": "I love this recipe!"
}
```

### **Private Endpoints** (Authentication Required)

#### 1. Dashboard Overview
```http
GET /api/v1/private/dashboard/overview?date_range=last_30_days
Authorization: Bearer <token>
```

**Response:**
```json
{
  "status": true,
  "data": {
    "stats": {
      "totalRecipes": 250,
      "activeUsers": 1250,
      "topCategory": {"name": "Desserts", "count": 45},
      "monthlyRevenue": 15000,
      "growthRate": 12.5,
      "engagementRate": 8.7
    },
    "analytics": {
      "totalViews": 5420,
      "totalBookmarks": 289,
      "totalShares": 156,
      "totalContactSubmissions": 45
    },
    "charts": {
      "recipeViewsTrend": [...],
      "categoryPerformance": [...],
      "userEngagementHeatmap": [...],
      "conversionFunnel": [...]
    },
    "recentActivity": [...]
  }
}
```

#### 2. CTA Click Analytics
```http
GET /api/v1/private/analytics/cta-clicks?date_range=last_30_days&sort=desc
Authorization: Bearer <token>
```

**Response:**
```json
{
  "status": true,
  "data": [
    {
      "recipe_id": 123,
      "recipe_name": "Chocolate Cake",
      "cta_type": "contact_form",
      "clicks": 25,
      "last_clicked_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### 3. Contact Submissions
```http
GET /api/v1/private/analytics/contact-submissions?date_range=last_30_days
Authorization: Bearer <token>
```

---

## 📊 Dashboard Components

### 🏠 Dashboard Layout (50/50 Split)

```
┌─────────────────────────────────────────────────────────────┐
│                    📊 DASHBOARD HEADER                      │
│  🍰 Recipes: 250   👥 Users: 1.2K   🏆 Top: Desserts      │
│  💰 Revenue: $15K   📈 Growth: +12%   🎯 Engagement: 8.7%  │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│                    📈 ANALYTICS SECTION                     │
├─────────────────────┬───────────────────────────────────────┤
│   🎯 CTA ANALYTICS  │      📝 CONTACT SUBMISSIONS           │
│     (LEFT 50%)      │         (RIGHT 50%)                   │
├─────────────────────┼───────────────────────────────────────┤
│ Recipe      | Clicks│ Recipe      | Name  | Email  | Date   │
│ Chocolate   |   25  │ Chocolate   | John  | john@  | Today  │
│ Apple Pie   |   18  │ Apple Pie   | Jane  | jane@  | 1d ago │
│ Cookies     |   12  │ Cookies     | Bob   | bob@   | 2d ago │
│ Pasta       |    8  │ Pasta       | Alice | alice@ | 3d ago │
│ Pizza       |    5  │ Pizza       | Mike  | mike@  | 4d ago │
└─────────────────────┴───────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│                    📊 CHARTS SECTION                        │
│  📈 Recipe Views Trend    📊 Category Performance          │
│  🔥 Engagement Heatmap    🎯 Conversion Funnel             │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│                   ⚡ RECENT ACTIVITY                        │
│  • John viewed "Chocolate Cake" - 2 min ago                │
│  • Jane clicked CTA on "Apple Pie" - 5 min ago             │
│  • Bob submitted contact form - 10 min ago                 │
└─────────────────────────────────────────────────────────────┘
```

### 📊 Dashboard Statistics

#### **Core Metrics Cards**
```typescript
interface DashboardStats {
  totalRecipes: number;         // Total recipes in system
  activeUsers: number;          // Unique users in date range
  topCategory: {               // Most popular category
    name: string;
    count: number;
  };
  monthlyRevenue: number;      // Calculated revenue
  growthRate: number;          // Percentage growth
  engagementRate: number;      // User engagement percentage
}
```

#### **Analytics Metrics**
```typescript
interface AnalyticsData {
  totalViews: number;              // Total recipe views
  totalBookmarks: number;          // Total bookmarks
  totalShares: number;             // Total shares
  totalContactSubmissions: number; // Total contact form submissions
}
```

#### **Chart Data Structures**
```typescript
interface ChartData {
  recipeViewsTrend: {              // Line chart data
    date: string;
    views: number;
    unique_visitors: number;
  }[];
  
  categoryPerformance: {           // Bar chart data
    category: string;
    views: number;
    engagement: number;
  }[];
  
  userEngagementHeatmap: {         // Heatmap data
    hour: number;
    day: number;
    activity: number;
  }[];
  
  conversionFunnel: {              // Conversion funnel
    stage: string;
    count: number;
    percentage: number;
  }[];
}
```

---

## ⚙️ Technical Implementation

### 🔧 Validation Layer

The system uses **Joi validation** for all inputs:

```typescript
// Recipe view validation
const trackRecipeViewValidator = () =>
  celebrate({
    [Segments.BODY]: {
      recipe_id: Joi.number().required(),
      organization_id: Joi.string().optional(),
      recipe_name: Joi.string().optional(),
      event_type: Joi.string()
        .valid("recipe_view", "recipe_bookmark", "recipe_share")
        .default("recipe_view")
        .optional(),
      referrer: Joi.string().optional(),
      share_platform: Joi.string()
        .valid("facebook", "twitter", "whatsapp", "email", "copy_link")
        .optional(),
    },
  });
```

### 🏃‍♂️ Service Layer

The **Analytics Service** handles all business logic:

```typescript
class AnalyticsService {
  // Track any event with flexible metadata
  async trackEvent(data: {
    eventType: AnalyticsEventType;
    entityType: AnalyticsEntityType;
    entityId?: number;
    organizationId?: string;
    userId?: number;
    sessionId?: string;
    ipAddress?: string;
    userAgent?: string;
    metadata?: any;
  }): Promise<Analytics> {
    return await Analytics.trackEvent({
      event_type: data.eventType,
      entity_type: data.entityType,
      entity_id: data.entityId,
      organization_id: data.organizationId,
      user_id: data.userId,
      session_id: data.sessionId,
      ip_address: data.ipAddress,
      user_agent: data.userAgent,
      metadata: data.metadata,
    });
  }

  // Get comprehensive dashboard statistics
  async getDashboardStats(
    organizationId?: string,
    dateRange: string = "last_30_days"
  ): Promise<DashboardStats> {
    // Complex aggregation logic...
  }
}
```

### 🗃️ Model Layer

The **Analytics Model** using Sequelize ORM:

```typescript
export class Analytics extends Model<AnalyticsAttributes> {
  id!: number;
  event_type!: AnalyticsEventType;
  entity_type!: AnalyticsEntityType;
  entity_id?: number;
  organization_id?: string;
  user_id?: number;
  ip_address?: string;
  user_agent?: string;
  metadata?: any;
  created_at!: Date;

  // Helper method to get typed metadata
  getMetadata<T = any>(): T {
    return this.metadata
      ? typeof this.metadata === "string"
        ? JSON.parse(this.metadata)
        : this.metadata
      : ({} as T);
  }

  // Static method to track events easily
  static async trackEvent(data: any): Promise<Analytics> {
    return await Analytics.create({
      ...data,
      metadata: data.metadata ? JSON.stringify(data.metadata) : null,
    });
  }
}
```

---

## 🌟 Real-World Examples

### **Example 1: Complete User Journey**

```javascript
// 1. User visits recipe page (auto-triggered)
await fetch('/api/v1/public/analytics/track/recipe-view', {
  method: 'POST',
  body: JSON.stringify({
    recipe_id: 123,
    organization_id: "gourmet_kitchen",
    recipe_name: "Professional Chocolate Soufflé",
    event_type: "recipe_view",
    referrer: document.referrer
  })
});

// 2. User scrolls and spends time reading
await fetch('/api/v1/public/analytics/track/recipe-view', {
  method: 'POST',
  body: JSON.stringify({
    recipe_id: 123,
    organization_id: "gourmet_kitchen",
    recipe_name: "Professional Chocolate Soufflé",
    event_type: "recipe_view",
    scroll_depth: 45 // percentage scrolled
  })
});

// 3. User clicks "Contact Chef" button
await fetch('/api/v1/public/analytics/track/cta-click', {
  method: 'POST',
  body: JSON.stringify({
    recipe_id: 123,
    organization_id: "gourmet_kitchen",
    recipe_name: "Professional Chocolate Soufflé",
    cta_type: "contact_form",
    cta_text: "Contact Chef for Personal Lesson"
  })
});

// 4. User fills and submits contact form
await fetch('/api/v1/public/analytics/contact-form', {
  method: 'POST',
  body: JSON.stringify({
    recipe_id: 123,
    organization_id: "gourmet_kitchen",
    recipe_name: "Professional Chocolate Soufflé",
    name: "Sarah Johnson",
    email: "<EMAIL>",
    mobile: "******-0123",
    message: "I'd love to learn this recipe for my restaurant!"
  })
});
```

### **Example 2: Admin Dashboard Query**

```javascript
// Admin opens dashboard
const response = await fetch('/api/v1/private/dashboard/overview?date_range=last_30_days', {
  headers: {
    'Authorization': `Bearer ${adminToken}`,
    'Content-Type': 'application/json'
  }
});

const dashboardData = await response.json();

// Dashboard data structure:
{
  "status": true,
  "data": {
    "stats": {
      "totalRecipes": 150,
      "activeUsers": 2847,
      "topCategory": {"name": "French Cuisine", "count": 23},
      "monthlyRevenue": 45600,
      "growthRate": 18.5,
      "engagementRate": 12.3
    },
    "analytics": {
      "totalViews": 15420,
      "totalBookmarks": 892,
      "totalShares": 347,
      "totalContactSubmissions": 156
    },
    "charts": {
      "recipeViewsTrend": [
        {"date": "2024-01-01", "views": 234, "unique_visitors": 89},
        {"date": "2024-01-02", "views": 267, "unique_visitors": 102},
        // ... more data points
      ],
      "categoryPerformance": [
        {"category": "French Cuisine", "views": 3400, "engagement": 15.2},
        {"category": "Italian", "views": 2890, "engagement": 12.8},
        // ... more categories
      ]
    },
    "recentActivity": [
      {
        "type": "contact_form_submit",
        "recipe_name": "Chocolate Soufflé", 
        "user_name": "Sarah Johnson",
        "timestamp": "2024-01-15T14:30:00Z"
      },
      // ... more activities
    ]
  }
}
```

### **Example 3: Business Intelligence Queries**

```javascript
// Get top-performing recipes by conversion rate
const conversionData = await fetch('/api/v1/private/analytics/conversion-analysis?date_range=last_90_days');

// Response shows which recipes convert best
{
  "data": [
    {
      "recipe_id": 123,
      "recipe_name": "Chocolate Soufflé",
      "total_views": 1250,
      "cta_clicks": 187,
      "contact_submissions": 23,
      "conversion_rate": 12.3,  // contacts / clicks
      "view_to_contact_rate": 1.8  // contacts / views
    }
  ]
}

// Identify trending recipes
const trendingData = await fetch('/api/v1/private/analytics/trending-recipes?period=weekly');

// See which recipes are gaining popularity
{
  "data": [
    {
      "recipe_id": 456,
      "recipe_name": "Vegan Pasta Carbonara",
      "growth_rate": 145.6,  // % increase in views
      "current_week_views": 456,
      "previous_week_views": 186,
      "engagement_trend": "rising"
    }
  ]
}
```

---

## 🔌 Integration Guide

### **Frontend Integration**

#### **HTML/JavaScript Setup**
```html
<!DOCTYPE html>
<html>
<head>
    <title>Recipe Page</title>
</head>
<body>
    <div class="recipe-container">
        <h1 id="recipe-title">Chocolate Soufflé</h1>
        <div class="recipe-content">
            <!-- Recipe content here -->
        </div>
        
        <!-- CTA Buttons -->
        <button id="contact-btn" class="cta-button" data-cta-type="contact_form">
            Contact Chef
        </button>
        
        <div id="contact-form" style="display:none;">
            <form id="contact-form-element">
                <input type="text" name="name" placeholder="Your Name" required>
                <input type="email" name="email" placeholder="Email" required>
                <input type="tel" name="mobile" placeholder="Phone">
                <textarea name="message" placeholder="Message" required></textarea>
                <button type="submit">Send Message</button>
            </form>
        </div>
    </div>

    <script src="analytics-tracker.js"></script>
</body>
</html>
```

#### **Analytics Tracker Script**
```javascript
// analytics-tracker.js
class RecipeAnalytics {
    constructor(recipeId, organizationId) {
        this.recipeId = recipeId;
        this.organizationId = organizationId;
        this.startTime = Date.now();

        this.init();
    }
    
    init() {
        // Track initial page view
        this.trackRecipeView();
        
        // Setup CTA click tracking
        this.setupCtaTracking();
        
        // Setup form submission tracking
        this.setupFormTracking();
        
        // Track page visibility changes
        this.setupVisibilityTracking();
        
        // Track scroll depth
        this.setupScrollTracking();
    }
    

    
    async trackRecipeView(additionalData = {}) {
        const data = {
            recipe_id: this.recipeId,
            organization_id: this.organizationId,
            recipe_name: document.getElementById('recipe-title').textContent,
            event_type: "recipe_view",
            referrer: document.referrer,
            ...additionalData
        };
        
        try {
            await fetch('/api/v1/public/analytics/track/recipe-view', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });
        } catch (error) {
            console.error('Analytics tracking failed:', error);
        }
    }
    
    setupCtaTracking() {
        document.querySelectorAll('.cta-button').forEach(button => {
            button.addEventListener('click', async (e) => {
                const ctaType = e.target.dataset.ctaType;
                const ctaText = e.target.textContent;
                
                await this.trackCtaClick(ctaType, ctaText);
            });
        });
    }
    
    async trackCtaClick(ctaType, ctaText) {
        const data = {
            recipe_id: this.recipeId,
            organization_id: this.organizationId,
            session_id: this.sessionId,
            recipe_name: document.getElementById('recipe-title').textContent,
            cta_type: ctaType,
            cta_text: ctaText
        };
        
        try {
            await fetch('/api/v1/public/analytics/track/cta-click', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });
        } catch (error) {
            console.error('CTA tracking failed:', error);
        }
    }
    
    setupFormTracking() {
        const form = document.getElementById('contact-form-element');
        if (form) {
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const formData = new FormData(form);
                const data = {
                    recipe_id: this.recipeId,
                    organization_id: this.organizationId,
                    recipe_name: document.getElementById('recipe-title').textContent,
                    name: formData.get('name'),
                    email: formData.get('email'),
                    mobile: formData.get('mobile'),
                    message: formData.get('message')
                };
                
                try {
                    const response = await fetch('/api/v1/public/analytics/contact-form', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    });
                    
                    if (response.ok) {
                        alert('Thank you! Your message has been sent.');
                        form.reset();
                        document.getElementById('contact-form').style.display = 'none';
                    }
                } catch (error) {
                    console.error('Form submission failed:', error);
                    alert('Sorry, there was an error sending your message.');
                }
            });
        }
    }
    
    setupVisibilityTracking() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // Page is now hidden, update view duration
                this.trackRecipeView({ 
                    visibility_change: 'hidden',
                    final_duration: Math.floor((Date.now() - this.startTime) / 1000)
                });
            }
        });
    }
    
    setupScrollTracking() {
        let maxScrollDepth = 0;
        
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            
            if (scrollPercent > maxScrollDepth) {
                maxScrollDepth = Math.floor(scrollPercent);
                
                // Track significant scroll milestones
                if (maxScrollDepth % 25 === 0 && maxScrollDepth > 0) {
                    this.trackRecipeView({ 
                        scroll_milestone: maxScrollDepth,
                        scroll_depth: maxScrollDepth
                    });
                }
            }
        });
    }
}

// Initialize analytics when page loads
document.addEventListener('DOMContentLoaded', () => {
    // Get recipe ID and organization ID from page data
    const recipeId = window.recipeData?.id || 123;
    const organizationId = window.organizationData?.id || 'default_org';
    
    new RecipeAnalytics(recipeId, organizationId);
});
```

### **React Integration**
```jsx
// useAnalytics.js - Custom React Hook
import { useEffect, useRef } from 'react';

export const useAnalytics = (recipeId, organizationId) => {
    const sessionId = useRef(`sess_${Math.random().toString(36).substr(2, 9)}`);
    const startTime = useRef(Date.now());
    
    const trackEvent = async (endpoint, data) => {
        try {
            await fetch(`/api/v1/public/analytics/${endpoint}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    recipe_id: recipeId,
                    organization_id: organizationId,
                    session_id: sessionId.current,
                    ...data
                })
            });
        } catch (error) {
            console.error('Analytics error:', error);
        }
    };
    
    const trackRecipeView = (additionalData = {}) => {
        trackEvent('track/recipe-view', {
            event_type: 'recipe_view',
            referrer: document.referrer,
            ...additionalData
        });
    };
    
    const trackCtaClick = (ctaType, ctaText) => {
        trackEvent('track/cta-click', {
            cta_type: ctaType,
            cta_text: ctaText
        });
    };
    
    const submitContactForm = (formData) => {
        trackEvent('contact-form', formData);
    };
    
    // Track initial view
    useEffect(() => {
        trackRecipeView();
    }, []);
    
    return {
        trackRecipeView,
        trackCtaClick, 
        submitContactForm
    };
};

// RecipePage.jsx - React Component
import React from 'react';
import { useAnalytics } from './useAnalytics';

const RecipePage = ({ recipe, organization }) => {
    const { trackCtaClick, submitContactForm } = useAnalytics(recipe.id, organization.id);
    
    const handleCtaClick = (ctaType) => {
        trackCtaClick(ctaType, 'Contact Chef');
        // Show contact form or navigate
    };
    
    const handleFormSubmit = (formData) => {
        submitContactForm(formData);
        // Handle form submission success
    };
    
    return (
        <div className="recipe-page">
            <h1>{recipe.name}</h1>
            <div className="recipe-content">
                {/* Recipe content */}
            </div>
            
            <button 
                className="cta-button"
                onClick={() => handleCtaClick('contact_form')}
            >
                Contact Chef
            </button>
            
            <ContactForm onSubmit={handleFormSubmit} />
        </div>
    );
};
```

---

## 🔍 Troubleshooting

### **Common Issues & Solutions**

#### **1. Events Not Being Tracked**
```javascript
// Check if API endpoint is reachable
const testConnection = async () => {
    try {
        const response = await fetch('/api/v1/public/analytics/track/recipe-view', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                recipe_id: 1,
                organization_id: 'test',
                session_id: 'test',
                event_type: 'recipe_view'
            })
        });
        
        console.log('API Status:', response.status);
        console.log('Response:', await response.text());
    } catch (error) {
        console.error('Connection failed:', error);
    }
};
```

#### **2. Dashboard Not Loading Data**
```sql
-- Check if analytics data exists
SELECT 
    event_type,
    COUNT(*) as event_count,
    DATE(created_at) as event_date
FROM mo_recipe_analytics 
WHERE organization_id = 'your_org_id'
    AND created_at >= NOW() - INTERVAL '30 days'
GROUP BY event_type, DATE(created_at)
ORDER BY event_date DESC;
```

#### **3. Validation Errors**
```javascript
// Ensure all required fields are present
const validateAnalyticsData = (data) => {
    const required = ['recipe_id', 'organization_id'];
    const missing = required.filter(field => !data[field]);
    
    if (missing.length > 0) {
        console.error('Missing required fields:', missing);
        return false;
    }
    
    if (typeof data.recipe_id !== 'number') {
        console.error('recipe_id must be a number');
        return false;
    }
    
    return true;
};
```

#### **4. Performance Issues**
```sql
-- Add database indexes for better performance
CREATE INDEX CONCURRENTLY idx_analytics_org_date 
ON mo_recipe_analytics(organization_id, created_at DESC);

CREATE INDEX CONCURRENTLY idx_analytics_recipe_event 
ON mo_recipe_analytics(entity_id, event_type) 
WHERE entity_type = 'recipe';
```

### **Monitoring & Alerts**

```javascript
// Set up monitoring for analytics health
const monitorAnalytics = async () => {
    const checks = [
        {
            name: 'Event tracking rate',
            check: async () => {
                const count = await getEventCountLastHour();
                return count > 0; // Should have some events
            }
        },
        {
            name: 'Database connection',
            check: async () => {
                try {
                    await sequelize.authenticate();
                    return true;
                } catch (error) {
                    return false;
                }
            }
        },
        {
            name: 'API response time',
            check: async () => {
                const start = Date.now();
                await fetch('/api/health');
                const duration = Date.now() - start;
                return duration < 1000; // Under 1 second
            }
        }
    ];
    
    for (const check of checks) {
        const result = await check.check();
        console.log(`${check.name}: ${result ? 'OK' : 'FAILED'}`);
    }
};

// Run monitoring every 5 minutes
setInterval(monitorAnalytics, 5 * 60 * 1000);
```

---

## 📈 Performance Optimization

### **Database Optimization**
```sql
-- Partitioning by month for large datasets
CREATE TABLE mo_recipe_analytics_2024_01 PARTITION OF mo_recipe_analytics
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Optimize queries with proper indexing
CREATE INDEX idx_analytics_composite 
ON mo_recipe_analytics(organization_id, event_type, created_at DESC)
WHERE created_at >= '2024-01-01';
```

### **Caching Strategy**
```javascript
// Redis caching for dashboard data
const getCachedDashboardData = async (organizationId, dateRange) => {
    const cacheKey = `dashboard:${organizationId}:${dateRange}`;
    
    // Try cache first
    const cached = await redis.get(cacheKey);
    if (cached) {
        return JSON.parse(cached);
    }
    
    // Generate fresh data
    const data = await analyticsService.getDashboardStats(organizationId, dateRange);
    
    // Cache for 5 minutes
    await redis.setex(cacheKey, 300, JSON.stringify(data));
    
    return data;
};
```

### **API Rate Limiting**
```javascript
// Rate limiting for public endpoints
const rateLimit = require('express-rate-limit');

const publicAnalyticsLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 100, // 100 requests per minute per IP
    message: 'Too many analytics requests, please try again later',
    standardHeaders: true,
    legacyHeaders: false,
});

app.use('/api/v1/public/analytics', publicAnalyticsLimiter);
```

---

This comprehensive documentation covers the complete analytics and dashboard system. The system provides powerful insights into user behavior while maintaining excellent performance and reliability. 🚀 