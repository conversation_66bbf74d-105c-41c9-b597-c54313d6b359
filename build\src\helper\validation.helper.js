"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonValidations = exports.ValidationHelper = void 0;
const http_status_codes_1 = require("http-status-codes");
/**
 * Enhanced input validation utility
 */
class ValidationHelper {
    /**
     * Check validation results and return errors if any
     */
    static checkValidation(req, res, next) {
        // Since we're using celebrate for validation, this is a placeholder
        // The actual validation is handled by celebrate middleware
        next();
    }
    /**
     * Validate required string field (basic validation)
     */
    static validateRequiredString(value, field, minLength = 1, maxLength = 255) {
        if (!value || typeof value !== 'string') {
            return `${field} is required`;
        }
        const trimmed = value.trim();
        if (trimmed.length < minLength || trimmed.length > maxLength) {
            return `${field} must be between ${minLength} and ${maxLength} characters`;
        }
        return null;
    }
    /**
     * Validate optional string field (basic validation)
     */
    static validateOptionalString(value, field, maxLength = 255) {
        if (value === null || value === undefined || value === '') {
            return null; // Optional field
        }
        if (typeof value !== 'string') {
            return `${field} must be a string`;
        }
        if (value.trim().length > maxLength) {
            return `${field} must not exceed ${maxLength} characters`;
        }
        return null;
    }
    /**
     * Validate email field (basic validation)
     */
    static validateEmail(value, field = 'email') {
        if (!value || typeof value !== 'string') {
            return `${field} is required`;
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value.trim())) {
            return 'Please provide a valid email address';
        }
        return null;
    }
    /**
     * Validate numeric field (basic validation)
     */
    static validateNumber(value, field, min, max) {
        const num = Number(value);
        if (isNaN(num)) {
            return `${field} must be a valid number`;
        }
        if (min !== undefined && num < min) {
            return `${field} must be at least ${min}`;
        }
        if (max !== undefined && num > max) {
            return `${field} must not exceed ${max}`;
        }
        return null;
    }
    /**
     * Validate positive integer (basic validation)
     */
    static validatePositiveInteger(value, field) {
        const num = Number(value);
        if (isNaN(num) || !Number.isInteger(num) || num <= 0) {
            return `${field} must be a positive integer`;
        }
        return null;
    }
    /**
     * Validate array field (basic validation)
     */
    static validateArray(value, field, minLength = 0, maxLength) {
        if (!Array.isArray(value)) {
            return `${field} must be an array`;
        }
        if (value.length < minLength) {
            return `${field} must have at least ${minLength} items`;
        }
        if (maxLength !== undefined && value.length > maxLength) {
            return `${field} must not exceed ${maxLength} items`;
        }
        return null;
    }
    /**
     * Sanitize HTML content
     */
    static sanitizeHtml(value) {
        if (!value)
            return value;
        // Basic HTML sanitization - remove script tags and dangerous attributes
        return value
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '')
            .trim();
    }
    /**
     * Validate and sanitize user input
     */
    static sanitizeInput(input) {
        if (typeof input === 'string') {
            return this.sanitizeHtml(input);
        }
        if (Array.isArray(input)) {
            return input.map(item => this.sanitizeInput(item));
        }
        if (typeof input === 'object' && input !== null) {
            const sanitized = {};
            for (const [key, value] of Object.entries(input)) {
                sanitized[key] = this.sanitizeInput(value);
            }
            return sanitized;
        }
        return input;
    }
    /**
     * Validate file upload
     */
    static validateFileUpload(field, allowedTypes = [], maxSize) {
        return (req, res, next) => {
            const files = req.files;
            if (!files || !files[field]) {
                return next(); // Optional file
            }
            const file = Array.isArray(files[field]) ? files[field][0] : files[field];
            // Check file type
            if (allowedTypes.length > 0 && !allowedTypes.includes(file.mimetype)) {
                return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                    status: false,
                    message: `Invalid file type. Allowed types: ${allowedTypes.join(', ')}`
                });
            }
            // Check file size
            if (maxSize && file.size > maxSize) {
                return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                    status: false,
                    message: `File size too large. Maximum size: ${maxSize} bytes`
                });
            }
            next();
        };
    }
    /**
     * Validate organization access (uses existing isDefaultAccess function)
     */
    static validateOrganizationAccess() {
        return (req, res, next) => __awaiter(this, void 0, void 0, function* () {
            const user = req.user;
            if (!user) {
                return res.status(http_status_codes_1.StatusCodes.UNAUTHORIZED).json({
                    status: false,
                    message: 'Authentication required'
                });
            }
            // Use existing role-based access check
            const { isDefaultAccess } = yield Promise.resolve().then(() => __importStar(require('../helper/common')));
            const hasDefaultAccess = yield isDefaultAccess(user.id);
            if (!user.organization_id && !hasDefaultAccess) {
                return res.status(http_status_codes_1.StatusCodes.UNAUTHORIZED).json({
                    status: false,
                    message: 'Organization access required'
                });
            }
            next();
        });
    }
    /**
     * Validate admin access (uses existing role-based system)
     */
    static validateAdminAccess() {
        return (req, res, next) => __awaiter(this, void 0, void 0, function* () {
            const user = req.user;
            if (!user) {
                return res.status(http_status_codes_1.StatusCodes.UNAUTHORIZED).json({
                    status: false,
                    message: 'Authentication required'
                });
            }
            // Use existing role-based access check
            const { isDefaultAccess } = yield Promise.resolve().then(() => __importStar(require('../helper/common')));
            const hasDefaultAccess = yield isDefaultAccess(user.id);
            if (!hasDefaultAccess) {
                return res.status(http_status_codes_1.StatusCodes.FORBIDDEN).json({
                    status: false,
                    message: 'Admin access required'
                });
            }
            next();
        });
    }
    /**
     * Get effective organization ID for queries (async to check roles properly)
     * Admin users can specify organization_id in query params or get all data
     * Regular users are restricted to their organization
     */
    static getEffectiveOrganizationId(user, queryOrganizationId) {
        return __awaiter(this, void 0, void 0, function* () {
            // Use existing role-based access check
            const { isDefaultAccess } = yield Promise.resolve().then(() => __importStar(require('../helper/common')));
            const hasDefaultAccess = yield isDefaultAccess(user.id);
            if (hasDefaultAccess) {
                // Admin can specify organization_id in query, or get all data if not specified
                if (queryOrganizationId !== undefined) {
                    return queryOrganizationId === 'null' || queryOrganizationId === '' ? null : queryOrganizationId;
                }
                // If no organization specified in query, return undefined to get all data
                return undefined;
            }
            else {
                // Regular users are restricted to their organization
                return user.organization_id;
            }
        });
    }
    /**
     * Check if user has admin access (async to check roles properly)
     */
    static isAdminUser(user) {
        return __awaiter(this, void 0, void 0, function* () {
            // Use existing role-based access check
            const { isDefaultAccess } = yield Promise.resolve().then(() => __importStar(require('../helper/common')));
            return yield isDefaultAccess(user.id);
        });
    }
    /**
     * Validate organization access for analytics endpoints
     */
    static validateAnalyticsAccess() {
        return (req, res, next) => __awaiter(this, void 0, void 0, function* () {
            const user = req.user;
            if (!user) {
                return res.status(http_status_codes_1.StatusCodes.UNAUTHORIZED).json({
                    status: false,
                    message: 'Authentication required'
                });
            }
            // Use existing role-based access check
            const { isDefaultAccess } = yield Promise.resolve().then(() => __importStar(require('../helper/common')));
            const hasDefaultAccess = yield isDefaultAccess(user.id);
            // For analytics, either admin or user with organization_id
            if (!hasDefaultAccess && !user.organization_id) {
                return res.status(http_status_codes_1.StatusCodes.UNAUTHORIZED).json({
                    status: false,
                    message: 'Organization access required for analytics'
                });
            }
            next();
        });
    }
    /**
     * Rate limiting validation
     */
    static validateRateLimit(maxRequests = 100, windowMs = 15 * 60 * 1000) {
        const requests = new Map();
        return (req, res, next) => {
            const identifier = req.ip || 'unknown';
            const now = Date.now();
            const windowStart = now - windowMs;
            // Clean old entries
            for (const [key, value] of requests.entries()) {
                if (value.resetTime < windowStart) {
                    requests.delete(key);
                }
            }
            // Check current request count
            const current = requests.get(identifier);
            if (!current) {
                requests.set(identifier, { count: 1, resetTime: now });
                return next();
            }
            if (current.count >= maxRequests) {
                return res.status(http_status_codes_1.StatusCodes.TOO_MANY_REQUESTS).json({
                    status: false,
                    message: 'Too many requests. Please try again later.',
                    retryAfter: Math.ceil((current.resetTime + windowMs - now) / 1000)
                });
            }
            current.count++;
            next();
        };
    }
}
exports.ValidationHelper = ValidationHelper;
/**
 * Common validation utilities for different entities
 */
exports.CommonValidations = {
    /**
     * Validate recipe data
     */
    validateRecipeData(data) {
        const errors = [];
        const titleError = ValidationHelper.validateRequiredString(data.recipe_title, 'recipe_title', 2, 100);
        if (titleError)
            errors.push(titleError);
        const descError = ValidationHelper.validateOptionalString(data.recipe_description, 'recipe_description', 1000);
        if (descError)
            errors.push(descError);
        const prepTimeError = ValidationHelper.validateNumber(data.recipe_preparation_time, 'recipe_preparation_time', 0, 1440);
        if (prepTimeError)
            errors.push(prepTimeError);
        const cookTimeError = ValidationHelper.validateNumber(data.recipe_cook_time, 'recipe_cook_time', 0, 1440);
        if (cookTimeError)
            errors.push(cookTimeError);
        return errors;
    },
    /**
     * Validate ingredient data
     */
    validateIngredientData(data) {
        const errors = [];
        const nameError = ValidationHelper.validateRequiredString(data.ingredient_name, 'ingredient_name', 2, 100);
        if (nameError)
            errors.push(nameError);
        const descError = ValidationHelper.validateOptionalString(data.ingredient_description, 'ingredient_description', 500);
        if (descError)
            errors.push(descError);
        const costError = ValidationHelper.validateNumber(data.cost_per_unit, 'cost_per_unit', 0);
        if (costError)
            errors.push(costError);
        const unitError = ValidationHelper.validatePositiveInteger(data.unit_of_measure, 'unit_of_measure');
        if (unitError)
            errors.push(unitError);
        return errors;
    },
    /**
     * Validate category data
     */
    validateCategoryData(data) {
        const errors = [];
        const nameError = ValidationHelper.validateRequiredString(data.category_name, 'category_name', 2, 50);
        if (nameError)
            errors.push(nameError);
        const descError = ValidationHelper.validateOptionalString(data.category_description, 'category_description', 200);
        if (descError)
            errors.push(descError);
        return errors;
    }
};
