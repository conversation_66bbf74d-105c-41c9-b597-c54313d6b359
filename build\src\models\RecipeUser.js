"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecipeUser = exports.RecipeUserStatus = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var RecipeUserStatus;
(function (RecipeUserStatus) {
    RecipeUserStatus["active"] = "active";
    RecipeUserStatus["inactive"] = "inactive";
})(RecipeUserStatus || (exports.RecipeUserStatus = RecipeUserStatus = {}));
class RecipeUser extends sequelize_1.Model {
}
exports.RecipeUser = RecipeUser;
RecipeUser.init({
    recipe_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        references: {
            model: "mo_recipe",
            key: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
    },
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        references: {
            model: "users",
            key: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
    },
    status: {
        type: sequelize_1.DataTypes.ENUM(Object.values(RecipeUserStatus)),
        allowNull: false,
        defaultValue: RecipeUserStatus.active,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_recipe_user",
    modelName: "RecipeUser",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
        {
            unique: true,
            fields: ["recipe_id", "user_id"],
            name: "unique_recipe_user_bookmark",
        },
        {
            fields: ["organization_id"],
            name: "idx_recipe_user_organization",
        },
        {
            fields: ["status"],
            name: "idx_recipe_user_status",
        },
        {
            fields: ["created_by"],
            name: "idx_recipe_user_created_by",
        },
        {
            fields: ["updated_by"],
            name: "idx_recipe_user_updated_by",
        },
    ],
});
// Define associations
RecipeUser.associate = (models) => {
    // RecipeUser belongs to Recipe
    RecipeUser.belongsTo(models.Recipe, {
        foreignKey: "recipe_id",
        as: "recipe",
    });
    // RecipeUser belongs to User (user_id)
    RecipeUser.belongsTo(models.User, {
        foreignKey: "user_id",
        as: "user",
    });
    // RecipeUser belongs to User (created_by)
    RecipeUser.belongsTo(models.User, {
        foreignKey: "created_by",
        as: "creator",
    });
    // RecipeUser belongs to User (updated_by)
    RecipeUser.belongsTo(models.User, {
        foreignKey: "updated_by",
        as: "updater",
    });
};
exports.default = RecipeUser;
