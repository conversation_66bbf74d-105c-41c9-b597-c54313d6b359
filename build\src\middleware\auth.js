"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_codes_1 = require("http-status-codes");
const lodash_1 = __importDefault(require("lodash"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const common_1 = require("../helper/common");
const constant_1 = require("../helper/constant");
const userAuth = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d;
    try {
        /** check Token is present  */
        if (!req.headers.authorization) {
            return res
                .status(http_status_codes_1.StatusCodes.UNAUTHORIZED)
                .send({ status: false, message: res.__("ERROR_TOKEN_REQUIRED") });
        }
        const token = (_b = (_a = req.headers) === null || _a === void 0 ? void 0 : _a.authorization) === null || _b === void 0 ? void 0 : _b.split(" ")[1];
        if (req.headers.authorization) {
            const decoded = jsonwebtoken_1.default.decode(token);
            // Check if token is valid and decoded properly
            if (!decoded || typeof decoded !== "object" || !decoded.user_id) {
                return res
                    .status(http_status_codes_1.StatusCodes.UNAUTHORIZED)
                    .json({ status: false, message: res.__("ERROR_INVALID_TOKEN") });
            }
            req.user = yield (0, common_1.getUser)(decoded.user_id, true);
            if (!((_c = req === null || req === void 0 ? void 0 : req.user) === null || _c === void 0 ? void 0 : _c.id)) {
                return res
                    .status(http_status_codes_1.StatusCodes.UNAUTHORIZED)
                    .json({ status: false, message: res.__("ERROR_INVALID_TOKEN") });
            }
            const userRoles = yield (0, common_1.getUserAllRoles)((_d = req === null || req === void 0 ? void 0 : req.user) === null || _d === void 0 ? void 0 : _d.id);
            const loginUserRoles = userRoles.length > 0
                ? lodash_1.default.map(userRoles, (userRole) => userRole.role_name)
                : [];
            const normalUser = [...constant_1.NORMAL_USER];
            const adminSideUser = [...constant_1.ADMIN_SIDE_USER];
            if (!loginUserRoles.some((item) => adminSideUser.includes(item)) &&
                req.headers["platform-type"] == "web") {
                return res
                    .status(http_status_codes_1.StatusCodes.UNAUTHORIZED)
                    .json({ status: false, message: res.__("ERROR_INVALID_TOKEN") });
            }
            if (!loginUserRoles.some((item) => normalUser.includes(item)) &&
                (req.headers["platform-type"] == "ios" ||
                    req.headers["platform-type"] == "android")) {
                return res
                    .status(http_status_codes_1.StatusCodes.UNAUTHORIZED)
                    .json({ status: false, message: res.__("ERROR_INVALID_TOKEN") });
            }
            if (userRoles.length > 0) {
                const roleData = userRoles[0];
                if (process.env.NEXT_NODE_ENV !== "staging" && roleData.id !== 1) {
                    let deviceType = req.headers["platform-type"];
                    deviceType = deviceType == "ios" ? "android" : deviceType;
                    const session = yield (0, common_1.getUserSession)(token, deviceType);
                    if (!session) {
                        return res
                            .status(http_status_codes_1.StatusCodes.UNAUTHORIZED)
                            .json({ status: false, message: res.__("ERROR_INVALID_TOKEN") });
                    }
                }
            }
            req.user.roles = userRoles;
            next();
        }
    }
    catch (e) {
        if (e.message == "jwt malformed") {
            return res
                .status(http_status_codes_1.StatusCodes.UNAUTHORIZED)
                .send({ status: false, message: res.__("ERROR_TOKEN_NOT_FOUND") });
        }
        else {
            if (e.name == "TokenExpiredError") {
                return res.status(http_status_codes_1.StatusCodes.UNAUTHORIZED).send({
                    status: false,
                    message: res.__("ERROR_INVALID_TOKEN"),
                });
            }
            else {
                return res.status(401).send({ status: false, message: e.message });
            }
        }
    }
});
exports.default = userAuth;
