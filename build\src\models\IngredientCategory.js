"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IngredientCategory = exports.IngredientCategoryStatus = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var IngredientCategoryStatus;
(function (IngredientCategoryStatus) {
    IngredientCategoryStatus["active"] = "active";
    IngredientCategoryStatus["inactive"] = "inactive";
})(IngredientCategoryStatus || (exports.IngredientCategoryStatus = IngredientCategoryStatus = {}));
class IngredientCategory extends sequelize_1.Model {
}
exports.IngredientCategory = IngredientCategory;
IngredientCategory.init({
    category_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        references: {
            model: "mo_category",
            key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
    },
    ingredient_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        references: {
            model: "mo_ingredients",
            key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
    },
    ingredient_category_status: {
        type: sequelize_1.DataTypes.ENUM,
        values: Object.values(IngredientCategoryStatus),
        defaultValue: IngredientCategoryStatus.active,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_ingredients_category",
    modelName: "IngredientCategory",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
        {
            unique: true,
            fields: ["category_id", "ingredient_id"],
            name: "primary_ingredient_category",
        },
        {
            fields: ["organization_id"],
            name: "idx_ingredient_category_organization",
        },
        {
            fields: ["ingredient_category_status"],
            name: "idx_ingredient_category_status",
        },
    ],
});
// Define associations
IngredientCategory.associate = (models) => {
    // IngredientCategory belongs to Ingredient
    IngredientCategory.belongsTo(models.Ingredient, {
        foreignKey: "ingredient_id",
        as: "ingredient",
    });
    // IngredientCategory belongs to Category
    IngredientCategory.belongsTo(models.Category, {
        foreignKey: "category_id",
        as: "category",
    });
};
