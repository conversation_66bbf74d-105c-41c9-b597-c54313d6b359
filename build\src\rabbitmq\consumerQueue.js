"use strict";
// import rabbitMQ from "./rabbitmq";
// import { RABBITMQ_QUEUE } from "../helper/constant";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupConsumers = void 0;
const setupConsumers = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log("Setting up RabbitMQ consumers...");
        // Add your consumer logic here
        // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.NOTIFICATION_SUCCESS);
        // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.PLAN_PAYMENT_SUCCESS);
        // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.PLAN_PAYMENT_FAILED);
        // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.PLAN_PAYMENT_UPGRADE_FAILED);
        // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.PLAN_PAYMENT_UPGRADE_SUCCESS);
        // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.PLAN_PUBLIC_PAYMENT_FAILED);
        // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.PLAN_PUBLIC_PAYMENT_SUCCESS);
        // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.SUBSCRIPTION_EXPIRY);
        console.log("RabbitMQ consumers are set up successfully.");
    }
    catch (error) {
        console.error("Error setting up RabbitMQ consumers:", error);
    }
});
exports.setupConsumers = setupConsumers;
