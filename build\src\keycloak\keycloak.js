"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.memoryStore = exports.keycloak = void 0;
const keycloak_connect_1 = __importDefault(require("keycloak-connect"));
const express_session_1 = __importDefault(require("express-session"));
// Session Store
const memoryStore = new express_session_1.default.MemoryStore();
exports.memoryStore = memoryStore;
// Keycloak configuration
const keycloakConfig = {
    clientId: global.config.KEYCLOAK_CLIENT_ID,
    bearerOnly: true, // backend-only authentication
    serverUrl: global.config.KEYCLOAK_SERVER_URL,
    realm: global.config.KEYCLOAK_REALM_NAME,
    credentials: {
        secret: global.config.KEYCLOAK_SECRET_KEY, // Your client secret
    },
};
// Initialize Keycloak
const keycloak = new keycloak_connect_1.default({ store: memoryStore }, keycloakConfig);
exports.keycloak = keycloak;
