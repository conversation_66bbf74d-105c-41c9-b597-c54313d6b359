"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validationMessages = exports.validateSettingValue = exports.specificSettingValidations = void 0;
const celebrate_1 = require("celebrate");
// Setting key validation patterns
const settingKeyPattern = /^[a-z_]+\.[a-z_]+$/;
// Common setting value types
const settingValueSchema = celebrate_1.Joi.alternatives().try(celebrate_1.Joi.string(), celebrate_1.Joi.number(), celebrate_1.Joi.boolean(), celebrate_1.Joi.object(), celebrate_1.Joi.array());
// Get settings by category validator
const getSettingsByCategoryValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: celebrate_1.Joi.object({
        category: celebrate_1.Joi.string()
            .valid("recipe", "dashboard", "public", "analytics", "system")
            .optional(),
    }),
});
// Get single setting by key validator
const getSettingByKeyValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object({
        key: celebrate_1.Joi.string().pattern(settingKeyPattern).required().messages({
            "string.pattern.base": 'Setting key must be in format "category.setting_name"',
        }),
    }),
});
// Update multiple settings validator
const updateSettingsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object()
        .pattern(celebrate_1.Joi.string().pattern(settingKeyPattern), settingValueSchema)
        .min(1)
        .required()
        .messages({
        "object.min": "At least one setting must be provided",
    }),
});
// Update single setting validator
const updateSettingValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object({
        key: celebrate_1.Joi.string().pattern(settingKeyPattern).required(),
    }),
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object({
        value: settingValueSchema.required(),
    }),
});
// Reset settings validator
const resetSettingsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object({
        category: celebrate_1.Joi.string()
            .valid("recipe", "dashboard", "public", "analytics", "system")
            .optional(),
    }),
});
// Specific setting validations - matching UI exactly
exports.specificSettingValidations = {
    // Recipe settings
    "recipe.public_store_enabled": celebrate_1.Joi.boolean(),
    "recipe.highlight_changes": celebrate_1.Joi.boolean(),
    // CTA Settings
    "recipe.cta_contact_form": celebrate_1.Joi.boolean(),
    "recipe.cta_contact_info": celebrate_1.Joi.boolean(),
    "recipe.cta_custom_link": celebrate_1.Joi.boolean(),
    "recipe.cta_none": celebrate_1.Joi.boolean(),
    // Contact Info Fields
    "recipe.contact_info_name": celebrate_1.Joi.string().allow("").max(100),
    "recipe.contact_info_phone": celebrate_1.Joi.string().allow("").max(20),
    "recipe.contact_info_email": celebrate_1.Joi.string().allow("").email().max(255),
    "recipe.contact_info_link": celebrate_1.Joi.string().allow("").uri().max(500),
    // Custom CTA Fields
    "recipe.custom_cta_text": celebrate_1.Joi.string().allow("").max(100),
    "recipe.custom_cta_link": celebrate_1.Joi.string().allow("").uri().max(500),
    // Display settings - exactly matching UI
    "recipe.display_category": celebrate_1.Joi.boolean(),
    "recipe.display_ingredients": celebrate_1.Joi.boolean(),
    "recipe.display_nutritional_information": celebrate_1.Joi.boolean(),
    "recipe.display_allergen_information": celebrate_1.Joi.boolean(),
    "recipe.display_preparation_steps": celebrate_1.Joi.boolean(),
    "recipe.display_total_time": celebrate_1.Joi.boolean(),
    "recipe.display_yield_portioning": celebrate_1.Joi.boolean(),
    "recipe.display_cost": celebrate_1.Joi.boolean(),
    "recipe.display_dietary_suitability": celebrate_1.Joi.boolean(),
    "recipe.display_cuisine_type": celebrate_1.Joi.boolean(),
    "recipe.display_media": celebrate_1.Joi.boolean(),
    "recipe.display_links": celebrate_1.Joi.boolean(),
    "recipe.display_scale": celebrate_1.Joi.boolean(),
    "recipe.display_serve_in": celebrate_1.Joi.boolean(),
    "recipe.display_garnish": celebrate_1.Joi.boolean(),
    // Dashboard settings
    "dashboard.refresh_interval": celebrate_1.Joi.number().min(30000).max(3600000), // 30 seconds to 1 hour
    "dashboard.default_date_range": celebrate_1.Joi.string().valid("last_7_days", "last_30_days", "last_90_days", "last_year"),
    "dashboard.public_analytics_enabled": celebrate_1.Joi.boolean(),
    // Analytics settings
    "analytics.track_anonymous_views": celebrate_1.Joi.boolean(),
    "analytics.retention_days": celebrate_1.Joi.number().min(1).max(2555), // 1 day to 7 years
    "analytics.enable_detailed_tracking": celebrate_1.Joi.boolean(),
    // System settings (usually read-only for org admins)
    "system.max_file_size": celebrate_1.Joi.number().min(1024).max(104857600), // 1KB to 100MB
    "system.allowed_file_types": celebrate_1.Joi.array().items(celebrate_1.Joi.string()),
    "system.backup_enabled": celebrate_1.Joi.boolean(),
};
// Validate setting value based on key
const validateSettingValue = (key, value) => {
    const validator = exports.specificSettingValidations[key];
    if (validator) {
        return validator.validate(value);
    }
    // Default validation for unknown settings
    return settingValueSchema.validate(value);
};
exports.validateSettingValue = validateSettingValue;
// Common validation messages
exports.validationMessages = {
    INVALID_SETTING_KEY: 'Setting key must be in format "category.setting_name"',
    INVALID_SETTING_VALUE: "Setting value is invalid for the specified key",
    SETTING_NOT_FOUND: "Setting not found",
    CATEGORY_REQUIRED: "Category is required",
    VALUE_REQUIRED: "Value is required",
    SETTINGS_REQUIRED: "At least one setting must be provided",
};
// Recipe configuration settings validator
const publicRecipeCallToActionSchema = celebrate_1.Joi.object({
    contactForm: celebrate_1.Joi.boolean().optional(),
    contactInfo: celebrate_1.Joi.object({
        enabled: celebrate_1.Joi.boolean().optional(),
        name: celebrate_1.Joi.string().allow("").max(100).optional(),
        phone: celebrate_1.Joi.string().allow("").max(20).optional(),
        email: celebrate_1.Joi.string().allow("").email().max(255).optional(),
        link: celebrate_1.Joi.string().allow("").uri().max(500).optional(),
    }).optional(),
    customCtaLink: celebrate_1.Joi.object({
        enabled: celebrate_1.Joi.boolean().optional(),
        text: celebrate_1.Joi.string().allow("").max(100).optional(),
        link: celebrate_1.Joi.string().allow("").uri().max(500).optional(),
    }).optional(),
    none: celebrate_1.Joi.boolean().optional(),
})
    .custom((value, helpers) => {
    var _a, _b;
    // Determine which CTA options are enabled (truthy)
    const enabledFlags = [
        value === null || value === void 0 ? void 0 : value.contactForm,
        (_a = value === null || value === void 0 ? void 0 : value.contactInfo) === null || _a === void 0 ? void 0 : _a.enabled,
        (_b = value === null || value === void 0 ? void 0 : value.customCtaLink) === null || _b === void 0 ? void 0 : _b.enabled,
        value === null || value === void 0 ? void 0 : value.none,
    ].filter(Boolean);
    if (enabledFlags.length > 1) {
        return helpers.error("any.exclusiveCTA");
    }
    return value;
})
    .messages({
    "any.exclusiveCTA": "Only one Call-To-Action option can be enabled at a time.",
});
// Recipe configuration settings validator
const recipeConfigurationValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object({
        privateRecipeVisibilitySettings: celebrate_1.Joi.object({
            highlightChanges: celebrate_1.Joi.boolean().optional(),
        }).optional(),
        publicRecipeSettings: celebrate_1.Joi.object({
            publicStoreAccess: celebrate_1.Joi.boolean().optional(),
        }).optional(),
        publicRecipeCallToAction: publicRecipeCallToActionSchema.optional(),
        recipeDetailsToDisplayPublicly: celebrate_1.Joi.object({
            category: celebrate_1.Joi.boolean().optional(),
            ingredients: celebrate_1.Joi.boolean().optional(),
            nutritionalInformation: celebrate_1.Joi.boolean().optional(),
            allergenInformation: celebrate_1.Joi.boolean().optional(),
            preparationSteps: celebrate_1.Joi.boolean().optional(),
            totalTime: celebrate_1.Joi.boolean().optional(),
            yieldPortioning: celebrate_1.Joi.boolean().optional(),
            cost: celebrate_1.Joi.boolean().optional(),
            dietarySuitability: celebrate_1.Joi.boolean().optional(),
            cuisineType: celebrate_1.Joi.boolean().optional(),
            media: celebrate_1.Joi.boolean().optional(),
            links: celebrate_1.Joi.boolean().optional(),
            scale: celebrate_1.Joi.boolean().optional(),
            serveIn: celebrate_1.Joi.boolean().optional(),
            garnish: celebrate_1.Joi.boolean().optional(),
        }).optional(),
    }),
});
exports.default = {
    getSettingsByCategoryValidator,
    getSettingByKeyValidator,
    updateSettingsValidator,
    updateSettingValidator,
    resetSettingsValidator,
    recipeConfigurationValidator,
};
