"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_codes_1 = require("http-status-codes");
const analytics_service_1 = __importDefault(require("../services/analytics.service"));
const index_1 = require("../models/index");
const transaction_helper_1 = require("../helper/transaction.helper");
const validation_helper_1 = require("../helper/validation.helper");
const analytics_controller_1 = __importDefault(require("./analytics.controller"));
/**
 * Get dashboard overview statistics - ROBUST VERSION
 * @route GET /api/v1/private/dashboard/overview
 * @access Private (Authenticated users)
 */
const getDashboardOverview = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Input validation and sanitization
        const sanitizedQuery = validation_helper_1.ValidationHelper.sanitizeInput(req.query);
        const { date_range = "last_30_days", category_type } = sanitizedQuery;
        // Get effective organization ID (handles admin users)
        const effectiveOrganizationId = yield validation_helper_1.ValidationHelper.getEffectiveOrganizationId(req.user, sanitizedQuery.organization_id);
        // Validate date_range parameter
        const validDateRanges = [
            "last_7_days",
            "last_30_days",
            "last_90_days",
            "custom",
        ];
        if (!validDateRanges.includes(date_range)) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: "Invalid date_range. Must be one of: " + validDateRanges.join(", "),
            });
        }
        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error("Analytics service timeout")), 10000); // 10 second timeout
        });
        // Get dashboard stats from analytics service with timeout
        const stats = yield Promise.race([
            analytics_service_1.default.getDashboardStats(effectiveOrganizationId || undefined, date_range, category_type),
            timeoutPromise,
        ]);
        const overviewData = {
            // Core Statistics - Dashboard Cards (matching your UI design)
            stats: {
                totalRecipes: stats.totalRecipes,
                topCategory: stats.topCategory,
            },
            // Analytics Statistics
            analytics: {
                totalViews: stats.totalViews,
                totalBookmarks: stats.totalBookmarks,
                totalShares: stats.totalShares,
                totalContactSubmissions: stats.totalContactSubmissions,
            },
            // Dashboard Charts Data (matching your UI design)
            charts: {
                recipeViewsTrend: stats.recipeViewsTrend, // Line chart
                categoryPerformance: stats.categoryPerformance, // Bar chart
                userEngagementHeatmap: stats.userEngagementHeatmap, // Heatmap
                conversionFunnel: stats.conversionFunnel, // Conversion analytics
            },
            // Recent Activity
            recentActivity: stats.recentActivity,
        };
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: "Dashboard overview fetched successfully",
            data: overviewData,
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        console.error("Dashboard overview error:", error);
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: "Failed to fetch dashboard overview",
            error: process.env.NODE_ENV === "development"
                ? error.message
                : "Internal server error",
            timestamp: new Date().toISOString(),
        });
    }
});
/**
 * Get system health status
 * @route GET /api/v1/private/dashboard/health
 */
const getSystemHealth = (_req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const healthChecks = {
            database: false,
            storage: false,
            analytics: false,
            overall: false,
        };
        // Check database connection
        try {
            yield index_1.sequelize.authenticate();
            healthChecks.database = true;
        }
        catch (error) {
            console.error("Database health check failed:", error);
        }
        // Check storage (basic check)
        try {
            // This would check your file storage system
            healthChecks.storage = true;
        }
        catch (error) {
            console.error("Storage health check failed:", error);
        }
        // Check analytics
        try {
            yield analytics_service_1.default.getAnalytics({ limit: 1 });
            healthChecks.analytics = true;
        }
        catch (error) {
            console.error("Analytics health check failed:", error);
        }
        // Overall health
        healthChecks.overall =
            healthChecks.database && healthChecks.storage && healthChecks.analytics;
        const status = healthChecks.overall
            ? http_status_codes_1.StatusCodes.OK
            : http_status_codes_1.StatusCodes.SERVICE_UNAVAILABLE;
        return res.status(status).json({
            status: healthChecks.overall,
            message: healthChecks.overall ? "System is healthy" : "System has issues",
            data: {
                checks: healthChecks,
                timestamp: new Date().toISOString(),
            },
        });
    }
    catch (error) {
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: "Health check failed",
            error: process.env.NODE_ENV === "development" ? error.message : undefined,
        });
    }
});
/**
 * Export dashboard data - ENHANCED VERSION WITH TIMEOUT AND BETTER ERROR HANDLING
 * @route GET /api/v1/private/dashboard/export
 */
const exportDashboardData = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Input validation and sanitization
        const sanitizedQuery = validation_helper_1.ValidationHelper.sanitizeInput(req.query);
        const { format = "json", date_range = "last_30_days" } = sanitizedQuery;
        // Get effective organization ID (handles admin users)
        const effectiveOrganizationId = yield validation_helper_1.ValidationHelper.getEffectiveOrganizationId(req.user, sanitizedQuery.organization_id);
        // Add timeout to prevent hanging (same as dashboard overview)
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error("Export service timeout")), 15000); // 15 second timeout for export
        });
        // Get dashboard stats with timeout protection
        const dashboardData = yield Promise.race([
            analytics_service_1.default.getDashboardStats(effectiveOrganizationId || undefined, date_range),
            timeoutPromise,
        ]);
        if (format === "csv") {
            // Convert to CSV format with corrected data mapping
            const csv = convertToCSV(dashboardData, date_range);
            res.setHeader("Content-Type", "text/csv");
            res.setHeader("Content-Disposition", `attachment; filename=dashboard-export-${new Date().toISOString().split("T")[0]}.csv`);
            return res.send(csv);
        }
        // Default JSON export
        res.setHeader("Content-Type", "application/json");
        res.setHeader("Content-Disposition", `attachment; filename=dashboard-export-${new Date().toISOString().split("T")[0]}.json`);
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("DASHBOARD_DATA_EXPORTED_SUCCESSFULLY"),
            data: dashboardData,
            exportedAt: new Date().toISOString(),
            dateRange: date_range,
            organizationId: effectiveOrganizationId,
        });
    }
    catch (error) {
        console.error("Dashboard export error:", error);
        // Handle specific error types
        if (error.message === "Export service timeout") {
            return res.status(http_status_codes_1.StatusCodes.REQUEST_TIMEOUT).json({
                status: false,
                message: "Export request timed out. Please try again or contact support.",
                errorType: "TIMEOUT_ERROR",
            });
        }
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error exporting dashboard data");
    }
});
// Helper function to convert data to CSV - CORRECTED VERSION
function convertToCSV(data, dateRange = "last_30_days") {
    var _a, _b, _c, _d, _e, _f, _g;
    const headers = ["Metric", "Value"];
    // Use the actual data structure returned by getDashboardStats
    const rows = [
        // Export metadata
        ["Export Date", new Date().toISOString()],
        ["Date Range", dateRange],
        ["", ""], // Empty row for separation
        // Core Business Metrics
        ["Total Recipes", data.totalRecipes || 0],
        ["Top Category Name", ((_a = data.topCategory) === null || _a === void 0 ? void 0 : _a.name) || "No Data"],
        ["Top Category Count", ((_b = data.topCategory) === null || _b === void 0 ? void 0 : _b.count) || 0],
        ["", ""], // Empty row for separation
        // Recipe Analytics
        ["Total Views", data.totalViews || 0],
        ["Total Bookmarks", data.totalBookmarks || 0],
        ["Total Shares", data.totalShares || 0],
        ["Total Contact Submissions", data.totalContactSubmissions || 0],
        ["", ""], // Empty row for separation
        // Chart Data Summary
        ["Recipe Views Trend Data Points", ((_c = data.recipeViewsTrend) === null || _c === void 0 ? void 0 : _c.length) || 0],
        ["Category Performance Data Points", ((_d = data.categoryPerformance) === null || _d === void 0 ? void 0 : _d.length) || 0],
        [
            "User Engagement Heatmap Data Points",
            ((_e = data.userEngagementHeatmap) === null || _e === void 0 ? void 0 : _e.length) || 0,
        ],
        ["Conversion Funnel Data Points", ((_f = data.conversionFunnel) === null || _f === void 0 ? void 0 : _f.length) || 0],
        ["Recent Activity Items", ((_g = data.recentActivity) === null || _g === void 0 ? void 0 : _g.length) || 0],
    ];
    const csvContent = [
        `# Dashboard Export - ${new Date().toISOString()}`,
        `# Date Range: ${dateRange}`,
        `# Generated by Recipe Management System`,
        "",
        headers.join(","),
        ...rows.map((row) => row.join(",")),
    ].join("\n");
    return csvContent;
}
/**
 * Get CTA analytics for dashboard - Wrapper for analytics controller
 * @route GET /api/v1/private/dashboard/cta-analytics
 */
const getCtaAnalytics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    // Reuse existing analytics controller logic
    return analytics_controller_1.default.getCtaClickAnalytics(req, res);
});
/**
 * Get contact analytics for dashboard - Wrapper for analytics controller
 * @route GET /api/v1/private/dashboard/contact-analytics
 */
const getContactAnalytics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    // Reuse existing analytics controller logic
    return analytics_controller_1.default.getContactSubmissionAnalytics(req, res);
});
exports.default = {
    getDashboardOverview,
    getSystemHealth,
    exportDashboardData,
    getCtaAnalytics,
    getContactAnalytics,
};
