"use strict";
/**
 * Helper functions for recipe highlight feature
 * Analyzes RecipeHistory table to determine what was last updated in recipes
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.hasRecentChanges = exports.getBulkRecipeHighlights = exports.getRecipeHighlight = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("../models/index");
const RecipeHistory_1 = require("../models/RecipeHistory");
const common_1 = require("./common");
// Component types that can be highlighted in a recipe
const RecipeComponentType = {
    BASIC_INFO: "basic_info",
    INGREDIENTS: "ingredients",
    STEPS: "steps",
    CATEGORIES: "categories",
    ATTRIBUTES: "attributes",
    RESOURCES: "resources",
    STATUS: "status",
    GENERAL: "general"
};
// Highlight priority levels for different types of changes
const HighlightPriority = {
    LOW: 1,
    MEDIUM: 2,
    HIGH: 3,
    CRITICAL: 4
};
// Mapping of RecipeHistoryAction to component types
const ACTION_TO_COMPONENT_MAP = {
    // Basic recipe information
    [RecipeHistory_1.RecipeHistoryAction.created]: RecipeComponentType.STATUS,
    [RecipeHistory_1.RecipeHistoryAction.updated]: RecipeComponentType.BASIC_INFO,
    [RecipeHistory_1.RecipeHistoryAction.published]: RecipeComponentType.STATUS,
    [RecipeHistory_1.RecipeHistoryAction.archived]: RecipeComponentType.STATUS,
    [RecipeHistory_1.RecipeHistoryAction.restored]: RecipeComponentType.STATUS,
    [RecipeHistory_1.RecipeHistoryAction.deleted]: RecipeComponentType.STATUS,
    // Ingredients
    [RecipeHistory_1.RecipeHistoryAction.ingredient_added]: RecipeComponentType.INGREDIENTS,
    [RecipeHistory_1.RecipeHistoryAction.ingredient_removed]: RecipeComponentType.INGREDIENTS,
    [RecipeHistory_1.RecipeHistoryAction.ingredient_updated]: RecipeComponentType.INGREDIENTS,
    // Steps
    [RecipeHistory_1.RecipeHistoryAction.step_added]: RecipeComponentType.STEPS,
    [RecipeHistory_1.RecipeHistoryAction.step_removed]: RecipeComponentType.STEPS,
    [RecipeHistory_1.RecipeHistoryAction.step_updated]: RecipeComponentType.STEPS,
    // Categories
    [RecipeHistory_1.RecipeHistoryAction.category_added]: RecipeComponentType.CATEGORIES,
    [RecipeHistory_1.RecipeHistoryAction.category_removed]: RecipeComponentType.CATEGORIES,
    // Attributes
    [RecipeHistory_1.RecipeHistoryAction.attribute_added]: RecipeComponentType.ATTRIBUTES,
    [RecipeHistory_1.RecipeHistoryAction.attribute_removed]: RecipeComponentType.ATTRIBUTES,
    // Resources
    [RecipeHistory_1.RecipeHistoryAction.resource_added]: RecipeComponentType.RESOURCES,
    [RecipeHistory_1.RecipeHistoryAction.resource_removed]: RecipeComponentType.RESOURCES,
    // Bookmarks (not highlighted as they're user-specific)
    [RecipeHistory_1.RecipeHistoryAction.bookmark_added]: RecipeComponentType.GENERAL,
    [RecipeHistory_1.RecipeHistoryAction.bookmark_removed]: RecipeComponentType.GENERAL,
};
// Priority mapping for different actions
const ACTION_PRIORITY_MAP = {
    // High priority - structural changes
    [RecipeHistory_1.RecipeHistoryAction.created]: HighlightPriority.HIGH,
    [RecipeHistory_1.RecipeHistoryAction.published]: HighlightPriority.HIGH,
    [RecipeHistory_1.RecipeHistoryAction.archived]: HighlightPriority.CRITICAL,
    [RecipeHistory_1.RecipeHistoryAction.deleted]: HighlightPriority.CRITICAL,
    [RecipeHistory_1.RecipeHistoryAction.restored]: HighlightPriority.HIGH,
    // Medium priority - content changes
    [RecipeHistory_1.RecipeHistoryAction.updated]: HighlightPriority.MEDIUM,
    [RecipeHistory_1.RecipeHistoryAction.ingredient_added]: HighlightPriority.MEDIUM,
    [RecipeHistory_1.RecipeHistoryAction.ingredient_removed]: HighlightPriority.MEDIUM,
    [RecipeHistory_1.RecipeHistoryAction.ingredient_updated]: HighlightPriority.MEDIUM,
    [RecipeHistory_1.RecipeHistoryAction.step_added]: HighlightPriority.MEDIUM,
    [RecipeHistory_1.RecipeHistoryAction.step_removed]: HighlightPriority.MEDIUM,
    [RecipeHistory_1.RecipeHistoryAction.step_updated]: HighlightPriority.MEDIUM,
    // Low priority - metadata changes
    [RecipeHistory_1.RecipeHistoryAction.category_added]: HighlightPriority.LOW,
    [RecipeHistory_1.RecipeHistoryAction.category_removed]: HighlightPriority.LOW,
    [RecipeHistory_1.RecipeHistoryAction.attribute_added]: HighlightPriority.LOW,
    [RecipeHistory_1.RecipeHistoryAction.attribute_removed]: HighlightPriority.LOW,
    [RecipeHistory_1.RecipeHistoryAction.resource_added]: HighlightPriority.LOW,
    [RecipeHistory_1.RecipeHistoryAction.resource_removed]: HighlightPriority.LOW,
    // Very low priority - user actions
    [RecipeHistory_1.RecipeHistoryAction.bookmark_added]: HighlightPriority.LOW,
    [RecipeHistory_1.RecipeHistoryAction.bookmark_removed]: HighlightPriority.LOW,
};
// Component display names for UI
const COMPONENT_DISPLAY_NAMES = {
    [RecipeComponentType.BASIC_INFO]: "Basic Information",
    [RecipeComponentType.INGREDIENTS]: "Ingredients",
    [RecipeComponentType.STEPS]: "Preparation Steps",
    [RecipeComponentType.CATEGORIES]: "Categories",
    [RecipeComponentType.ATTRIBUTES]: "Attributes",
    [RecipeComponentType.RESOURCES]: "Resources",
    [RecipeComponentType.STATUS]: "Recipe Status",
    [RecipeComponentType.GENERAL]: "General",
};
// Actions that should be excluded from highlighting (e.g., bookmarks are user-specific)
const EXCLUDED_HIGHLIGHT_ACTIONS = [
    RecipeHistory_1.RecipeHistoryAction.bookmark_added,
    RecipeHistory_1.RecipeHistoryAction.bookmark_removed,
];
// Default highlight configuration
const DEFAULT_HIGHLIGHT_CONFIG = {
    maxHighlightAgeDays: 30, // Show highlights for changes within last 30 days
    includeUserActions: false, // Don't include bookmarks in highlights
    minPriorityLevel: HighlightPriority.LOW, // Show all priority levels
};
/**
 * Get the most recent highlight for a single recipe
 */
const getRecipeHighlight = (recipeId_1, organizationId_1, ...args_1) => __awaiter(void 0, [recipeId_1, organizationId_1, ...args_1], void 0, function* (recipeId, organizationId, config = DEFAULT_HIGHLIGHT_CONFIG) {
    try {
        // Calculate the cutoff date for highlights
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - config.maxHighlightAgeDays);
        // Build where conditions
        const whereConditions = {
            recipe_id: recipeId,
            created_at: {
                [sequelize_1.Op.gte]: cutoffDate,
            },
        };
        if (organizationId) {
            whereConditions.organization_id = organizationId;
        }
        // Exclude certain actions if configured
        if (!config.includeUserActions) {
            whereConditions.action = {
                [sequelize_1.Op.notIn]: EXCLUDED_HIGHLIGHT_ACTIONS,
            };
        }
        // Get the most recent history entry
        const latestHistory = yield RecipeHistory_1.RecipeHistory.findOne({
            where: whereConditions,
            order: [["created_at", "DESC"]],
            limit: 1,
        });
        if (!latestHistory) {
            return null;
        }
        // Check if the action meets minimum priority level
        const actionPriority = ACTION_PRIORITY_MAP[latestHistory.action];
        if (actionPriority < config.minPriorityLevel) {
            return null;
        }
        // Get user information
        const userInfo = yield (0, common_1.getUser)(latestHistory.created_by);
        // Map action to component type
        const component = ACTION_TO_COMPONENT_MAP[latestHistory.action];
        // Create highlight object
        const highlight = {
            component,
            action: latestHistory.action,
            description: latestHistory.description || generateDefaultDescription(latestHistory.action, component),
            lastModified: latestHistory.created_at,
            modifiedBy: {
                userId: latestHistory.created_by,
                userName: (userInfo === null || userInfo === void 0 ? void 0 : userInfo.user_full_name) || (userInfo === null || userInfo === void 0 ? void 0 : userInfo.user_first_name) || (userInfo === null || userInfo === void 0 ? void 0 : userInfo.user_email) || "Unknown User",
            },
            priority: actionPriority,
            context: {
                fieldName: latestHistory.field_name,
            },
        };
        return highlight;
    }
    catch (error) {
        console.error("Error getting recipe highlight:", error);
        return null;
    }
});
exports.getRecipeHighlight = getRecipeHighlight;
/**
 * Get highlights for multiple recipes efficiently
 */
const getBulkRecipeHighlights = (recipeIds_1, organizationId_1, ...args_1) => __awaiter(void 0, [recipeIds_1, organizationId_1, ...args_1], void 0, function* (recipeIds, organizationId, config = DEFAULT_HIGHLIGHT_CONFIG) {
    try {
        if (recipeIds.length === 0) {
            return {
                highlights: {},
                metadata: {
                    totalRecipes: 0,
                    recipesWithHighlights: 0,
                    queryTimestamp: new Date(),
                },
            };
        }
        // Calculate the cutoff date for highlights
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - config.maxHighlightAgeDays);
        // Build the SQL query to get the most recent history entry for each recipe
        const excludedActions = config.includeUserActions ? [] : EXCLUDED_HIGHLIGHT_ACTIONS;
        const excludedActionsClause = excludedActions.length > 0
            ? `AND rh.action NOT IN (${excludedActions.map(a => `'${a}'`).join(', ')})`
            : '';
        const organizationClause = organizationId
            ? `AND rh.organization_id = :organizationId`
            : '';
        const query = `
      SELECT 
        rh.recipe_id,
        rh.action,
        rh.description,
        rh.field_name,
        rh.created_at,
        rh.created_by,
        ROW_NUMBER() OVER (PARTITION BY rh.recipe_id ORDER BY rh.created_at DESC) as rn
      FROM mo_recipe_history rh
      WHERE rh.recipe_id IN (${recipeIds.map((id) => `${id}`).join(', ')})
        AND rh.created_at >= :cutoffDate
        ${excludedActionsClause}
        ${organizationClause}
    `;
        const results = yield index_1.sequelize.query(query, {
            type: sequelize_1.QueryTypes.SELECT,
            replacements: Object.assign({ cutoffDate: cutoffDate.toISOString() }, (organizationId && { organizationId })),
        });
        // Filter to get only the most recent entry per recipe and apply priority filter
        const latestEntries = results.filter((row) => {
            if (row.rn !== 1)
                return false; // Only the most recent entry
            const actionPriority = ACTION_PRIORITY_MAP[row.action];
            return actionPriority >= config.minPriorityLevel;
        });
        // Get user information for all unique user IDs
        const userIds = [...new Set(latestEntries.map((entry) => entry.created_by))];
        const userInfoMap = new Map();
        for (const userId of userIds) {
            const userInfo = yield (0, common_1.getUser)(userId);
            userInfoMap.set(userId, userInfo);
        }
        // Build highlights map
        const highlights = {};
        for (const entry of latestEntries) {
            const userInfo = userInfoMap.get(entry.created_by);
            const component = ACTION_TO_COMPONENT_MAP[entry.action];
            const actionPriority = ACTION_PRIORITY_MAP[entry.action];
            highlights[entry.recipe_id] = {
                component,
                action: entry.action,
                description: entry.description || generateDefaultDescription(entry.action, component),
                lastModified: new Date(entry.created_at),
                modifiedBy: {
                    userId: entry.created_by,
                    userName: (userInfo === null || userInfo === void 0 ? void 0 : userInfo.user_full_name) || (userInfo === null || userInfo === void 0 ? void 0 : userInfo.user_first_name) || (userInfo === null || userInfo === void 0 ? void 0 : userInfo.user_email) || "Unknown User",
                },
                priority: actionPriority,
                context: {
                    fieldName: entry.field_name,
                },
            };
        }
        return {
            highlights,
            metadata: {
                totalRecipes: recipeIds.length,
                recipesWithHighlights: Object.keys(highlights).length,
                queryTimestamp: new Date(),
            },
        };
    }
    catch (error) {
        console.error("Error getting bulk recipe highlights:", error);
        return {
            highlights: {},
            metadata: {
                totalRecipes: recipeIds.length,
                recipesWithHighlights: 0,
                queryTimestamp: new Date(),
            },
        };
    }
});
exports.getBulkRecipeHighlights = getBulkRecipeHighlights;
/**
 * Generate a default description for an action if none exists
 */
function generateDefaultDescription(action, component) {
    const componentName = COMPONENT_DISPLAY_NAMES[component];
    switch (action) {
        case RecipeHistory_1.RecipeHistoryAction.created:
            return "Recipe was created";
        case RecipeHistory_1.RecipeHistoryAction.updated:
            return `${componentName} was updated`;
        case RecipeHistory_1.RecipeHistoryAction.published:
            return "Recipe was published";
        case RecipeHistory_1.RecipeHistoryAction.archived:
            return "Recipe was archived";
        case RecipeHistory_1.RecipeHistoryAction.restored:
            return "Recipe was restored";
        case RecipeHistory_1.RecipeHistoryAction.deleted:
            return "Recipe was deleted";
        case RecipeHistory_1.RecipeHistoryAction.ingredient_added:
            return "New ingredient was added";
        case RecipeHistory_1.RecipeHistoryAction.ingredient_removed:
            return "Ingredient was removed";
        case RecipeHistory_1.RecipeHistoryAction.ingredient_updated:
            return "Ingredient was updated";
        case RecipeHistory_1.RecipeHistoryAction.step_added:
            return "New preparation step was added";
        case RecipeHistory_1.RecipeHistoryAction.step_removed:
            return "Preparation step was removed";
        case RecipeHistory_1.RecipeHistoryAction.step_updated:
            return "Preparation step was updated";
        case RecipeHistory_1.RecipeHistoryAction.category_added:
            return "Category was added";
        case RecipeHistory_1.RecipeHistoryAction.category_removed:
            return "Category was removed";
        case RecipeHistory_1.RecipeHistoryAction.attribute_added:
            return "Attribute was added";
        case RecipeHistory_1.RecipeHistoryAction.attribute_removed:
            return "Attribute was removed";
        case RecipeHistory_1.RecipeHistoryAction.resource_added:
            return "Resource was added";
        case RecipeHistory_1.RecipeHistoryAction.resource_removed:
            return "Resource was removed";
        default:
            return `${componentName} was modified`;
    }
}
/**
 * Check if a recipe has recent changes within the specified timeframe
 */
const hasRecentChanges = (recipeId_1, organizationId_1, ...args_1) => __awaiter(void 0, [recipeId_1, organizationId_1, ...args_1], void 0, function* (recipeId, organizationId, daysBack = 7) {
    try {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysBack);
        const whereConditions = {
            recipe_id: recipeId,
            created_at: {
                [sequelize_1.Op.gte]: cutoffDate,
            },
            action: {
                [sequelize_1.Op.notIn]: EXCLUDED_HIGHLIGHT_ACTIONS,
            },
        };
        if (organizationId) {
            whereConditions.organization_id = organizationId;
        }
        const count = yield RecipeHistory_1.RecipeHistory.count({
            where: whereConditions,
        });
        return count > 0;
    }
    catch (error) {
        console.error("Error checking recent changes:", error);
        return false;
    }
});
exports.hasRecentChanges = hasRecentChanges;
