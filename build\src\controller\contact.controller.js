"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_codes_1 = require("http-status-codes");
const ContactUs_1 = __importDefault(require("../models/ContactUs"));
const sequelize_1 = require("sequelize");
const transaction_helper_1 = require("../helper/transaction.helper");
const validation_helper_1 = require("../helper/validation.helper");
/**
 * Create a new contact us submission
 * @route POST /api/v1/public/contact-us
 */
const createContactUs = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const transactionManager = new transaction_helper_1.TransactionManager();
    try {
        // Input validation and sanitization
        const sanitizedBody = validation_helper_1.ValidationHelper.sanitizeInput(req.body);
        const { name, email, mobile, message, recipe_id } = sanitizedBody;
        // Validate required fields
        const errors = [];
        const nameError = validation_helper_1.ValidationHelper.validateRequiredString(name, 'name', 2, 100);
        if (nameError)
            errors.push(nameError);
        const emailError = validation_helper_1.ValidationHelper.validateEmail(email, 'email');
        if (emailError)
            errors.push(emailError);
        const messageError = validation_helper_1.ValidationHelper.validateRequiredString(message, 'message', 10, 1000);
        if (messageError)
            errors.push(messageError);
        if (mobile) {
            const mobileError = validation_helper_1.ValidationHelper.validateOptionalString(mobile, 'mobile', 20);
            if (mobileError)
                errors.push(mobileError);
        }
        if (recipe_id) {
            const recipeIdError = validation_helper_1.ValidationHelper.validatePositiveInteger(recipe_id, 'recipe_id');
            if (recipeIdError)
                errors.push(recipeIdError);
        }
        if (errors.length > 0) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: "Validation failed",
                errors
            });
        }
        // Start transaction
        const transaction = yield transactionManager.start();
        const contact = yield ContactUs_1.default.create({
            name: name.trim(),
            email: email.trim().toLowerCase(),
            mobile: (mobile === null || mobile === void 0 ? void 0 : mobile.trim()) || null,
            message: message.trim(),
            recipe_id: recipe_id || null,
        }, { transaction });
        yield transactionManager.commit();
        return res.status(http_status_codes_1.StatusCodes.CREATED).json({
            status: true,
            message: res.__("CONTACT_US_CREATED_SUCCESSFULLY"),
            data: { id: contact.id },
        });
    }
    catch (error) {
        yield transactionManager.rollback();
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error creating contact submission");
    }
});
/**
 * Get all contact us submissions
 * @route GET /api/v1/public/contact-us/list
 */
const getAllContactUs = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Input validation and sanitization
        const sanitizedQuery = validation_helper_1.ValidationHelper.sanitizeInput(req.query);
        const { page, size, search = "", recipe_id } = sanitizedQuery;
        // Validate pagination parameters
        const pageNumber = Math.max(1, parseInt(page, 10) || 1);
        const pageSize = Math.min(100, Math.max(1, parseInt(size, 10) || 10));
        const offset = (pageNumber - 1) * pageSize;
        const whereClause = {};
        // Validate and apply search filter
        if (search && typeof search === "string" && search.trim()) {
            const searchTerm = search.trim();
            if (searchTerm.length >= 2) { // Minimum search length
                whereClause[sequelize_1.Op.or] = [
                    { name: { [sequelize_1.Op.like]: `%${searchTerm}%` } },
                    { email: { [sequelize_1.Op.like]: `%${searchTerm}%` } },
                    { message: { [sequelize_1.Op.like]: `%${searchTerm}%` } },
                ];
            }
        }
        // Validate and apply recipe filter
        if (recipe_id) {
            const recipeIdNum = parseInt(recipe_id, 10);
            if (!isNaN(recipeIdNum) && recipeIdNum > 0) {
                whereClause.recipe_id = recipeIdNum;
            }
        }
        const { rows: contacts, count } = yield ContactUs_1.default.findAndCountAll({
            where: whereClause,
            limit: pageSize,
            offset: offset,
            order: [["created_at", "DESC"]],
        });
        const totalPages = Math.ceil(count / pageSize);
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_DATA_FETCHED"),
            data: contacts,
            pagination: {
                current_page: pageNumber,
                page_size: pageSize,
                total_records: count,
                total_pages: totalPages,
                has_next: pageNumber < totalPages,
                has_prev: pageNumber > 1
            }
        });
    }
    catch (error) {
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error fetching contact submissions");
    }
});
/**
 * Get single contact us by ID
 * @route GET /api/v1/public/contact-us/get/:id
 */
const getContactUsById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        // Validate ID parameter
        const idError = validation_helper_1.ValidationHelper.validatePositiveInteger(id, 'id');
        if (idError) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: idError
            });
        }
        const contact = yield ContactUs_1.default.findByPk(id);
        if (!contact) {
            return res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("CONTACT_US_NOT_FOUND"),
            });
        }
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_DATA_FETCHED"),
            data: contact,
        });
    }
    catch (error) {
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error fetching contact submission");
    }
});
/**
 * Update contact us by ID
 * @route PUT /api/v1/public/contact-us/update/:id
 */
const updateContactUs = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const transactionManager = new transaction_helper_1.TransactionManager();
    try {
        const { id } = req.params;
        // Validate ID parameter
        const idError = validation_helper_1.ValidationHelper.validatePositiveInteger(id, 'id');
        if (idError) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: idError
            });
        }
        // Input validation and sanitization
        const sanitizedBody = validation_helper_1.ValidationHelper.sanitizeInput(req.body);
        const { name, email, mobile, message, recipe_id } = sanitizedBody;
        // Start transaction
        const transaction = yield transactionManager.start();
        const contact = yield ContactUs_1.default.findByPk(id, { transaction });
        if (!contact) {
            yield transactionManager.rollback();
            return res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("CONTACT_US_NOT_FOUND"),
            });
        }
        // Validate updated fields if provided
        const errors = [];
        if (name !== undefined) {
            const nameError = validation_helper_1.ValidationHelper.validateRequiredString(name, 'name', 2, 100);
            if (nameError)
                errors.push(nameError);
        }
        if (email !== undefined) {
            const emailError = validation_helper_1.ValidationHelper.validateEmail(email, 'email');
            if (emailError)
                errors.push(emailError);
        }
        if (message !== undefined) {
            const messageError = validation_helper_1.ValidationHelper.validateRequiredString(message, 'message', 10, 1000);
            if (messageError)
                errors.push(messageError);
        }
        if (mobile !== undefined && mobile !== null) {
            const mobileError = validation_helper_1.ValidationHelper.validateOptionalString(mobile, 'mobile', 20);
            if (mobileError)
                errors.push(mobileError);
        }
        if (recipe_id !== undefined && recipe_id !== null) {
            const recipeIdError = validation_helper_1.ValidationHelper.validatePositiveInteger(recipe_id, 'recipe_id');
            if (recipeIdError)
                errors.push(recipeIdError);
        }
        if (errors.length > 0) {
            yield transactionManager.rollback();
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: "Validation failed",
                errors
            });
        }
        yield contact.update({
            name: (name === null || name === void 0 ? void 0 : name.trim()) || contact.name,
            email: (email === null || email === void 0 ? void 0 : email.trim().toLowerCase()) || contact.email,
            mobile: (mobile === null || mobile === void 0 ? void 0 : mobile.trim()) || contact.mobile,
            message: (message === null || message === void 0 ? void 0 : message.trim()) || contact.message,
            recipe_id: recipe_id !== undefined ? recipe_id : contact.recipe_id,
        }, { transaction });
        yield transactionManager.commit();
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("CONTACT_US_UPDATED_SUCCESSFULLY"),
            data: { id: contact.id },
        });
    }
    catch (error) {
        yield transactionManager.rollback();
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error updating contact submission");
    }
});
/**
 * Delete contact us by ID
 * @route DELETE /api/v1/public/contact-us/delete/:id
 */
const deleteContactUs = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const transactionManager = new transaction_helper_1.TransactionManager();
    try {
        const { id } = req.params;
        // Validate ID parameter
        const idError = validation_helper_1.ValidationHelper.validatePositiveInteger(id, 'id');
        if (idError) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                status: false,
                message: idError
            });
        }
        // Start transaction
        const transaction = yield transactionManager.start();
        const contact = yield ContactUs_1.default.findByPk(id, { transaction });
        if (!contact) {
            yield transactionManager.rollback();
            return res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("CONTACT_US_NOT_FOUND"),
            });
        }
        yield contact.destroy({ transaction });
        yield transactionManager.commit();
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            status: true,
            message: res.__("CONTACT_US_DELETED_SUCCESSFULLY"),
        });
    }
    catch (error) {
        yield transactionManager.rollback();
        return transaction_helper_1.ErrorHandler.createErrorResponse(error, res, "Error deleting contact submission");
    }
});
exports.default = {
    createContactUs,
    getAllContactUs,
    getContactUsById,
    updateContactUs,
    deleteContactUs,
};
