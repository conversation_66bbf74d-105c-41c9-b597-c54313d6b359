"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecipeAttributes = exports.RecipeAttributesStatus = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var RecipeAttributesStatus;
(function (RecipeAttributesStatus) {
    RecipeAttributesStatus["active"] = "active";
    RecipeAttributesStatus["inactive"] = "inactive";
})(RecipeAttributesStatus || (exports.RecipeAttributesStatus = RecipeAttributesStatus = {}));
class RecipeAttributes extends sequelize_1.Model {
}
exports.RecipeAttributes = RecipeAttributes;
RecipeAttributes.init({
    recipe_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        references: {
            model: "mo_recipe",
            key: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
    },
    attributes_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        references: {
            model: "mo_food_attributes",
            key: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
    },
    unit_of_measure: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
        comment: "Unit of measure as text (e.g., 'grams', 'cups', 'tablespoons')",
    },
    unit: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
    },
    status: {
        type: sequelize_1.DataTypes.ENUM(Object.values(RecipeAttributesStatus)),
        allowNull: false,
        defaultValue: RecipeAttributesStatus.active,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    attribute_description: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    may_contain: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
        comment: "For allergens: true = may contain, false/null = contains",
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_recipe_attributes",
    modelName: "RecipeAttributes",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
        {
            unique: true,
            fields: ["recipe_id", "attributes_id", "may_contain"],
            name: "unique_recipe_attributes_may_contain",
        },
        {
            fields: ["organization_id"],
            name: "idx_recipe_attributes_organization",
        },
        {
            fields: ["status"],
            name: "idx_recipe_attributes_status",
        },
        {
            fields: ["created_by"],
            name: "idx_recipe_attributes_created_by",
        },
        {
            fields: ["updated_by"],
            name: "idx_recipe_attributes_updated_by",
        },
    ],
});
// Define associations
RecipeAttributes.associate = (models) => {
    // RecipeAttributes belongs to Recipe
    RecipeAttributes.belongsTo(models.Recipe, {
        foreignKey: "recipe_id",
        as: "recipe",
    });
    // RecipeAttributes belongs to FoodAttributes
    RecipeAttributes.belongsTo(models.FoodAttributes, {
        foreignKey: "attributes_id",
        as: "attribute",
    });
    // Note: unit_of_measure is now a text field, no longer a foreign key to RecipeMeasure
    // RecipeAttributes belongs to User (created_by)
    RecipeAttributes.belongsTo(models.User, {
        foreignKey: "created_by",
        as: "creator",
    });
    // RecipeAttributes belongs to User (updated_by)
    RecipeAttributes.belongsTo(models.User, {
        foreignKey: "updated_by",
        as: "updater",
    });
};
exports.default = RecipeAttributes;
