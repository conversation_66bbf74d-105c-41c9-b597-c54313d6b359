"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecipeResources = exports.RecipeResourceStatus = exports.RecipeResourceLinkType = exports.RecipeResourceType = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var RecipeResourceType;
(function (RecipeResourceType) {
    RecipeResourceType["item"] = "item";
    RecipeResourceType["link"] = "link";
})(RecipeResourceType || (exports.RecipeResourceType = RecipeResourceType = {}));
var RecipeResourceLinkType;
(function (RecipeResourceLinkType) {
    RecipeResourceLinkType["image"] = "image";
    RecipeResourceLinkType["video"] = "video";
    RecipeResourceLinkType["pdf"] = "pdf";
    RecipeResourceLinkType["audio"] = "audio";
    RecipeResourceLinkType["youtube"] = "youtube";
    RecipeResourceLinkType["link"] = "link";
    RecipeResourceLinkType["document"] = "document";
    RecipeResourceLinkType["other"] = "other";
    RecipeResourceLinkType["text"] = "text";
})(RecipeResourceLinkType || (exports.RecipeResourceLinkType = RecipeResourceLinkType = {}));
var RecipeResourceStatus;
(function (RecipeResourceStatus) {
    RecipeResourceStatus["active"] = "active";
    RecipeResourceStatus["inactive"] = "inactive";
})(RecipeResourceStatus || (exports.RecipeResourceStatus = RecipeResourceStatus = {}));
class RecipeResources extends sequelize_1.Model {
}
exports.RecipeResources = RecipeResources;
RecipeResources.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    recipe_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: "mo_recipe",
            key: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
    },
    type: {
        type: sequelize_1.DataTypes.ENUM(Object.values(RecipeResourceType)),
        allowNull: false,
    },
    item_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
    },
    item_link: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    item_link_type: {
        type: sequelize_1.DataTypes.ENUM(Object.values(RecipeResourceLinkType)),
        allowNull: true,
    },
    status: {
        type: sequelize_1.DataTypes.ENUM(Object.values(RecipeResourceStatus)),
        allowNull: false,
        defaultValue: RecipeResourceStatus.active,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_recipe_resources",
    modelName: "RecipeResources",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
        {
            fields: ["recipe_id"],
            name: "idx_recipe_resources_recipe",
        },
        {
            fields: ["organization_id"],
            name: "idx_recipe_resources_organization",
        },
        {
            fields: ["status"],
            name: "idx_recipe_resources_status",
        },
        {
            fields: ["type"],
            name: "idx_recipe_resources_type",
        },
        {
            fields: ["created_by"],
            name: "idx_recipe_resources_created_by",
        },
        {
            fields: ["updated_by"],
            name: "idx_recipe_resources_updated_by",
        },
    ],
});
// Define associations
RecipeResources.associate = (models) => {
    // RecipeResources belongs to Recipe
    RecipeResources.belongsTo(models.Recipe, {
        foreignKey: "recipe_id",
        as: "recipe",
    });
    // RecipeResources belongs to User (created_by)
    RecipeResources.belongsTo(models.User, {
        foreignKey: "created_by",
        as: "creator",
    });
    // RecipeResources belongs to User (updated_by)
    RecipeResources.belongsTo(models.User, {
        foreignKey: "updated_by",
        as: "updater",
    });
    // RecipeResources belongs to Item (item_id) - for file resources only
    // Note: No foreign key constraint because item_id can be NULL for link resources
    RecipeResources.belongsTo(models.Item, {
        foreignKey: "item_id",
        as: "resourceItem",
        constraints: false, // Disable constraint to allow NULL for link resources
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
    });
};
exports.default = RecipeResources;
