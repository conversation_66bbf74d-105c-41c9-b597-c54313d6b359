"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const Settings_1 = __importStar(require("../models/Settings"));
// RECIPE SETTINGS - EXACTLY MATCHING UI SCREENSHOTS
const DEFAULT_SETTINGS = [
    // Private Recipe Visibility Settings
    {
        key: "recipe.highlight_changes",
        defaultValue: false,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.RECIPE,
        description: "Enable this to show highlighted changes to the assigned team member when viewing the recipe",
        isSystem: false,
    },
    // Public Recipe Settings
    {
        key: "recipe.public_store_enabled",
        defaultValue: false,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.RECIPE,
        description: "Enable to make public recipe features available. Turning this off hides all public recipe options",
        isSystem: false,
    },
    // Public Recipe Call-To-Action Settings
    {
        key: "recipe.cta_contact_form",
        defaultValue: true,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Show a basic contact form (Name, Email, Phone, Message)",
        isSystem: false,
    },
    {
        key: "recipe.cta_contact_info",
        defaultValue: false,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Display a predefined contact block (Phone, Email, Link)",
        isSystem: false,
    },
    {
        key: "recipe.cta_custom_link",
        defaultValue: false,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Show a custom CTA with text and an external link",
        isSystem: false,
    },
    {
        key: "recipe.cta_none",
        defaultValue: false,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Show nothing",
        isSystem: false,
    },
    // Contact Info Fields (for when contact info is selected)
    {
        key: "recipe.contact_info_name",
        defaultValue: "",
        type: Settings_1.SettingType.STRING,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Contact name",
        isSystem: false,
    },
    {
        key: "recipe.contact_info_phone",
        defaultValue: "",
        type: Settings_1.SettingType.STRING,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Phone number",
        isSystem: false,
    },
    {
        key: "recipe.contact_info_email",
        defaultValue: "",
        type: Settings_1.SettingType.STRING,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Email address",
        isSystem: false,
    },
    {
        key: "recipe.contact_info_link",
        defaultValue: "",
        type: Settings_1.SettingType.STRING,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Link URL",
        isSystem: false,
    },
    // Custom CTA Fields (for when custom CTA is selected)
    {
        key: "recipe.custom_cta_text",
        defaultValue: "",
        type: Settings_1.SettingType.STRING,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Custom CTA button text",
        isSystem: false,
    },
    {
        key: "recipe.custom_cta_link",
        defaultValue: "",
        type: Settings_1.SettingType.STRING,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Custom CTA link URL",
        isSystem: false,
    },
    // Recipe Details to Display Publicly (exactly as shown in UI screenshots)
    {
        key: "recipe.display_category",
        defaultValue: true,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Category",
        isSystem: false,
    },
    {
        key: "recipe.display_ingredients",
        defaultValue: true,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Ingredients",
        isSystem: false,
    },
    {
        key: "recipe.display_nutritional_information",
        defaultValue: true,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Nutritional Information",
        isSystem: false,
    },
    {
        key: "recipe.display_allergen_information",
        defaultValue: true,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Allergen Information",
        isSystem: false,
    },
    {
        key: "recipe.display_preparation_steps",
        defaultValue: true,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Preparation Steps",
        isSystem: false,
    },
    {
        key: "recipe.display_total_time",
        defaultValue: false,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Total Time",
        isSystem: false,
    },
    {
        key: "recipe.display_yield_portioning",
        defaultValue: false,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Yield & Portioning",
        isSystem: false,
    },
    {
        key: "recipe.display_cost",
        defaultValue: false,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Cost",
        isSystem: false,
    },
    {
        key: "recipe.display_dietary_suitability",
        defaultValue: false,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Dietary Suitability",
        isSystem: false,
    },
    {
        key: "recipe.display_cuisine_type",
        defaultValue: false,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Cuisine Type",
        isSystem: false,
    },
    {
        key: "recipe.display_media",
        defaultValue: false,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Media",
        isSystem: false,
    },
    {
        key: "recipe.display_links",
        defaultValue: false,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Links",
        isSystem: false,
    },
    {
        key: "recipe.display_scale",
        defaultValue: false,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Scale",
        isSystem: false,
    },
    {
        key: "recipe.display_serve_in",
        defaultValue: false,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Serve In",
        isSystem: false,
    },
    {
        key: "recipe.display_garnish",
        defaultValue: false,
        type: Settings_1.SettingType.BOOLEAN,
        category: Settings_1.SettingCategory.PUBLIC,
        description: "Garnish",
        isSystem: false,
    },
];
class SettingsService {
    /**
     * Get setting value with fallback to default
     */
    getSetting(key, organizationId) {
        return __awaiter(this, void 0, void 0, function* () {
            const setting = yield Settings_1.default.findOne({
                where: {
                    setting_key: key,
                    organization_id: organizationId || null,
                },
            });
            if (setting) {
                return this.parseSettingValue(setting);
            }
            // Return default value if setting doesn't exist
            const defaultSetting = DEFAULT_SETTINGS.find((s) => s.key === key);
            return defaultSetting ? defaultSetting.defaultValue : null;
        });
    }
    /**
     * Get all settings for a category
     */
    getSettingsByCategory(category, organizationId) {
        return __awaiter(this, void 0, void 0, function* () {
            const settings = yield Settings_1.default.findAll({
                where: {
                    setting_category: category,
                    organization_id: organizationId || null,
                },
            });
            const result = {};
            // Add existing settings
            settings.forEach((setting) => {
                result[setting.setting_key] = this.parseSettingValue(setting);
            });
            // Add missing default settings
            DEFAULT_SETTINGS.filter((def) => def.category === category).forEach((def) => {
                if (!(def.key in result)) {
                    result[def.key] = def.defaultValue;
                }
            });
            return result;
        });
    }
    /**
     * Update or create setting
     */
    updateSetting(key, value, organizationId, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const defaultSetting = DEFAULT_SETTINGS.find((s) => s.key === key);
            if (!defaultSetting) {
                throw new Error(`Unknown setting key: ${key}`);
            }
            const serializedValue = this.serializeSettingValue(value, defaultSetting.type);
            const [setting] = yield Settings_1.default.upsert({
                setting_key: key,
                setting_value: serializedValue,
                setting_type: defaultSetting.type,
                setting_category: defaultSetting.category,
                setting_description: defaultSetting.description,
                is_system_setting: defaultSetting.isSystem,
                organization_id: organizationId || null,
                created_by: userId || 1,
                updated_by: userId || 1,
            });
            return setting;
        });
    }
    /**
     * Update multiple settings at once
     */
    updateSettings(settings, organizationId, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const promises = Object.entries(settings).map(([key, value]) => this.updateSetting(key, value, organizationId, userId));
            yield Promise.all(promises);
        });
    }
    /**
     * Initialize default settings for an organization
     */
    initializeDefaultSettings(organizationId, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const existingSettings = yield Settings_1.default.findAll({
                where: { organization_id: organizationId },
                attributes: ["setting_key"],
            });
            const existingKeys = existingSettings.map((s) => s.setting_key);
            const missingSettings = DEFAULT_SETTINGS.filter((def) => !existingKeys.includes(def.key));
            if (missingSettings.length > 0) {
                const settingsToCreate = missingSettings.map((def) => ({
                    setting_key: def.key,
                    setting_value: this.serializeSettingValue(def.defaultValue, def.type),
                    setting_type: def.type,
                    setting_category: def.category,
                    setting_description: def.description,
                    is_system_setting: def.isSystem,
                    organization_id: organizationId,
                    created_by: userId,
                    updated_by: userId,
                }));
                yield Settings_1.default.bulkCreate(settingsToCreate);
            }
        });
    }
    /**
     * Parse setting value based on type
     */
    parseSettingValue(setting) {
        switch (setting.setting_type) {
            case Settings_1.SettingType.BOOLEAN:
                return setting.getBooleanValue();
            case Settings_1.SettingType.NUMBER:
                return setting.getNumberValue();
            case Settings_1.SettingType.JSON:
                return setting.getJsonValue();
            case Settings_1.SettingType.ARRAY:
                return setting.getArrayValue();
            default:
                return setting.getStringValue();
        }
    }
    /**
     * Serialize setting value for storage
     */
    serializeSettingValue(value, type) {
        switch (type) {
            case Settings_1.SettingType.JSON:
            case Settings_1.SettingType.ARRAY:
                return JSON.stringify(value);
            default:
                return String(value);
        }
    }
    /**
     * Get setting definitions (for UI generation)
     */
    getSettingDefinitions() {
        return DEFAULT_SETTINGS;
    }
    /**
     * Get all settings for an organization (common function for public APIs)
     * Returns both recipe and public settings combined
     */
    getSettingsByOrganizationId(organizationId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Get both recipe and public settings for the organization
                const [recipeSettings, publicSettings] = yield Promise.all([
                    this.getSettingsByCategory(Settings_1.SettingCategory.RECIPE, organizationId),
                    this.getSettingsByCategory(Settings_1.SettingCategory.PUBLIC, organizationId)
                ]);
                // Combine both settings into a single object
                return Object.assign(Object.assign({}, recipeSettings), publicSettings);
            }
            catch (error) {
                console.error("Error in getSettingsByOrganizationId:", error);
                throw error;
            }
        });
    }
    /**
     * Get structured settings for an organization (for public APIs)
     * Returns settings in the same format as getRecipeConfiguration API
     */
    getStructuredSettingsByOrganizationId(organizationId) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
            try {
                // Get both recipe and public settings for the organization
                const [recipeSettings, publicSettings] = yield Promise.all([
                    this.getSettingsByCategory(Settings_1.SettingCategory.RECIPE, organizationId),
                    this.getSettingsByCategory(Settings_1.SettingCategory.PUBLIC, organizationId)
                ]);
                // Structure the settings in the same format as getRecipeConfiguration
                const structuredSettings = {
                    // Private Recipe Visibility Settings
                    privateRecipeVisibilitySettings: {
                        highlightChanges: (_a = recipeSettings["recipe.highlight_changes"]) !== null && _a !== void 0 ? _a : false,
                    },
                    // Public Recipe Settings
                    publicRecipeSettings: {
                        publicStoreAccess: recipeSettings["recipe.public_store_enabled"] || false,
                    },
                    // Public Recipe Call-To-Action (CTA)
                    publicRecipeCallToAction: {
                        contactForm: (_b = publicSettings["recipe.cta_contact_form"]) !== null && _b !== void 0 ? _b : true,
                        contactInfo: {
                            enabled: publicSettings["recipe.cta_contact_info"] || false,
                            name: publicSettings["recipe.contact_info_name"] || "",
                            phone: publicSettings["recipe.contact_info_phone"] || "",
                            email: publicSettings["recipe.contact_info_email"] || "",
                            link: publicSettings["recipe.contact_info_link"] || "",
                        },
                        customCtaLink: {
                            enabled: publicSettings["recipe.cta_custom_link"] || false,
                            text: publicSettings["recipe.custom_cta_text"] || "",
                            link: publicSettings["recipe.custom_cta_link"] || "",
                        },
                        none: publicSettings["recipe.cta_none"] || false,
                    },
                    // Recipe Details to Display Publicly
                    recipeDetailsToDisplayPublicly: {
                        category: (_c = publicSettings["recipe.display_category"]) !== null && _c !== void 0 ? _c : true,
                        ingredients: (_d = publicSettings["recipe.display_ingredients"]) !== null && _d !== void 0 ? _d : true,
                        nutritionalInformation: (_e = publicSettings["recipe.display_nutritional_information"]) !== null && _e !== void 0 ? _e : true,
                        allergenInformation: (_f = publicSettings["recipe.display_allergen_information"]) !== null && _f !== void 0 ? _f : true,
                        preparationSteps: (_g = publicSettings["recipe.display_preparation_steps"]) !== null && _g !== void 0 ? _g : true,
                        totalTime: (_h = publicSettings["recipe.display_total_time"]) !== null && _h !== void 0 ? _h : false,
                        yieldPortioning: (_j = publicSettings["recipe.display_yield_portioning"]) !== null && _j !== void 0 ? _j : false,
                        cost: (_k = publicSettings["recipe.display_cost"]) !== null && _k !== void 0 ? _k : false,
                        dietarySuitability: (_l = publicSettings["recipe.display_dietary_suitability"]) !== null && _l !== void 0 ? _l : false,
                        cuisineType: (_m = publicSettings["recipe.display_cuisine_type"]) !== null && _m !== void 0 ? _m : false,
                        media: (_o = publicSettings["recipe.display_media"]) !== null && _o !== void 0 ? _o : false,
                        links: (_p = publicSettings["recipe.display_links"]) !== null && _p !== void 0 ? _p : false,
                        scale: (_q = publicSettings["recipe.display_scale"]) !== null && _q !== void 0 ? _q : false,
                        serveIn: (_r = publicSettings["recipe.display_serve_in"]) !== null && _r !== void 0 ? _r : false,
                        garnish: (_s = publicSettings["recipe.display_garnish"]) !== null && _s !== void 0 ? _s : false,
                    },
                };
                return structuredSettings;
            }
            catch (error) {
                console.error("Error in getStructuredSettingsByOrganizationId:", error);
                throw error;
            }
        });
    }
}
exports.default = new SettingsService();
