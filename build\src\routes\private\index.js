"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const routes = express_1.default.Router();
const category_routes_1 = __importDefault(require("./category.routes"));
const foodAttributes_routes_1 = __importDefault(require("./foodAttributes.routes"));
const recipeMeasure_routes_1 = __importDefault(require("./recipeMeasure.routes"));
const ingredients_routes_1 = __importDefault(require("./ingredients.routes"));
const settings_routes_1 = __importDefault(require("./settings.routes"));
const dashboard_routes_1 = __importDefault(require("./dashboard.routes"));
const analytics_routes_1 = __importDefault(require("./analytics.routes"));
const recipe_routes_1 = __importDefault(require("./recipe.routes"));
// Category routes (with integrated icon upload)
routes.use("/category", category_routes_1.default);
// Food attributes routes (with integrated icon upload)
routes.use("/food-attributes", foodAttributes_routes_1.default);
// Recipe measure units routes (with integrated icon upload)
routes.use("/recipe-measures", recipeMeasure_routes_1.default);
// Ingredients routes
routes.use("/ingredients", ingredients_routes_1.default);
// Settings routes
routes.use("/settings", settings_routes_1.default);
// Dashboard routes
routes.use("/dashboard", dashboard_routes_1.default);
// Analytics routes (private)
routes.use("/analytics", analytics_routes_1.default);
// Recipe routes
routes.use("/recipes", recipe_routes_1.default);
exports.default = routes;
