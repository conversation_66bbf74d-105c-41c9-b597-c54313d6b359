"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserRoles = exports.getOrganizationData = exports.makeRequest = exports.getUserData = void 0;
const axios_1 = __importDefault(require("axios"));
const getUserData = (url_1, ...args_1) => __awaiter(void 0, [url_1, ...args_1], void 0, function* (url, token = null) {
    try {
        // Fetch the token
        const response = yield makeRequest(url, "GET", null, {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
        });
        if (response.status == false) {
            return {
                status: response.status,
                statusText: response.statusText,
                statusCode: response.statusCode,
                message: response.message,
            }; // Return true if the user is created successfully in Keycloak
        }
        if (response.data && response.data.attributes) {
            const attributes = response.data.attributes;
            // Transform the attributes by converting array values to single values
            const transformedAttributes = {};
            Object.keys(attributes).forEach((key) => {
                // Take the first element of the array or keep the value if it's already a single value
                transformedAttributes[key] = Array.isArray(attributes[key])
                    ? attributes[key][0]
                    : attributes[key];
            });
            // Update the response with the transformed attributes
            response.data.attributes = transformedAttributes;
        }
        return {
            status: true,
            message: response,
            statusText: response.statusText,
            data: response.data,
        }; // Return true if the user is created successfully in Keycloak
    }
    catch (e) {
        console.log("Get data By Id Exception: ", e.response); // Log any exceptions that occur
        if (e.response.status !== 200) {
            return {
                status: false,
                statusCode: e.response.status,
                message: e.response.data.errorMessage,
                statusText: e.response.statusText,
            }; // Return false if an exception is caught
        }
    }
});
exports.getUserData = getUserData;
const makeRequest = (url_1, method_1, ...args_1) => __awaiter(void 0, [url_1, method_1, ...args_1], void 0, function* (url, method, payload = null, headers = null) {
    var _a;
    try {
        const config = {
            method,
            url,
            headers: headers,
        };
        if (payload) {
            config.data = payload; // Attach the payload for methods like POST, PUT, etc.
        }
        const response = yield (0, axios_1.default)(config);
        return response; // Return the response data
    }
    catch (e) {
        console.log("axios Exception: ", e.response); // Log any exceptions that occur
        if (e.response && e.response.status !== 200) {
            return {
                status: false,
                statusCode: e.response.status,
                message: e.response.data.errorMessage,
                statusText: e.response.data.error,
                field: (_a = e.response.data) === null || _a === void 0 ? void 0 : _a.field,
            }; // Return false if an exception is caught
        }
    }
});
exports.makeRequest = makeRequest;
const getOrganizationData = (url_1, ...args_1) => __awaiter(void 0, [url_1, ...args_1], void 0, function* (url, token = null) {
    var _a;
    try {
        const response = yield makeRequest(url, "GET", null, {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
        });
        if (response.status == false) {
            return {
                status: response.status,
                statusText: response.statusText,
                statusCode: response.statusCode,
                message: response.message,
            }; // Return true if the user is created successfully in Keycloak
        }
        if (response.data && response.data.attributes) {
            const attributes = response.data.attributes;
            // Transform the attributes by converting array values to single values
            const transformedAttributes = {};
            Object.keys(attributes).forEach((key) => {
                // Take the first element of the array or keep the value if it's already a single value
                transformedAttributes[key] = Array.isArray(attributes[key])
                    ? attributes[key][0]
                    : attributes[key];
            });
            // Update the response with the transformed attributes
            response.data.attributes = transformedAttributes;
        }
        return {
            status: true,
            message: response,
            statusText: response.statusText,
            data: response.data,
        }; // Return true if the user is created successfully in Keycloak
    }
    catch (e) {
        console.log("axios Exception: ", e.response); // Log any exceptions that occur
        if (e.response && e.response.status !== 200) {
            return {
                status: false,
                statusCode: e.response.status,
                message: e.response.data.errorMessage,
                statusText: e.response.data.error,
                field: (_a = e.response.data) === null || _a === void 0 ? void 0 : _a.field,
            }; // Return false if an exception is caught
        }
    }
});
exports.getOrganizationData = getOrganizationData;
/** Get user assigned roles */
const getUserRoles = (userId, token) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${userId}/role-mappings`;
        const response = yield makeRequest(keycloakRealmUrl, "GET", null, {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
        });
        if (response.status == false) {
            return {
                status: response.status,
                statusText: response.statusText,
                statusCode: response.statusCode,
                message: response.message,
            };
        }
        return {
            status: true,
            statusText: response.statusText,
            data: response.data,
        };
    }
    catch (e) {
        console.log("Get User Roles Exception: ", e.response); // Log any exceptions that occur
        if (e.response.status !== 200) {
            return {
                status: false,
                statusCode: e.response.status,
                message: e.response.data.errorMessage,
                statusText: e.response.statusText,
            }; // Return false if an exception is caught
        }
    }
});
exports.getUserRoles = getUserRoles;
