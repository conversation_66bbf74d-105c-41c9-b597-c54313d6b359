"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecipeSteps = exports.RecipeStepsStatus = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var RecipeStepsStatus;
(function (RecipeStepsStatus) {
    RecipeStepsStatus["active"] = "active";
    RecipeStepsStatus["inactive"] = "inactive";
})(RecipeStepsStatus || (exports.RecipeStepsStatus = RecipeStepsStatus = {}));
class RecipeSteps extends sequelize_1.Model {
}
exports.RecipeSteps = RecipeSteps;
RecipeSteps.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    recipe_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: "mo_recipe",
            key: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
    },
    item_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        references: {
            model: "nv_items",
            key: "id",
        },
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
    },
    recipe_step_order: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    recipe_step_description: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    status: {
        type: sequelize_1.DataTypes.ENUM(Object.values(RecipeStepsStatus)),
        allowNull: false,
        defaultValue: RecipeStepsStatus.active,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_recipe_steps",
    modelName: "RecipeSteps",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
        {
            unique: true,
            fields: ["recipe_id", "recipe_step_order"],
            name: "unique_recipe_step_order",
        },
        {
            fields: ["organization_id"],
            name: "idx_recipe_steps_organization",
        },
        {
            fields: ["status"],
            name: "idx_recipe_steps_status",
        },
        {
            fields: ["created_by"],
            name: "idx_recipe_steps_created_by",
        },
        {
            fields: ["updated_by"],
            name: "idx_recipe_steps_updated_by",
        },
    ],
});
// Define associations
RecipeSteps.associate = (models) => {
    // RecipeSteps belongs to Recipe
    RecipeSteps.belongsTo(models.Recipe, {
        foreignKey: "recipe_id",
        as: "recipe",
    });
    // RecipeSteps belongs to Item (item_id)
    RecipeSteps.belongsTo(models.Item, {
        foreignKey: "item_id",
        as: "stepItem",
        constraints: true,
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
    });
    // RecipeSteps belongs to User (created_by)
    RecipeSteps.belongsTo(models.User, {
        foreignKey: "created_by",
        as: "creator",
    });
    // RecipeSteps belongs to User (updated_by)
    RecipeSteps.belongsTo(models.User, {
        foreignKey: "updated_by",
        as: "updater",
    });
};
exports.default = RecipeSteps;
