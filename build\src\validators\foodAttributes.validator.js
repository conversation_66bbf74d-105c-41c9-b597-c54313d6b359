"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const celebrate_1 = require("celebrate");
const FoodAttributes_1 = require("../models/FoodAttributes");
const createFoodAttributeValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object()
        .keys({
        attribute_title: celebrate_1.Joi.string().min(2).max(100).required(),
        attribute_description: celebrate_1.Joi.string().allow(null, "").optional(),
        attribute_type: celebrate_1.Joi.string()
            .valid(...Object.values(FoodAttributes_1.AttributeType))
            .required(),
        attribute_status: celebrate_1.Joi.string()
            .valid(...Object.values(FoodAttributes_1.AttributeStatus))
            .optional(),
        is_system_attribute: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.boolean(), celebrate_1.Joi.string().valid("true", "false"))
            .optional(),
    })
        .unknown(true), // Allow file uploads and other fields
});
const updateFoodAttributeValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object()
        .keys({
        attribute_title: celebrate_1.Joi.string().min(2).max(100).optional(),
        attribute_description: celebrate_1.Joi.string().allow(null, "").optional(),
        attribute_type: celebrate_1.Joi.string()
            .valid(...Object.values(FoodAttributes_1.AttributeType))
            .optional(),
        attribute_status: celebrate_1.Joi.string()
            .valid(...Object.values(FoodAttributes_1.AttributeStatus))
            .optional(),
        is_system_attribute: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.boolean(), celebrate_1.Joi.string().valid("true", "false"))
            .optional(),
    })
        .unknown(true), // Allow file uploads and other fields
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().integer().min(1).required(),
    }),
});
const deleteFoodAttributeValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
});
const getFoodAttributeValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
});
const getFoodAttributesListValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: celebrate_1.Joi.object().keys({
        page: celebrate_1.Joi.number().integer().min(1).default(1),
        limit: celebrate_1.Joi.number().integer().min(1).max(100).default(10),
        search: celebrate_1.Joi.string().max(100).optional(),
        status: celebrate_1.Joi.string()
            .valid(...Object.values(FoodAttributes_1.AttributeStatus))
            .optional(),
        type: celebrate_1.Joi.string()
            .valid(...Object.values(FoodAttributes_1.AttributeType))
            .optional(),
        sort_by: celebrate_1.Joi.string()
            .valid("attribute_title", "created_at", "updated_at")
            .default("created_at"),
        sort_order: celebrate_1.Joi.string().valid("asc", "desc").default("desc"),
    }),
});
// Default export object
exports.default = {
    createFoodAttributeValidator,
    updateFoodAttributeValidator,
    deleteFoodAttributeValidator,
    getFoodAttributeValidator,
    getFoodAttributesListValidator,
};
