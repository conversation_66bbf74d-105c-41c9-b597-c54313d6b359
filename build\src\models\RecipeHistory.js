"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecipeHistory = exports.RecipeHistoryAction = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var RecipeHistoryAction;
(function (RecipeHistoryAction) {
    RecipeHistoryAction["created"] = "created";
    RecipeHistoryAction["updated"] = "updated";
    RecipeHistoryAction["deleted"] = "deleted";
    RecipeHistoryAction["published"] = "published";
    RecipeHistoryAction["archived"] = "archived";
    RecipeHistoryAction["restored"] = "restored";
    RecipeHistoryAction["ingredient_added"] = "ingredient_added";
    RecipeHistoryAction["ingredient_removed"] = "ingredient_removed";
    RecipeHistoryAction["ingredient_updated"] = "ingredient_updated";
    RecipeHistoryAction["step_added"] = "step_added";
    RecipeHistoryAction["step_removed"] = "step_removed";
    RecipeHistoryAction["step_updated"] = "step_updated";
    RecipeHistoryAction["category_added"] = "category_added";
    RecipeHistoryAction["category_removed"] = "category_removed";
    RecipeHistoryAction["attribute_added"] = "attribute_added";
    RecipeHistoryAction["attribute_removed"] = "attribute_removed";
    RecipeHistoryAction["resource_added"] = "resource_added";
    RecipeHistoryAction["resource_removed"] = "resource_removed";
    RecipeHistoryAction["bookmark_added"] = "bookmark_added";
    RecipeHistoryAction["bookmark_removed"] = "bookmark_removed";
})(RecipeHistoryAction || (exports.RecipeHistoryAction = RecipeHistoryAction = {}));
class RecipeHistory extends sequelize_1.Model {
}
exports.RecipeHistory = RecipeHistory;
RecipeHistory.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    recipe_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: "mo_recipe",
            key: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
    },
    action: {
        type: sequelize_1.DataTypes.ENUM(...Object.values(RecipeHistoryAction)),
        allowNull: false,
    },
    field_name: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
        comment: "Name of the field that was changed",
    },
    old_value: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        comment: "Previous value before change (JSON string for complex objects)",
    },
    new_value: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        comment: "New value after change (JSON string for complex objects)",
    },
    description: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        comment: "Human-readable description of the change",
    },
    ip_address: {
        type: sequelize_1.DataTypes.STRING(45),
        allowNull: true,
        comment: "IP address of the user who made the change",
    },
    user_agent: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        comment: "User agent string of the browser/client",
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: "Timestamp when the history record was created",
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_recipe_history",
    modelName: "RecipeHistory",
    timestamps: false, // We handle created_at manually
    indexes: [
        {
            fields: ["recipe_id"],
            name: "idx_recipe_history_recipe",
        },
        {
            fields: ["action"],
            name: "idx_recipe_history_action",
        },
        {
            fields: ["created_by"],
            name: "idx_recipe_history_created_by",
        },
        {
            fields: ["created_at"],
            name: "idx_recipe_history_created_at",
        },
        {
            fields: ["organization_id"],
            name: "idx_recipe_history_organization",
        },
        {
            fields: ["recipe_id", "created_at"],
            name: "idx_recipe_history_recipe_date",
        },
    ],
});
// Define associations
RecipeHistory.associate = (models) => {
    // RecipeHistory belongs to Recipe
    RecipeHistory.belongsTo(models.Recipe, {
        foreignKey: "recipe_id",
        as: "recipe",
    });
    // RecipeHistory belongs to User (created_by)
    RecipeHistory.belongsTo(models.User, {
        foreignKey: "created_by",
        as: "creator",
    });
};
exports.default = RecipeHistory;
