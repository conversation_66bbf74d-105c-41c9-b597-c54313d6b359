"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const contact_controller_1 = __importDefault(require("../../controller/contact.controller"));
const contact_validator_1 = __importDefault(require("../../validators/contact.validator"));
const router = express_1.default.Router();
// POST /api/v1/public/contact-us - Create new contact us submission
router.post("/", contact_validator_1.default.createContactUsValidator(), contact_controller_1.default.createContactUs);
// GET /api/v1/public/contact-us/list - Get all contact us submissions
router.get("/list", contact_controller_1.default.getAllContactUs);
// GET /api/v1/public/contact-us/get/:id - Get single contact us by ID
router.get("/get/:id", contact_validator_1.default.getContactUsValidator(), contact_controller_1.default.getContactUsById);
// PUT /api/v1/public/contact-us/update/:id - Update contact us by ID
router.put("/update/:id", contact_validator_1.default.updateContactUsValidator(), contact_controller_1.default.updateContactUs);
// DELETE /api/v1/public/contact-us/delete/:id - Delete contact us by ID
router.delete("/delete/:id", contact_validator_1.default.deleteContactUsValidator(), contact_controller_1.default.deleteContactUs);
exports.default = router;
