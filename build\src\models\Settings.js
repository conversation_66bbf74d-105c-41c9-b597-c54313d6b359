"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Settings = exports.SettingCategory = exports.SettingType = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
var SettingType;
(function (SettingType) {
    SettingType["STRING"] = "string";
    SettingType["NUMBER"] = "number";
    SettingType["BOOLEAN"] = "boolean";
    SettingType["JSON"] = "json";
    SettingType["ARRAY"] = "array";
})(SettingType || (exports.SettingType = SettingType = {}));
var SettingCategory;
(function (SettingCategory) {
    SettingCategory["RECIPE"] = "recipe";
    SettingCategory["DASHBOARD"] = "dashboard";
    SettingCategory["PUBLIC"] = "public";
    SettingCategory["ANALYTICS"] = "analytics";
    SettingCategory["SYSTEM"] = "system";
})(SettingCategory || (exports.SettingCategory = SettingCategory = {}));
class Settings extends sequelize_1.Model {
    // Helper methods for type-safe value access
    getStringValue() {
        return this.setting_type === SettingType.STRING
            ? this.setting_value
            : String(this.setting_value);
    }
    getBooleanValue() {
        // Ensure we return a proper boolean regardless of stored representation
        if (typeof this.setting_value === "boolean") {
            return this.setting_value;
        }
        if (typeof this.setting_value === "string") {
            const trimmed = this.setting_value.trim().toLowerCase();
            if (trimmed === "true")
                return true;
            if (trimmed === "false")
                return false;
        }
        if (typeof this.setting_value === "number") {
            return this.setting_value !== 0;
        }
        // Fallback conversion
        return Boolean(this.setting_value);
    }
    getNumberValue() {
        return this.setting_type === SettingType.NUMBER
            ? this.setting_value
            : Number(this.setting_value);
    }
    getJsonValue() {
        return this.setting_type === SettingType.JSON
            ? this.setting_value
            : JSON.parse(this.setting_value);
    }
    getArrayValue() {
        return this.setting_type === SettingType.ARRAY
            ? this.setting_value
            : JSON.parse(this.setting_value);
    }
}
exports.Settings = Settings;
Settings.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    setting_key: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
        unique: "unique_org_setting",
    },
    setting_value: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: false,
    },
    setting_type: {
        type: sequelize_1.DataTypes.ENUM(Object.values(SettingType)),
        allowNull: false,
        defaultValue: SettingType.STRING,
    },
    setting_category: {
        type: sequelize_1.DataTypes.ENUM(Object.values(SettingCategory)),
        allowNull: false,
    },
    setting_description: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    is_system_setting: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
        unique: "unique_org_setting",
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    sequelize: index_1.sequelize,
    modelName: "Settings",
    tableName: "mo_recipe_settings",
    timestamps: true,
});
exports.default = Settings;
