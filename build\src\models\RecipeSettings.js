"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecipeSettings = void 0;
const sequelize_1 = require("sequelize");
const index_1 = require("./index");
class RecipeSettings extends sequelize_1.Model {
}
exports.RecipeSettings = RecipeSettings;
RecipeSettings.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    key: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
    },
    value: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    organization_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    updated_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
}, {
    sequelize: index_1.sequelize,
    tableName: "mo_recipe_setting",
    modelName: "RecipeSettings",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
        {
            unique: true,
            fields: ["key", "organization_id"],
            name: "unique_setting_key_per_org",
        },
        {
            fields: ["organization_id"],
            name: "idx_recipe_settings_organization",
        },
        {
            fields: ["created_by"],
            name: "idx_recipe_settings_created_by",
        },
        {
            fields: ["updated_by"],
            name: "idx_recipe_settings_updated_by",
        },
    ],
});
// Define associations
RecipeSettings.associate = (models) => {
    // RecipeSettings belongs to User (created_by)
    RecipeSettings.belongsTo(models.User, {
        foreignKey: "created_by",
        as: "creator",
    });
    // RecipeSettings belongs to User (updated_by)
    RecipeSettings.belongsTo(models.User, {
        foreignKey: "updated_by",
        as: "updater",
    });
};
exports.default = RecipeSettings;
