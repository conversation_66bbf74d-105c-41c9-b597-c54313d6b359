"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const celebrate_1 = require("celebrate");
const HandleErrorMessage = (err, req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d, _e, _f, _g;
    try {
        if ((0, celebrate_1.isCelebrateError)(err)) {
            let errorDetails;
            // Check different sources of validation errors
            if (err.details.get("body")) {
                errorDetails = err.details.get("body");
            }
            else if (err.details.get("query")) {
                errorDetails = err.details.get("query");
            }
            else if (err.details.get("params")) {
                errorDetails = err.details.get("params");
            }
            else if (err.details.get("headers")) {
                errorDetails = err.details.get("headers");
            }
            if (errorDetails &&
                errorDetails.details &&
                errorDetails.details.length > 0) {
                // Get the first error for the main message
                const firstError = errorDetails.details[0];
                const fieldPath = firstError.path ? firstError.path.join(".") : "field";
                // Debug logging to understand error structure
                console.log("Validation Error Debug:", {
                    type: firstError.type,
                    message: firstError.message,
                    context: firstError.context,
                    path: firstError.path
                });
                // Create user-friendly main message
                let mainMessage = "Validation failed";
                // Customize main message based on error type
                if (firstError.type === "any.required") {
                    mainMessage = `${fieldPath} is required`;
                }
                else if (firstError.type === "string.empty") {
                    mainMessage = `${fieldPath} cannot be empty`;
                }
                else if (firstError.type === "string.max") {
                    mainMessage = `${fieldPath} must not exceed ${(_a = firstError.context) === null || _a === void 0 ? void 0 : _a.limit} characters`;
                }
                else if (firstError.type === "string.min") {
                    mainMessage = `${fieldPath} must be at least ${(_b = firstError.context) === null || _b === void 0 ? void 0 : _b.limit} characters`;
                }
                else if (firstError.type === "number.min") {
                    mainMessage = `${fieldPath} must be at least ${(_c = firstError.context) === null || _c === void 0 ? void 0 : _c.limit}`;
                }
                else if (firstError.type === "number.max") {
                    mainMessage = `${fieldPath} must not exceed ${(_d = firstError.context) === null || _d === void 0 ? void 0 : _d.limit}`;
                }
                else if (firstError.type === "any.only") {
                    // Try different possible property names for valid options
                    let validOptions = "valid options";
                    if (((_e = firstError.context) === null || _e === void 0 ? void 0 : _e.valids) && Array.isArray(firstError.context.valids)) {
                        validOptions = firstError.context.valids.join(", ");
                    }
                    else if (((_f = firstError.context) === null || _f === void 0 ? void 0 : _f.valid) && Array.isArray(firstError.context.valid)) {
                        validOptions = firstError.context.valid.join(", ");
                    }
                    else if (((_g = firstError.context) === null || _g === void 0 ? void 0 : _g.allowedValues) && Array.isArray(firstError.context.allowedValues)) {
                        validOptions = firstError.context.allowedValues.join(", ");
                    }
                    else {
                        // If we can't find the valid options, extract them from the original message
                        const originalMessage = firstError.message || "";
                        // Try different patterns to extract valid options
                        let match = originalMessage.match(/must be one of \[(.*?)\]/);
                        if (!match) {
                            match = originalMessage.match(/must be one of (.*)/);
                        }
                        if (match && match[1]) {
                            validOptions = match[1].replace(/"/g, "").replace(/\[|\]/g, "");
                        }
                        else {
                            // Last resort: use the original message but clean it up
                            validOptions = originalMessage.replace(/.*must be one of\s*/i, "").replace(/"/g, "");
                        }
                    }
                    mainMessage = `${fieldPath} must be one of [${validOptions}]`;
                }
                else if (firstError.type === "string.pattern.base") {
                    mainMessage = `${fieldPath} format is invalid`;
                }
                else if (firstError.type === "number.base") {
                    mainMessage = `${fieldPath} must be a valid number`;
                }
                else if (firstError.type === "boolean.base") {
                    mainMessage = `${fieldPath} must be true or false`;
                }
                else if (firstError.type === "alternatives.match") {
                    mainMessage = firstError.message || `${fieldPath} has invalid format`;
                }
                else if (firstError.type === "object.unknown") {
                    mainMessage = `${fieldPath} is not allowed`;
                }
                else {
                    // For any other error type, try to include field name if not already present
                    const cleanMessage = firstError.message
                        .replace(/"/g, "") // Remove quotes
                        .replace(/\\/g, "") // Remove backslashes
                        .trim();
                    // If the message doesn't already contain the field name, prepend it
                    if (!cleanMessage.includes(fieldPath) && fieldPath !== "field") {
                        mainMessage = `${fieldPath} ${cleanMessage}`;
                    }
                    else {
                        mainMessage = cleanMessage;
                    }
                }
                return res.status(400).json({
                    status: false,
                    message: mainMessage,
                });
            }
            // Fallback for celebrate errors without detailed information
            return res.status(400).json({
                status: false,
                message: "Invalid request data",
            });
        }
        // If it's not a celebrate error, pass it to the next error handler
        next(err);
    }
    catch (e) {
        console.error("Error in validation middleware:", e);
        return res.status(400).json({
            status: false,
            message: "Validation error occurred",
        });
    }
});
exports.default = HandleErrorMessage;
