"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const dotenv_1 = __importDefault(require("dotenv"));
const http_1 = __importDefault(require("http"));
const cors_1 = __importDefault(require("cors"));
const body_parser_1 = __importDefault(require("body-parser"));
const morgan_1 = __importDefault(require("morgan"));
const i18n_1 = __importDefault(require("./helper/i18n"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
dotenv_1.default.config();
const env = process.env.NEXT_NODE_ENV || "development";
const config_json_1 = __importDefault(require("../shared/config/config.json"));
const db_json_1 = __importDefault(require("../shared/config/db.json"));
global.config = JSON.parse(JSON.stringify(config_json_1.default))[env];
global.db = JSON.parse(JSON.stringify(db_json_1.default))[env];
const index_1 = require("./models/index");
const index_2 = require("./routes/index");
const auth_1 = __importDefault(require("./middleware/auth"));
const validatorMessage_1 = __importDefault(require("./middleware/validatorMessage"));
const swagger_ui_express_1 = __importDefault(require("swagger-ui-express"));
const swagger_config_1 = require("./swagger/swagger.config");
const consumerQueue_1 = require("./rabbitmq/consumerQueue");
const docSecurity_1 = __importDefault(require("./middleware/docSecurity"));
// import defaultDataSeeder from "./services/defaultDataSeeder.service";
index_1.db.sequelize
    .sync({ alter: true })
    .then(() => __awaiter(void 0, void 0, void 0, function* () {
    console.log("✅ Database connected and synchronized");
    // Run default data seeder after database sync
    // try {
    //   await defaultDataSeeder.seedAllDefaults();
    //   console.log("✅ Default data seeded successfully");
    // } catch (error) {
    //   console.error("❌ Error running default data seeder:", error);
    //   // Don't throw error to prevent breaking the server startup
    // }
}))
    .catch((error) => {
    console.error("❌ Database connection failed:", error);
    throw error;
});
const app = (0, express_1.default)();
const router = express_1.default.Router();
app.use((0, morgan_1.default)("combined"));
router.use(body_parser_1.default.urlencoded({ extended: true }));
router.use(body_parser_1.default.json());
// Enable CORS
app.use((0, cors_1.default)("*"));
router.use((0, cookie_parser_1.default)());
router.use(i18n_1.default.init);
// /** Use routers *
router.all("/v1/private/*", auth_1.default);
router.use("/v1/private", index_2.privateRoutes);
router.use("/v1/public", index_2.publicRoutes);
app.use(router);
function getAllRoutes(app) {
    const routes = [];
    function formatRoute(path) {
        const route = path
            // eslint-disable-next-line no-useless-escape
            .replace(/^\^\\\/?\(\?\=\\\/\|\$\)\^/, "") // Remove leading regex patterns
            // eslint-disable-next-line no-useless-escape
            .replace(/\\\/\?\(\?\=\\\/\|\$\)/g, "") // Remove optional trailing slash regex
            .replace(/\^|\$|\\/g, "") // Remove remaining ^, $, and \
            .replace("(?:/([/]+?))/?", "/:id");
        return route;
    }
    function processStack(stack, basePath = "") {
        stack.forEach((layer) => {
            if (layer.route) {
                // If it's a direct route
                Object.keys(layer.route.methods).forEach((method) => {
                    if (["GET", "POST", "PUT", "DELETE"].includes(method.toUpperCase())) {
                        routes.push({
                            method: method.toUpperCase(),
                            path: formatRoute(basePath + layer.regexp.source),
                            params: (layer.route.path.match(/:\w+/g) || []).map((param) => param.replace(":", "")),
                        });
                    }
                });
            }
            else if (layer.name === "router" && layer.handle.stack) {
                // If it's a router, recurse
                processStack(layer.handle.stack, basePath + layer.regexp.source);
            }
        });
    }
    processStack(app._router.stack);
    return routes;
}
app.get("/", (req, res) => {
    const json = {
        message: "Welcome to Recipe Microservice",
        data: getAllRoutes(app),
    };
    return res.send(json);
});
// Handle error message - must be after all routes
app.use(validatorMessage_1.default);
const server = http_1.default.createServer(app);
router.all("/uploads/*", docSecurity_1.default);
router.use("/uploads", express_1.default.static(__dirname + "/uploads"));
/** Swagger setup */
app.use("/api-docs", swagger_ui_express_1.default.serve, swagger_ui_express_1.default.setup(swagger_config_1.swaggerSpec));
// Initialize database and RabbitMQ consumers
const initializeApp = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log("🔄 Initializing RabbitMQ consumers...");
        /** Listen all queue from subscription-ms */
        yield (0, consumerQueue_1.setupConsumers)();
        console.log("✅ RabbitMQ consumers initialized");
        server.listen(global.config.PORT, () => {
            console.log(`🚀 Recipe Microservice running on port ${global.config.PORT}`);
            console.log(`📚 API Documentation: http://localhost:${global.config.PORT}/api-docs`);
            console.log(`🌐 Server URL: http://localhost:${global.config.PORT}`);
            console.log(`🎯 Ready for API testing!`);
        });
    }
    catch (error) {
        console.error("❌ Error during application initialization:", error);
        process.exit(1); // Exit the process if initialization fails
    }
});
// Start application initialization
initializeApp();
exports.default = router;
