"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const celebrate_1 = require("celebrate");
const Recipe_1 = require("../models/Recipe");
const createRecipeValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object()
        .keys({
        recipe_title: celebrate_1.Joi.string().max(100).required(),
        recipe_public_title: celebrate_1.Joi.string().max(100).allow(null, "").optional(),
        recipe_description: celebrate_1.Joi.string().allow(null, "").optional(),
        recipe_preparation_time: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d+$/))
            .allow(null)
            .optional(),
        recipe_cook_time: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d+$/))
            .allow(null)
            .optional(),
        has_recipe_public_visibility: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.boolean(), celebrate_1.Joi.string().valid("true", "false"))
            .default(false),
        has_recipe_private_visibility: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.boolean(), celebrate_1.Joi.string().valid("true", "false"))
            .default(false),
        recipe_status: celebrate_1.Joi.string()
            .valid(...Object.values(Recipe_1.RecipeStatus))
            .default(Recipe_1.RecipeStatus.draft),
        recipe_serve_in: celebrate_1.Joi.string()
            .valid(...Object.values(Recipe_1.RecipeServeIn))
            .allow(null, "")
            .optional(),
        recipe_complexity_level: celebrate_1.Joi.string()
            .valid(...Object.values(Recipe_1.RecipeComplexityLevel))
            .allow(null, "")
            .optional(),
        recipe_garnish: celebrate_1.Joi.string().allow(null, "").optional(),
        recipe_head_chef_tips: celebrate_1.Joi.string().allow(null, "").optional(),
        recipe_foh_tips: celebrate_1.Joi.string().allow(null, "").optional(),
        recipe_impression: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d+$/))
            .allow(null)
            .optional(),
        recipe_yield: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d*\.?\d+$/))
            .allow(null)
            .optional(),
        recipe_yield_unit: celebrate_1.Joi.string()
            .valid(...Object.values(Recipe_1.RecipeYieldUnit))
            .allow(null, "")
            .optional(),
        recipe_total_portions: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d*\.?\d+$/))
            .allow(null)
            .optional(),
        recipe_single_portion_size: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d*\.?\d+$/))
            .allow(null)
            .optional(),
        recipe_serving_method: celebrate_1.Joi.string()
            .valid(...Object.values(Recipe_1.RecipeServingMethod))
            .allow(null, "")
            .optional(),
        recipe_placeholder: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number(), celebrate_1.Joi.string().pattern(/^\d+$/))
            .allow(null)
            .optional(),
        // Vitamin and mineral content fields
        vitamin_a: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d*\.?\d+$/))
            .allow(null)
            .optional(),
        vitamin_c: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d*\.?\d+$/))
            .allow(null)
            .optional(),
        calcium: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d*\.?\d+$/))
            .allow(null)
            .optional(),
        iron: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d*\.?\d+$/))
            .allow(null)
            .optional(),
        // Cooking and preparation method flags
        is_ingredient_cooking_method: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.boolean(), celebrate_1.Joi.string().valid("true", "false"))
            .default(false)
            .optional(),
        is_preparation_method: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.boolean(), celebrate_1.Joi.string().valid("true", "false"))
            .default(false)
            .optional(),
        categories: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.number()).min(1), celebrate_1.Joi.string())
            .optional(),
        // Type-wise attributes (like ingredients)
        nutrition_attributes: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.object({
            id: celebrate_1.Joi.number().required(),
            unit_of_measure: celebrate_1.Joi.string()
                .max(100)
                .allow(null, "")
                .optional(),
            unit: celebrate_1.Joi.number().allow(null).optional(),
            description: celebrate_1.Joi.string().allow(null, "").optional(),
        })), celebrate_1.Joi.string())
            .optional(),
        allergen_attributes: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.object({
            contains: celebrate_1.Joi.array().items(celebrate_1.Joi.number()).optional(),
            may_contain: celebrate_1.Joi.array().items(celebrate_1.Joi.number()).optional(),
        }).custom((value, helpers) => {
            // Validation: ensure no allergen is in both contains and may_contain
            const contains = value.contains || [];
            const mayContain = value.may_contain || [];
            const duplicates = contains.filter((id) => mayContain.includes(id));
            if (duplicates.length > 0) {
                return helpers.error("any.custom", {
                    message: `Allergen IDs ${duplicates.join(", ")} cannot be in both 'contains' and 'may_contain' lists`,
                });
            }
            return value;
        }), celebrate_1.Joi.string())
            .optional(),
        may_contain_allergens: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.number()), celebrate_1.Joi.string())
            .optional(),
        cuisine_attributes: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.number()), celebrate_1.Joi.string())
            .optional(),
        dietary_attributes: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.number()), celebrate_1.Joi.string())
            .optional(),
        haccp_attributes: celebrate_1.Joi.any().optional(),
        // Legacy support for single attributes array
        attributes: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.object({
            id: celebrate_1.Joi.number().required(),
            unit_of_measure: celebrate_1.Joi.string()
                .max(100)
                .allow(null, "")
                .optional(),
            unit: celebrate_1.Joi.number().allow(null).optional(),
            description: celebrate_1.Joi.string().allow(null, "").optional(),
        })), celebrate_1.Joi.string())
            .optional(),
        ingredients: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.object({
            id: celebrate_1.Joi.number().required(),
            quantity: celebrate_1.Joi.number().min(0).allow(null).optional(),
            measure: celebrate_1.Joi.number().allow(null).optional(),
            wastage: celebrate_1.Joi.number().min(0).max(100).allow(null).optional(),
            cost: celebrate_1.Joi.number().min(0).allow(null).optional(),
            cooking_method: celebrate_1.Joi.number().allow(null).optional(),
            preparation_method: celebrate_1.Joi.number().allow(null).optional(),
        })), celebrate_1.Joi.string())
            .optional(),
        steps: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.object({
            order: celebrate_1.Joi.number().min(1).required(),
            description: celebrate_1.Joi.string().allow(null, "").optional(),
            item_id: celebrate_1.Joi.number().allow(null).optional(),
        })), celebrate_1.Joi.string())
            .optional(),
        resources: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.object({
            type: celebrate_1.Joi.string().valid("item", "link").required(),
            item_id: celebrate_1.Joi.string().when("type", {
                is: "item",
                then: celebrate_1.Joi.required(),
                otherwise: celebrate_1.Joi.allow(null),
            }),
            item_link: celebrate_1.Joi.string().when("type", {
                is: "link",
                then: celebrate_1.Joi.required(),
                otherwise: celebrate_1.Joi.allow(null),
            }),
            item_link_type: celebrate_1.Joi.string()
                .valid("image", "video", "pdf", "audio", "youtube", "link")
                .when("type", {
                is: "link",
                then: celebrate_1.Joi.required(),
                otherwise: celebrate_1.Joi.allow(null),
            }),
        })), celebrate_1.Joi.string())
            .optional(),
        // File upload fields (handled by multer)
        recipeFiles: celebrate_1.Joi.any().optional(),
        stepImages: celebrate_1.Joi.any().optional(),
        recipePlaceholder: celebrate_1.Joi.any().optional(),
    })
        .unknown(true)
        .options({ allowUnknown: true, stripUnknown: false }),
});
const updateRecipeValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object()
        .keys({
        recipe_title: celebrate_1.Joi.string().max(100).optional(),
        recipe_public_title: celebrate_1.Joi.string().max(100).allow(null, "").optional(),
        recipe_description: celebrate_1.Joi.string().allow(null, "").optional(),
        recipe_preparation_time: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d+$/))
            .allow(null)
            .optional(),
        recipe_cook_time: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d+$/))
            .allow(null)
            .optional(),
        has_recipe_public_visibility: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.boolean(), celebrate_1.Joi.string().valid("true", "false"))
            .optional()
            .messages({
            "alternatives.match": "has_recipe_public_visibility must be a boolean (true/false) or string ('true'/'false')"
        }),
        has_recipe_private_visibility: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.boolean(), celebrate_1.Joi.string().valid("true", "false"))
            .optional()
            .messages({
            "alternatives.match": "has_recipe_private_visibility must be a boolean (true/false) or string ('true'/'false')"
        }),
        recipe_status: celebrate_1.Joi.string()
            .valid(...Object.values(Recipe_1.RecipeStatus))
            .optional(),
        recipe_serve_in: celebrate_1.Joi.string()
            .valid(...Object.values(Recipe_1.RecipeServeIn))
            .allow(null, "")
            .optional(),
        recipe_complexity_level: celebrate_1.Joi.string()
            .valid(...Object.values(Recipe_1.RecipeComplexityLevel))
            .allow(null, "")
            .optional(),
        recipe_garnish: celebrate_1.Joi.string().allow(null, "").optional(),
        recipe_head_chef_tips: celebrate_1.Joi.string().allow(null, "").optional(),
        recipe_foh_tips: celebrate_1.Joi.string().allow(null, "").optional(),
        recipe_impression: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d+$/))
            .allow(null)
            .optional(),
        recipe_yield: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d*\.?\d+$/))
            .allow(null)
            .optional(),
        recipe_yield_unit: celebrate_1.Joi.string()
            .valid(...Object.values(Recipe_1.RecipeYieldUnit))
            .allow(null, "")
            .optional(),
        recipe_total_portions: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d*\.?\d+$/))
            .allow(null)
            .optional(),
        recipe_single_portion_size: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d*\.?\d+$/))
            .allow(null)
            .optional(),
        recipe_serving_method: celebrate_1.Joi.string()
            .valid(...Object.values(Recipe_1.RecipeServingMethod))
            .allow(null, "")
            .optional(),
        recipe_placeholder: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number(), celebrate_1.Joi.string().pattern(/^\d+$/))
            .allow(null)
            .optional(),
        // Vitamin and mineral content fields
        vitamin_a: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d*\.?\d+$/))
            .allow(null)
            .optional(),
        vitamin_c: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d*\.?\d+$/))
            .allow(null)
            .optional(),
        calcium: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d*\.?\d+$/))
            .allow(null)
            .optional(),
        iron: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().min(0), celebrate_1.Joi.string().pattern(/^\d*\.?\d+$/))
            .allow(null)
            .optional(),
        // Cooking and preparation method flags
        is_ingredient_cooking_method: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.boolean(), celebrate_1.Joi.string().valid("true", "false"))
            .optional()
            .messages({
            "alternatives.match": "is_ingredient_cooking_method must be a boolean (true/false) or string ('true'/'false')"
        }),
        is_preparation_method: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.boolean(), celebrate_1.Joi.string().valid("true", "false"))
            .optional()
            .messages({
            "alternatives.match": "is_preparation_method must be a boolean (true/false) or string ('true'/'false')"
        }),
        categories: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.number()), celebrate_1.Joi.string())
            .optional(),
        // Type-wise attributes (like ingredients)
        nutrition_attributes: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.object({
            id: celebrate_1.Joi.number().required(),
            unit_of_measure: celebrate_1.Joi.string()
                .max(100)
                .allow(null, "")
                .optional(),
            unit: celebrate_1.Joi.number().allow(null).optional(),
            description: celebrate_1.Joi.string().allow(null, "").optional(),
        })), celebrate_1.Joi.string())
            .optional(),
        allergen_attributes: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.object({
            contains: celebrate_1.Joi.array().items(celebrate_1.Joi.number()).optional(),
            may_contain: celebrate_1.Joi.array().items(celebrate_1.Joi.number()).optional(),
        }).custom((value, helpers) => {
            // Validation: ensure no allergen is in both contains and may_contain
            const contains = value.contains || [];
            const mayContain = value.may_contain || [];
            const duplicates = contains.filter((id) => mayContain.includes(id));
            if (duplicates.length > 0) {
                return helpers.error("any.custom", {
                    message: `Allergen IDs ${duplicates.join(", ")} cannot be in both 'contains' and 'may_contain' lists`,
                });
            }
            return value;
        }), celebrate_1.Joi.string())
            .optional(),
        may_contain_allergens: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.number()), celebrate_1.Joi.string())
            .optional(),
        cuisine_attributes: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.number()), celebrate_1.Joi.string())
            .optional(),
        dietary_attributes: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.number()), celebrate_1.Joi.string())
            .optional(),
        haccp_attributes: celebrate_1.Joi.any().optional(),
        // Legacy support for single attributes array
        attributes: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.object({
            id: celebrate_1.Joi.number().required(),
            unit_of_measure: celebrate_1.Joi.string()
                .max(100)
                .allow(null, "")
                .optional(),
            unit: celebrate_1.Joi.number().allow(null).optional(),
            description: celebrate_1.Joi.string().allow(null, "").optional(),
        })), celebrate_1.Joi.string())
            .optional(),
        ingredients: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.object({
            id: celebrate_1.Joi.number().required(),
            quantity: celebrate_1.Joi.number().min(0).allow(null).optional(),
            measure: celebrate_1.Joi.number().allow(null).optional(),
            wastage: celebrate_1.Joi.number().min(0).max(100).allow(null).optional(),
            cost: celebrate_1.Joi.number().min(0).allow(null).optional(),
            cooking_method: celebrate_1.Joi.number().allow(null).optional(),
            preparation_method: celebrate_1.Joi.number().allow(null).optional(),
        })), celebrate_1.Joi.string())
            .optional(),
        steps: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.object({
            order: celebrate_1.Joi.number().min(1).required(),
            description: celebrate_1.Joi.string().allow(null, "").optional(),
            item_id: celebrate_1.Joi.number().allow(null).optional(),
        })), celebrate_1.Joi.string())
            .optional(),
        resources: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.array().items(celebrate_1.Joi.object({
            type: celebrate_1.Joi.string().valid("item", "link").required(),
            item_id: celebrate_1.Joi.string().when("type", {
                is: "item",
                then: celebrate_1.Joi.required(),
                otherwise: celebrate_1.Joi.allow(null),
            }),
            item_link: celebrate_1.Joi.string().when("type", {
                is: "link",
                then: celebrate_1.Joi.required(),
                otherwise: celebrate_1.Joi.allow(null),
            }),
            item_link_type: celebrate_1.Joi.string()
                .valid("image", "video", "pdf", "audio", "youtube", "link")
                .when("type", {
                is: "link",
                then: celebrate_1.Joi.required(),
                otherwise: celebrate_1.Joi.allow(null),
            }),
        })), celebrate_1.Joi.string())
            .optional(),
        // File upload fields (handled by multer)
        recipeFiles: celebrate_1.Joi.any().optional(),
        stepImages: celebrate_1.Joi.any().optional(),
        recipePlaceholder: celebrate_1.Joi.any().optional(),
    })
        .unknown(true)
        .options({ allowUnknown: true, stripUnknown: false }),
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
});
const getRecipeValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().positive(), celebrate_1.Joi.string().min(1))
            .required()
            .messages({
            "alternatives.match": "Recipe identifier must be a positive number (ID) or a non-empty string (slug)",
            "any.required": "Recipe identifier is required",
        }),
    }),
});
const deleteRecipeValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
});
const getRecipesListValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: celebrate_1.Joi.object().keys({
        organization_id: celebrate_1.Joi.string().optional(),
        recipe_status: celebrate_1.Joi.string()
            .valid(...Object.values(Recipe_1.RecipeStatus))
            .optional(),
        recipe_complexity_level: celebrate_1.Joi.string()
            .valid(...Object.values(Recipe_1.RecipeComplexityLevel))
            .optional(),
        category: celebrate_1.Joi.string().optional(),
        categories: celebrate_1.Joi.string().optional(), // For fast API
        attribute: celebrate_1.Joi.string().optional(),
        allergens: celebrate_1.Joi.string().optional(), // For fast API
        exclude_allergen: celebrate_1.Joi.string().optional(), // New filter to exclude allergens
        dietary: celebrate_1.Joi.string().optional(), // For fast API
        cuisine: celebrate_1.Joi.string().optional(), // For fast API
        ingredient: celebrate_1.Joi.string().optional(),
        search: celebrate_1.Joi.string().optional(),
        portion_cost_min: celebrate_1.Joi.number().min(0).optional(), // For fast API
        portion_cost_max: celebrate_1.Joi.number().min(0).optional(), // For fast API
        cooking_time_min: celebrate_1.Joi.number().min(0).optional(), // New filter for minimum cooking time
        cooking_time_max: celebrate_1.Joi.number().min(0).optional(), // New filter for maximum cooking time
        bookmark: celebrate_1.Joi.string().valid("true", "false").optional(), // For fast API
        sort_by: celebrate_1.Joi.string()
            .valid("recipe_title", "alphabetical", "title", "portion_cost", "created_at", "updated_at", "recipe_status", "recipe_preparation_time", "recipe_cook_time", "recipe_complexity_level")
            .optional(),
        sort_order: celebrate_1.Joi.string().valid("ASC", "DESC").optional(),
        page: celebrate_1.Joi.number().min(1).optional(),
        limit: celebrate_1.Joi.number().min(1).max(100).optional(),
        download: celebrate_1.Joi.string().valid("excel", "csv").optional(),
        visibility: celebrate_1.Joi.string().valid("public", "private").optional(),
        // New filter to get recipes by user
        exclude_ingredient: celebrate_1.Joi.string().optional(), // New filter to exclude ingredients
    }),
});
const addBookmarkValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
});
const impressionValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.alternatives()
            .try(celebrate_1.Joi.number().positive(), celebrate_1.Joi.string().min(1))
            .required()
            .messages({
            "alternatives.match": "Recipe identifier must be a positive number (ID) or a non-empty string (slug)",
            "any.required": "Recipe identifier is required",
        }),
    }),
});
const duplicateRecipeValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
});
const getHistoryValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
    [celebrate_1.Segments.QUERY]: celebrate_1.Joi.object().keys({
        page: celebrate_1.Joi.number().min(1).optional(),
        limit: celebrate_1.Joi.number().min(1).max(100).optional(),
        action: celebrate_1.Joi.string().optional(),
        user_id: celebrate_1.Joi.number().optional(),
        filter: celebrate_1.Joi.string().valid('activity', 'history').optional(),
    }),
});
const exportRecipeValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
    [celebrate_1.Segments.QUERY]: celebrate_1.Joi.object().keys({
        format: celebrate_1.Joi.string().valid("excel", "csv", "pdf").optional(),
    }),
});
const publishRecipeValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object().keys({
        has_recipe_public_visibility: celebrate_1.Joi.boolean().optional(),
        has_recipe_private_visibility: celebrate_1.Joi.boolean().optional(),
    }),
});
const makeRecipePublicValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.PARAMS]: celebrate_1.Joi.object().keys({
        id: celebrate_1.Joi.number().required(),
    }),
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object().keys({
        has_recipe_public_visibility: celebrate_1.Joi.boolean().required(),
        has_recipe_private_visibility: celebrate_1.Joi.boolean().optional(),
    }),
});
// Assignment validators
const manageAssignmentsValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.BODY]: celebrate_1.Joi.object().keys({
        recipe_id: celebrate_1.Joi.number().integer().min(1).required(),
        user_ids: celebrate_1.Joi.array()
            .items(celebrate_1.Joi.number().integer().min(1))
            .required()
            .messages({
            "array.base": "user_ids must be an array",
            "any.required": "user_ids is required",
        }),
    }),
});
const getAssignedRecipesValidator = () => (0, celebrate_1.celebrate)({
    [celebrate_1.Segments.QUERY]: celebrate_1.Joi.object().keys({
        page: celebrate_1.Joi.number().integer().min(1).default(1),
        limit: celebrate_1.Joi.number().integer().min(1).max(100).default(20),
        status: celebrate_1.Joi.string().valid("active", "inactive").optional(),
        recipe_status: celebrate_1.Joi.string()
            .valid(...Object.values(Recipe_1.RecipeStatus))
            .optional(),
        search: celebrate_1.Joi.string().max(100).optional(),
        sort_by: celebrate_1.Joi.string()
            .valid("assigned_date", "recipe_title", "created_at")
            .default("assigned_date"),
        sort_order: celebrate_1.Joi.string().valid("asc", "desc").default("desc"),
    }),
});
exports.default = {
    createRecipeValidator,
    updateRecipeValidator,
    getRecipeValidator,
    deleteRecipeValidator,
    getRecipesListValidator,
    addBookmarkValidator,
    impressionValidator,
    duplicateRecipeValidator,
    getHistoryValidator,
    exportRecipeValidator,
    publishRecipeValidator,
    makeRecipePublicValidator,
    manageAssignmentsValidator,
    getAssignedRecipesValidator,
};
